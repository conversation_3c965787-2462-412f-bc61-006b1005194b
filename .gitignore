# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js

# Database
*.db
/public/videos
# testing
/coverage
demo_video
/demo_video
# storybook
storybook-static
*storybook.log
./node_modules/@next/swc-darwin-arm64/next-swc.darwin-arm64.node
./.git/objects/44/aa1b14821ed2bb1e32846f532d5522714a92bd
# playwright
/test-results/
/playwright-report/
/playwright/.cache/

# next.js
/.next
/out

# cache
.swc/

# production
/build

# misc
.DS_Store
*.pem
Thumbs.db

# debug
npm-debug.log*
pnpm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env*.local

# local folder
local

# vercel
.vercel/public/videos

-- Disable <PERSON><PERSON> on all tables to fix authentication issues
ALTER TABLE public.users DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.credit_transactions DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.processed_images DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.processed_videos DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.tasks DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.test_items DISABLE ROW LEVEL SECURITY;

-- Add a comment to explain why <PERSON><PERSON> is disabled
COMMENT ON TABLE public.users IS '<PERSON><PERSON> disabled temporarily to fix authentication issues';

-- Create a tasks table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.tasks (
  id SERIAL PRIMARY KEY,
  name TEXT NOT NULL,
  user_id TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Disable <PERSON><PERSON> on the tasks table
ALTER TABLE public.tasks DISABLE ROW LEVEL SECURITY;

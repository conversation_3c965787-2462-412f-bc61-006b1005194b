-- Professional SaaS Payment System Schema Update
-- This migration adds all necessary fields for a complete payment system

-- Add new columns to users table for subscription management
ALTER TABLE public.users ADD COLUMN IF NOT EXISTS subscription_type TEXT DEFAULT 'Free';
ALTER TABLE public.users ADD COLUMN IF NOT EXISTS subscription_status TEXT DEFAULT 'active';
ALTER TABLE public.users ADD COLUMN IF NOT EXISTS stripe_customer_id TEXT;
ALTER TABLE public.users ADD COLUMN IF NOT EXISTS stripe_subscription_id TEXT;
ALTER TABLE public.users ADD COLUMN IF NOT EXISTS stripe_price_id TEXT;
ALTER TABLE public.users ADD COLUMN IF NOT EXISTS subscription_period_start TIMESTAMP WITH TIME ZONE;
ALTER TABLE public.users ADD COLUMN IF NOT EXISTS subscription_period_end TIMESTAMP WITH TIME ZONE;
ALTER TABLE public.users ADD COLUMN IF NOT EXISTS subscription_cancel_at TIMESTAMP WITH TIME ZONE;
ALTER TABLE public.users ADD COLUMN IF NOT EXISTS subscription_canceled_at TIMESTAMP WITH TIME ZONE;
ALTER TABLE public.users ADD COLUMN IF NOT EXISTS trial_end TIMESTAMP WITH TIME ZONE;
ALTER TABLE public.users ADD COLUMN IF NOT EXISTS credits_expire_at TIMESTAMP WITH TIME ZONE;
ALTER TABLE public.users ADD COLUMN IF NOT EXISTS last_payment_date TIMESTAMP WITH TIME ZONE;
ALTER TABLE public.users ADD COLUMN IF NOT EXISTS next_billing_date TIMESTAMP WITH TIME ZONE;

-- Create subscription_plans table for plan management
CREATE TABLE IF NOT EXISTS public.subscription_plans (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  price_monthly INTEGER NOT NULL, -- Price in cents
  price_yearly INTEGER, -- Price in cents (optional)
  stripe_price_id_monthly TEXT,
  stripe_price_id_yearly TEXT,
  credits_included INTEGER NOT NULL,
  credits_expire_days INTEGER DEFAULT 30, -- Credits expire after X days
  features JSONB DEFAULT '{}',
  is_active BOOLEAN DEFAULT true,
  sort_order INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert default plans
INSERT INTO public.subscription_plans (id, name, description, price_monthly, price_yearly, credits_included, credits_expire_days, features, sort_order) VALUES
('free', 'Free', 'Perfect for getting started', 0, 0, 50, 30, '{"max_uploads_per_month": 10, "max_file_size_mb": 5, "support": "community"}', 1),
('standard', 'Standard', 'Great for regular users', 100, 1000, 700, 30, '{"max_uploads_per_month": 100, "max_file_size_mb": 25, "support": "email", "priority_processing": false}', 2),
('pro', 'Pro', 'Perfect for professionals', 2600, 26000, 999999, 365, '{"max_uploads_per_month": -1, "max_file_size_mb": 100, "support": "priority", "priority_processing": true, "api_access": true}', 3),
('premium', 'Premium', 'For power users and teams', 7800, 78000, 999999, 365, '{"max_uploads_per_month": -1, "max_file_size_mb": 500, "support": "priority", "priority_processing": true, "api_access": true, "team_features": true}', 4)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  description = EXCLUDED.description,
  price_monthly = EXCLUDED.price_monthly,
  price_yearly = EXCLUDED.price_yearly,
  credits_included = EXCLUDED.credits_included,
  features = EXCLUDED.features,
  updated_at = NOW();

-- Create payment_history table for tracking all payments
CREATE TABLE IF NOT EXISTS public.payment_history (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id TEXT NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
  stripe_payment_intent_id TEXT,
  stripe_invoice_id TEXT,
  stripe_subscription_id TEXT,
  amount INTEGER NOT NULL, -- Amount in cents
  currency TEXT DEFAULT 'usd',
  status TEXT NOT NULL, -- succeeded, failed, pending, canceled
  payment_method TEXT, -- card, bank_transfer, etc.
  description TEXT,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create subscription_history table for tracking subscription changes
CREATE TABLE IF NOT EXISTS public.subscription_history (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id TEXT NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
  plan_id TEXT NOT NULL,
  action TEXT NOT NULL, -- created, updated, canceled, reactivated
  old_plan_id TEXT,
  stripe_subscription_id TEXT,
  effective_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Update credit_transactions table with expiration tracking
ALTER TABLE public.credit_transactions ADD COLUMN IF NOT EXISTS expires_at TIMESTAMP WITH TIME ZONE;
ALTER TABLE public.credit_transactions ADD COLUMN IF NOT EXISTS is_expired BOOLEAN DEFAULT false;
ALTER TABLE public.credit_transactions ADD COLUMN IF NOT EXISTS stripe_payment_intent_id TEXT;
ALTER TABLE public.credit_transactions ADD COLUMN IF NOT EXISTS plan_id TEXT;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_users_stripe_customer_id ON public.users(stripe_customer_id);
CREATE INDEX IF NOT EXISTS idx_users_stripe_subscription_id ON public.users(stripe_subscription_id);
CREATE INDEX IF NOT EXISTS idx_users_subscription_status ON public.users(subscription_status);
CREATE INDEX IF NOT EXISTS idx_payment_history_user_id ON public.payment_history(user_id);
CREATE INDEX IF NOT EXISTS idx_payment_history_status ON public.payment_history(status);
CREATE INDEX IF NOT EXISTS idx_subscription_history_user_id ON public.subscription_history(user_id);
CREATE INDEX IF NOT EXISTS idx_credit_transactions_expires_at ON public.credit_transactions(expires_at);
CREATE INDEX IF NOT EXISTS idx_credit_transactions_is_expired ON public.credit_transactions(is_expired);

-- Function to expire credits automatically
CREATE OR REPLACE FUNCTION expire_credits()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Mark expired credits
  UPDATE public.credit_transactions 
  SET is_expired = true 
  WHERE expires_at < NOW() 
    AND is_expired = false 
    AND amount > 0;
    
  -- Update user credits by recalculating non-expired credits
  UPDATE public.users 
  SET credits = (
    SELECT COALESCE(SUM(amount), 0)
    FROM public.credit_transactions 
    WHERE user_id = users.id 
      AND (expires_at IS NULL OR expires_at > NOW())
      AND is_expired = false
  );
END;
$$;

-- Function to add credits with expiration
CREATE OR REPLACE FUNCTION add_credits_with_expiration(
  p_user_id TEXT,
  p_amount INTEGER,
  p_description TEXT,
  p_transaction_type TEXT DEFAULT 'purchase',
  p_expires_days INTEGER DEFAULT 30,
  p_plan_id TEXT DEFAULT NULL
)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_expires_at TIMESTAMP WITH TIME ZONE;
BEGIN
  -- Calculate expiration date
  IF p_expires_days > 0 THEN
    v_expires_at := NOW() + INTERVAL '1 day' * p_expires_days;
  ELSE
    v_expires_at := NULL; -- Never expires
  END IF;
  
  -- Insert credit transaction
  INSERT INTO public.credit_transactions (
    user_id, 
    amount, 
    description, 
    transaction_type, 
    expires_at,
    plan_id,
    created_at
  ) VALUES (
    p_user_id, 
    p_amount, 
    p_description, 
    p_transaction_type, 
    v_expires_at,
    p_plan_id,
    NOW()
  );
  
  -- Update user's total credits (only non-expired)
  UPDATE public.users 
  SET credits = (
    SELECT COALESCE(SUM(amount), 0)
    FROM public.credit_transactions 
    WHERE user_id = p_user_id 
      AND (expires_at IS NULL OR expires_at > NOW())
      AND is_expired = false
  ),
  credits_expire_at = v_expires_at,
  updated_at = NOW()
  WHERE id = p_user_id;
END;
$$;

-- Create a function to handle subscription updates
CREATE OR REPLACE FUNCTION update_user_subscription(
  p_user_id TEXT,
  p_plan_id TEXT,
  p_stripe_customer_id TEXT DEFAULT NULL,
  p_stripe_subscription_id TEXT DEFAULT NULL,
  p_stripe_price_id TEXT DEFAULT NULL,
  p_subscription_status TEXT DEFAULT 'active',
  p_period_start TIMESTAMP WITH TIME ZONE DEFAULT NULL,
  p_period_end TIMESTAMP WITH TIME ZONE DEFAULT NULL
)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_plan RECORD;
  v_old_plan_id TEXT;
  v_credits_expire_days INTEGER;
BEGIN
  -- Get current plan for history
  SELECT subscription_type INTO v_old_plan_id FROM public.users WHERE id = p_user_id;
  
  -- Get plan details
  SELECT * INTO v_plan FROM public.subscription_plans WHERE id = p_plan_id;
  
  IF NOT FOUND THEN
    RAISE EXCEPTION 'Plan not found: %', p_plan_id;
  END IF;
  
  v_credits_expire_days := v_plan.credits_expire_days;
  
  -- Update user subscription
  UPDATE public.users SET
    subscription_type = v_plan.name,
    subscription_status = p_subscription_status,
    stripe_customer_id = COALESCE(p_stripe_customer_id, stripe_customer_id),
    stripe_subscription_id = COALESCE(p_stripe_subscription_id, stripe_subscription_id),
    stripe_price_id = p_stripe_price_id,
    subscription_period_start = p_period_start,
    subscription_period_end = p_period_end,
    last_payment_date = CASE WHEN p_subscription_status = 'active' THEN NOW() ELSE last_payment_date END,
    next_billing_date = p_period_end,
    updated_at = NOW()
  WHERE id = p_user_id;
  
  -- Add credits if it's a paid plan
  IF v_plan.credits_included > 0 THEN
    PERFORM add_credits_with_expiration(
      p_user_id,
      v_plan.credits_included,
      'Subscription: ' || v_plan.name || ' plan',
      'subscription',
      v_credits_expire_days,
      p_plan_id
    );
  END IF;
  
  -- Record subscription history
  INSERT INTO public.subscription_history (
    user_id,
    plan_id,
    action,
    old_plan_id,
    stripe_subscription_id,
    effective_date
  ) VALUES (
    p_user_id,
    p_plan_id,
    CASE WHEN v_old_plan_id IS NULL THEN 'created' ELSE 'updated' END,
    v_old_plan_id,
    p_stripe_subscription_id,
    NOW()
  );
END;
$$;

-- Enable RLS on new tables
ALTER TABLE public.subscription_plans ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.payment_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.subscription_history ENABLE ROW LEVEL SECURITY;

-- RLS policies for subscription_plans (public read)
CREATE POLICY "subscription_plans_select" ON public.subscription_plans FOR SELECT USING (is_active = true);

-- RLS policies for payment_history (users can only see their own)
CREATE POLICY "payment_history_select" ON public.payment_history FOR SELECT USING (auth.jwt() ->> 'sub' = user_id);

-- RLS policies for subscription_history (users can only see their own)
CREATE POLICY "subscription_history_select" ON public.subscription_history FOR SELECT USING (auth.jwt() ->> 'sub' = user_id);

-- Grant necessary permissions
GRANT SELECT ON public.subscription_plans TO authenticated;
GRANT SELECT ON public.payment_history TO authenticated;
GRANT SELECT ON public.subscription_history TO authenticated;

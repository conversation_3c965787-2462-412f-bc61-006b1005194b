-- Disable <PERSON><PERSON> on all tables to fix authentication issues
ALTER TABLE public.users DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.credit_transactions DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.processed_images DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.processed_videos DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.tasks DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.test_items DISABLE ROW LEVEL SECURITY;

-- Add a comment to explain why <PERSON><PERSON> is disabled
COMMENT ON TABLE public.users IS '<PERSON><PERSON> disabled temporarily to fix authentication issues';

-- Update the get_clerk_user_id function to work with the native Supabase integration
CREATE OR REPLACE FUNCTION public.get_clerk_user_id()
RETURNS TEXT
LANGUAGE sql
SECURITY DEFINER
SET search_path = public
AS $$
  -- With the native integration, the user ID should be in the 'sub' claim
  SELECT auth.jwt()->>'sub'
$$;

-- Update the RLS policy for users table
DROP POLICY IF EXISTS "Users can view their own data" ON public.users;
CREATE POLICY "Users can view their own data"
  ON public.users
  FOR SELECT
  TO authenticated
  USING (
    -- This matches the exact pattern from the Clerk documentation
    ((auth.jwt()->>'sub') = (id)::text)
  );

-- Update the update policy
DROP POLICY IF EXISTS "Users can update their own data" ON public.users;
CREATE POLICY "Users can update their own data"
  ON public.users
  FOR UPDATE
  TO authenticated
  USING (
    ((auth.jwt()->>'sub') = (id)::text)
  );

-- Update policies for credit_transactions table
DROP POLICY IF EXISTS "Users can view their own credit transactions" ON public.credit_transactions;
CREATE POLICY "Users can view their own credit transactions"
  ON public.credit_transactions
  FOR SELECT
  TO authenticated
  USING (
    ((auth.jwt()->>'sub') = (user_id)::text)
  );

-- Update policies for processed_images table
DROP POLICY IF EXISTS "Users can view their own processed images" ON public.processed_images;
CREATE POLICY "Users can view their own processed images"
  ON public.processed_images
  FOR SELECT
  TO authenticated
  USING (
    ((auth.jwt()->>'sub') = (user_id)::text)
  );

DROP POLICY IF EXISTS "Users can insert their own processed images" ON public.processed_images;
CREATE POLICY "Users can insert their own processed images"
  ON public.processed_images
  FOR INSERT
  TO authenticated
  WITH CHECK (
    ((auth.jwt()->>'sub') = (user_id)::text)
  );

-- Update policies for processed_videos table
DROP POLICY IF EXISTS "Users can view their own processed videos" ON public.processed_videos;
CREATE POLICY "Users can view their own processed videos"
  ON public.processed_videos
  FOR SELECT
  TO authenticated
  USING (
    ((auth.jwt()->>'sub') = (user_id)::text)
  );

DROP POLICY IF EXISTS "Users can insert their own processed videos" ON public.processed_videos;
CREATE POLICY "Users can insert their own processed videos"
  ON public.processed_videos
  FOR INSERT
  TO authenticated
  WITH CHECK (
    ((auth.jwt()->>'sub') = (user_id)::text)
  );

-- Re-enable RLS on all tables
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.credit_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.processed_images ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.processed_videos ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tasks ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.test_items ENABLE ROW LEVEL SECURITY;

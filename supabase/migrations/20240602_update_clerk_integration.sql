-- Update the get_clerk_user_id function to work with the native Supabase integration
CREATE OR REPLACE FUNCTION public.get_clerk_user_id()
RETURNS TEXT
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  _jwt jsonb := auth.jwt();
  _user_id text;
BEGIN
  -- Try to extract user ID from various locations
  -- With the native integration, the user ID should be in the 'sub' claim
  IF _jwt ->> 'sub' IS NOT NULL THEN
    _user_id := _jwt ->> 'sub';
  -- Fallbacks for backward compatibility
  ELSIF _jwt ->> 'user_id' IS NOT NULL THEN
    _user_id := _jwt ->> 'user_id';
  ELSIF _jwt -> 'user_metadata' ->> 'id' IS NOT NULL THEN
    _user_id := _jwt -> 'user_metadata' ->> 'id';
  ELSE
    _user_id := NULL;
  END IF;
  
  RETURN _user_id;
END;
$$;

-- Create a function to test the JWT token
CREATE OR REPLACE FUNCTION public.test_clerk_jwt()
RETURNS TABLE (
  jwt_payload JSONB,
  auth_role TEXT,
  extracted_user_id TEXT,
  matches_any_user BOOLEAN
)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    auth.jwt()::JSONB AS jwt_payload,
    auth.role() AS auth_role,
    get_clerk_user_id() AS extracted_user_id,
    EXISTS (SELECT 1 FROM users WHERE id = get_clerk_user_id()) AS matches_any_user;
END;
$$;

-- Update the RLS policy for users table to be more permissive
DROP POLICY IF EXISTS "Users can view their own data" ON public.users;
CREATE POLICY "Users can view their own data"
  ON public.users
  FOR SELECT
  TO authenticated
  USING (
    -- Try to match by ID first
    id = get_clerk_user_id() OR
    -- If that fails, allow access if the user is authenticated
    (get_clerk_user_id() IS NULL AND auth.role() = 'authenticated')
  );

-- Update the update policy
DROP POLICY IF EXISTS "Users can update their own data" ON public.users;
CREATE POLICY "Users can update their own data"
  ON public.users
  FOR UPDATE
  TO authenticated
  USING (
    id = get_clerk_user_id() OR
    (get_clerk_user_id() IS NULL AND auth.role() = 'authenticated')
  );

-- Re-enable RLS
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;

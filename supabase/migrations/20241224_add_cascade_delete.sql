-- Migration to add CASCAD<PERSON> DELETE to foreign key constraints
-- This allows automatic deletion of related records when a user is deleted

-- First, drop existing foreign key constraints
ALTER TABLE public.credit_transactions 
DROP CONSTRAINT IF EXISTS credit_transactions_user_id_fkey;

ALTER TABLE public.processed_images 
DROP CONSTRAINT IF EXISTS processed_images_user_id_fkey;

ALTER TABLE public.processed_videos 
DROP CONSTRAINT IF EXISTS processed_videos_user_id_fkey;

ALTER TABLE public.tasks 
DROP CONSTRAINT IF EXISTS tasks_user_id_fkey;

ALTER TABLE public.test_items 
DROP CONSTRAINT IF EXISTS test_items_user_id_fkey;

-- Add new foreign key constraints with CASCADE DELETE
ALTER TABLE public.credit_transactions 
ADD CONSTRAINT credit_transactions_user_id_fkey 
FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;

ALTER TABLE public.processed_images 
ADD CONSTRAINT processed_images_user_id_fkey 
FOREIG<PERSON> KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;

ALTER TABLE public.processed_videos 
ADD CONSTRAINT processed_videos_user_id_fkey 
FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;

ALTER TABLE public.tasks 
ADD CONSTRAINT tasks_user_id_fkey 
FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;

ALTER TABLE public.test_items 
ADD CONSTRAINT test_items_user_id_fkey 
FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;

-- Add comment to document the change
COMMENT ON TABLE public.users IS 'Users table with CASCADE DELETE enabled for all related tables';
COMMENT ON TABLE public.credit_transactions IS 'Credit transactions - automatically deleted when user is deleted';
COMMENT ON TABLE public.processed_images IS 'Processed images - automatically deleted when user is deleted';
COMMENT ON TABLE public.processed_videos IS 'Processed videos - automatically deleted when user is deleted';
COMMENT ON TABLE public.tasks IS 'Tasks - automatically deleted when user is deleted';
COMMENT ON TABLE public.test_items IS 'Test items - automatically deleted when user is deleted';

# Supabase-Clerk Integration

This directory contains the necessary files to set up the integration between Supabase and Clerk authentication.

## Setup Instructions

### 1. Set up <PERSON> as a Supabase third-party auth provider

1. In the Clerk Dashboard, navigate to the Supabase integration setup.
2. Select your configuration options, and then select "Activate Supabase integration". This will reveal the Clerk domain for your Clerk instance.
3. Save the Clerk domain.
4. In the Supabase Dashboard, navigate to Authentication > Sign In / Up.
5. Select "Add provider" and select Clerk from the list of providers.
6. Paste the Clerk domain you copied from the Clerk Dashboard.

### 2. Set up environment variables

Add the following environment variables to your `.env.local` file:

```
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
```

You can find these values in your Supabase dashboard under Project Settings > API.

### 3. Run the SQL setup script

Run the SQL script in `setup.sql` in the Supabase SQL editor to:
- Create helper functions for Clerk user ID and timestamp updates
- Create the necessary tables
- Enable Row Level Security (RLS)
- Set up RLS policies to restrict data access based on the Clerk user ID
- Create triggers for automatic timestamp updates

**Important**: The script addresses the Supabase warnings about function search paths by creating secure functions with explicit search paths.

### 4. Implement the CreateUserInSupabase component

To automatically create users in Supabase when they sign up with Clerk:

1. Update the `src/components/auth/CreateUserInSupabase.tsx` component to call a server action
2. Implement the server action in `src/app/actions.ts` to create users in Supabase
3. Add the component to your auth layout

## How it works

1. When a user signs in with Clerk, Clerk generates a session token.
2. The `CreateUserInSupabase` component (when implemented) creates a user record in Supabase with the same ID as the Clerk user.
3. The Supabase client includes the Clerk token in the Authorization header of requests to Supabase.
4. Supabase verifies the token using the Clerk domain you configured.
5. The RLS policies use the `get_clerk_user_id()` function to restrict data access accordingly.

## Troubleshooting

If you encounter issues:

1. Check that Clerk is properly set up as a Supabase provider
2. Verify your environment variables are correct
3. Ensure the SQL setup script has been run successfully
4. Check the browser console for any errors
5. Verify that your Clerk user has a valid session token

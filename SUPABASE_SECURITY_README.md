# Supabase Security Configuration Guide

This document provides a comprehensive guide to the security configuration of our Supabase database, focusing on Row Level Security (RLS) policies and how they integrate with Clerk authentication.

## Table of Contents

1. [Overview](#overview)
2. [Current RLS Configuration](#current-rls-configuration)
3. [How to Test Security](#how-to-test-security)
4. [Troubleshooting](#troubleshooting)
5. [Security Best Practices](#security-best-practices)
6. [SQL Scripts](#sql-scripts)

## Overview

Our application uses Supabase as the database and Clerk for authentication. To ensure data isolation between users, we've implemented Row Level Security (RLS) policies in Supabase that restrict access based on the user's identity from the Clerk JWT token.

### Key Security Components

- **Clerk Authentication**: Provides user identity and JWT tokens
- **Supabase RLS**: Enforces data access restrictions at the database level
- **JWT Integration**: Uses the `sub` claim from Clerk's JWT to identify users

## Current RLS Configuration

All tables have RLS enabled with policies that restrict access based on user identity. Here's a summary of our current policies:

### Users Table

```sql
-- Service role can do anything (admin access)
CREATE POLICY "Service role can do anything"
  ON public.users
  FOR ALL
  TO service_role
  USING (true)
  WITH CHECK (true);

-- Users can only view their own data
CREATE POLICY "Users can view their own data"
  ON public.users
  FOR SELECT
  TO authenticated
  USING (id = (auth.jwt() ->> 'sub'::text));

-- Users can only update their own data
CREATE POLICY "Users can update their own data"
  ON public.users
  FOR UPDATE
  TO authenticated
  USING (id = (auth.jwt() ->> 'sub'::text));
```

### Data Tables (credit_transactions, processed_images, processed_videos, tasks)

All data tables follow this pattern:

```sql
-- Service role can do anything (admin access)
CREATE POLICY "Service role can do anything"
  ON public.[table_name]
  FOR ALL
  TO service_role
  USING (true)
  WITH CHECK (true);

-- Users can only view their own data
CREATE POLICY "Users can view their own data"
  ON public.[table_name]
  FOR SELECT
  TO authenticated
  USING (user_id = (auth.jwt() ->> 'sub'::text));

-- Users can only insert their own data
CREATE POLICY "Users can insert their own data"
  ON public.[table_name]
  FOR INSERT
  TO authenticated
  WITH CHECK (user_id = (auth.jwt() ->> 'sub'::text));
```

### Test Items Table

The test_items table uses a slightly different approach with a custom function:

```sql
-- Users can only view their own test items
CREATE POLICY "Users can view their own test items"
  ON public.test_items
  FOR SELECT
  TO authenticated
  USING ((user_id = get_clerk_user_id()) OR (get_clerk_user_id() = ''::text));
```

## How to Test Security

### 1. SQL Testing (Server-Side)

Run this SQL query to check your RLS policies:

```sql
SELECT
  schemaname,
  tablename,
  policyname,
  permissive,
  roles,
  cmd,
  qual,
  with_check
FROM
  pg_policies
WHERE
  schemaname = 'public'
ORDER BY
  tablename, policyname;
```

Verify that the `qual` and `with_check` columns contain `id = (auth.jwt() ->> 'sub'::text)` or `user_id = (auth.jwt() ->> 'sub'::text)` instead of just `true`.

### 2. Multi-User Testing (Most Important)

This is the most thorough way to test your RLS policies:

1. **Create Two Test Users**:
   - Sign up for two different accounts in your application (User A and User B)

2. **Create Test Data with User A**:
   - Log in as User A
   - Create some data (e.g., a task, an image, etc.)
   - Note the ID or name of the created item

3. **Try to Access User A's Data with User B**:
   - Log out and log in as User B
   - Try to access User A's data
   - If RLS is working correctly, User B should not be able to see User A's data

### 3. Client-Side Testing with Security Test Page

Use the security test page at `/security-test` to test your RLS policies:

1. Log in to your application
2. Navigate to `/security-test`
3. Run the client-side tests by clicking "Run RLS Tests"
4. Run the server-side tests by clicking "Run Server Tests"

If your RLS policies are working correctly:
- You should be able to access your own data
- You should not be able to access other users' data
- The "filtered" check should show that only your data is returned when querying all items

### 4. API Testing

You can also test your RLS policies using API requests:

```javascript
// Test accessing your own data (should succeed)
const { data: myData, error: myError } = await supabase
  .from('tasks')
  .select('*')
  .eq('user_id', user.id);
console.log('My data:', myData, 'Error:', myError);

// Test accessing all data (should only return your data)
const { data: allData, error: allError } = await supabase
  .from('tasks')
  .select('*');
console.log('All data:', allData, 'Error:', allError);

// Test accessing another user's data (should fail or return empty)
const fakeUserId = 'user_' + Math.random().toString(36).substring(2, 15);
const { data: otherData, error: otherError } = await supabase
  .from('tasks')
  .select('*')
  .eq('user_id', fakeUserId);
console.log('Other user data:', otherData, 'Error:', otherError);
```

### 5. SQL Function for Testing

You can create a SQL function to test your RLS policies from the database side:

```sql
CREATE OR REPLACE FUNCTION test_rls_security()
RETURNS TABLE (
  test_name TEXT,
  status TEXT,
  details TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  _current_user_id TEXT := auth.jwt()->>'sub';
  _current_role TEXT := auth.role();
  _jwt_claims JSONB := auth.jwt();
  _rls_enabled BOOLEAN;
  _policy_count INTEGER;
  _table_record RECORD;
  _table_oid OID;
BEGIN
  -- Test 1: Check if the current user is authenticated
  IF _current_user_id IS NULL THEN
    RETURN QUERY SELECT 
      'Authentication Check'::TEXT, 
      'WARNING'::TEXT, 
      'No authenticated user detected. Some tests may fail.'::TEXT;
  ELSE
    RETURN QUERY SELECT 
      'Authentication Check'::TEXT, 
      'PASS'::TEXT, 
      'Authenticated as user ID: ' || _current_user_id::TEXT;
  END IF;
  
  -- Additional tests...
END;
$$;

-- Run the test
SELECT * FROM test_rls_security();
```

## Troubleshooting

### Common Issues

1. **No Authentication Detected**:
   - This is normal when running SQL directly in the SQL editor
   - Make sure your application is correctly passing the Clerk JWT token to Supabase

2. **Empty Results Instead of Errors**:
   - With RLS, you typically get empty results rather than errors when trying to access unauthorized data
   - This is expected behavior

3. **Service Role Bypass**:
   - The service role can bypass RLS
   - Make sure you're not using the service role for normal user operations

### Debugging Tips

1. **Check JWT Token**:
   ```sql
   SELECT auth.jwt();
   ```
   This should return a JSON object with a `sub` claim containing the user ID.

2. **Test get_clerk_user_id Function**:
   ```sql
   SELECT get_clerk_user_id();
   ```
   This should return the same user ID as the `sub` claim in the JWT.

3. **Check RLS Status**:
   ```sql
   SELECT
     c.relname as table_name,
     c.relrowsecurity as rls_enabled
   FROM
     pg_class c
   JOIN
     pg_namespace n ON c.relnamespace = n.oid
   WHERE
     n.nspname = 'public'
     AND c.relkind = 'r'
   ORDER BY
     c.relname;
   ```
   All tables should have `rls_enabled` set to `true`.

## Security Best Practices

1. **Always Use RLS**:
   - Never disable RLS in production
   - Use RLS policies to restrict access based on user identity

2. **Avoid Using Service Role**:
   - The service role bypasses RLS
   - Only use it for admin operations or migrations

3. **Regular Security Audits**:
   - Regularly review your RLS policies
   - Test with multiple users to ensure data isolation

4. **JWT Integration**:
   - Make sure your application is correctly passing the Clerk JWT token to Supabase
   - Use `auth.jwt()->>'sub'` to get the user ID in RLS policies

5. **Consistent Policy Implementation**:
   - Use the same pattern across all tables
   - Be explicit about which operations are allowed (SELECT, INSERT, UPDATE, DELETE)

## SQL Scripts

### Enable RLS on All Tables

```sql
-- Enable RLS on all tables
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.credit_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.processed_images ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.processed_videos ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tasks ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.test_items ENABLE ROW LEVEL SECURITY;
```

### Fix Insecure RLS Policies

```sql
-- Fix users table policies
DROP POLICY IF EXISTS "Users can view their own data" ON public.users;
CREATE POLICY "Users can view their own data"
  ON public.users
  FOR SELECT
  TO authenticated
  USING (id = auth.jwt()->>'sub');

-- Additional policy fixes...
```

### Test RLS Policies

```sql
-- Create a test function to check RLS and security
CREATE OR REPLACE FUNCTION test_rls_security()
RETURNS TABLE (
  test_name TEXT,
  status TEXT,
  details TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
-- Function body...
$$;

-- Run the test
SELECT * FROM test_rls_security();
```

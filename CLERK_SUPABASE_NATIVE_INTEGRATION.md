# Clerk-Supabase Native Integration Guide

## Overview

This document explains how our application integrates Clerk authentication with Supabase using the native Supabase integration. This is the recommended approach as of April 2023, as JWT templates will be deprecated by April 2025.

## Setup Steps

### 1. Set up Clerk as a Supabase Third-Party Auth Provider

1. In the Clerk Dashboard, navigate to the Supabase integration setup.
2. Select your configuration options, and then select "Activate Supabase integration".
3. Save the Clerk domain.
4. In the Supabase Dashboard, navigate to Authentication > Sign In / Up.
5. Select "Add provider" and select Clerk from the list of providers.
6. Paste the Clerk domain you copied from the Clerk Dashboard.

### 2. Set up RLS Policies

The RLS policies use the `get_clerk_user_id()` function to extract the Clerk user ID from the JWT token:

```sql
CREATE OR REPLACE FUNCTION public.get_clerk_user_id()
RETURNS TEXT
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  _jwt jsonb := auth.jwt();
  _user_id text;
BEGIN
  -- Try to extract user ID from various locations
  IF _jwt ->> 'sub' IS NOT NULL THEN
    _user_id := _jwt ->> 'sub';
  ELSIF _jwt ->> 'user_id' IS NOT NULL THEN
    _user_id := _jwt ->> 'user_id';
  ELSE
    _user_id := NULL;
  END IF;
  
  RETURN _user_id;
END;
$$;
```

The RLS policies restrict access to data based on the user ID:

```sql
CREATE POLICY "Users can view their own data"
  ON public.users
  FOR SELECT
  TO authenticated
  USING (
    id = get_clerk_user_id() OR
    (get_clerk_user_id() IS NULL AND auth.role() = 'authenticated')
  );
```

### 3. Client-Side Integration

The client-side integration uses the `accessToken` option to provide the Clerk session token to Supabase:

```typescript
function useSupabaseClient() {
  const { session } = useSession();

  const supabaseClient = useMemo(() => {
    return createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        async accessToken() {
          return session?.getToken() ?? null;
        },
      }
    );
  }, [session]);

  return supabaseClient;
}
```

### 4. Server-Side Integration

The server-side integration also uses the Clerk session token:

```typescript
function createServerSupabaseClient() {
  const { getToken } = auth();

  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      auth: {
        persistSession: false,
      },
      global: {
        headers: {
          Authorization: `Bearer ${getToken() || ''}`,
        },
      },
    }
  );
}
```

## Troubleshooting

### JWT Token Issues

If you're having issues with the JWT token, you can use the `test_clerk_jwt()` function to see what's in the token:

```sql
SELECT * FROM test_clerk_jwt();
```

This will show you:
- The JWT payload
- The auth role
- The extracted user ID
- Whether the user ID matches any user in the database

### Common Issues

1. **"refresh_token_not_found" error**: This can happen if the Clerk session is not properly initialized. Make sure you're signed in and that the Clerk session is available.

2. **RLS policies not working**: Make sure the `get_clerk_user_id()` function is correctly extracting the user ID from the JWT token. You can test this with the `test_clerk_jwt()` function.

3. **"Invalid JWT" error**: This can happen if the Clerk domain is not correctly set up in Supabase. Make sure the Clerk domain is correctly set up in the Supabase Dashboard.

## References

- [Clerk Supabase Integration Documentation](https://clerk.com/docs/integrations/databases/supabase)
- [Supabase Third-Party Auth Providers Documentation](https://supabase.com/docs/guides/auth/third-party-auth)

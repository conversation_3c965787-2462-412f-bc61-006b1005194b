/* eslint-disable jsonc/sort-keys */
{
  "compilerOptions": {
    "lib": ["dom", "dom.iterable", "esnext"],
    "module": "esnext",
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "removeComments": true,
    "preserveConstEnums": true,
    "strict": true,
    "alwaysStrict": true,
    "strictNullChecks": true,
    "noUncheckedIndexedAccess": true,

    "noImplicitAny": false,
    "noImplicitReturns": false,
    "noImplicitThis": false,
    "noUnusedLocals": false,
    "noUnusedParameters": false,
    "allowUnreachableCode": true,
    "noFallthroughCasesInSwitch": false,

    "target": "es2017",
    "outDir": "out",
    "sourceMap": true,

    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "allowJs": true,
    "checkJs": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,

    "jsx": "preserve",
    "noEmit": true,
    "isolatedModules": true,
    "incremental": true,

    // Load types
    "types": ["vitest/globals"],

    // Path aliases
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"],
      "@/public/*": ["./public/*"]
    },

    // Editor support
    "plugins": [
      {
        "name": "next"
      }
    ]
  },
  "exclude": [
    "./out/**/*",
    "./node_modules/**/*",
    "**/*.spec.ts",
    "**/*.e2e.ts"
  ],
  "include": [
    "next-env.d.ts",
    "**/*.ts",
    "**/*.tsx",
    ".storybook/*.ts",
    ".next/types/**/*.ts",
    "**/*.mts"
  ]
}

import { clerkMiddleware, createRouteMatcher } from '@clerk/nextjs/server';
import createMiddleware from 'next-intl/middleware';

import { AllLocales, AppConfig } from './utils/AppConfig';

const intlMiddleware = createMiddleware({
  locales: AllLocales,
  localePrefix: AppConfig.localePrefix,
  defaultLocale: AppConfig.defaultLocale,
});

// Define public routes that don't require authentication
const isPublicRoute = createRouteMatcher([
  '/',
  '/pricing',
  '/terms-of-service',
  '/privacy-policy',
  '/api/webhooks/(.*)',
  '/api/stripe/webhook',
  '/api/clerk/webhook',
]);

const isProtectedRoute = createRouteMatcher([
  '/dashboard(.*)',
  '/:locale/dashboard(.*)',
]);

export default clerkMiddleware(async (auth, req) => {
  // Skip middleware entirely for API routes
  if (req.nextUrl.pathname.startsWith('/api/')) {
    return;
  }

  // Skip authentication for public routes
  if (isPublicRoute(req)) {
    return intlMiddleware(req);
  }

  // Protect dashboard routes
  if (isProtectedRoute(req)) {
    const { userId } = await auth();

    if (!userId) {
      // Extract locale from path for redirect
      const locale = req.nextUrl.pathname.match(/^\/([^\/]+)/)?.at(1) ?? '';
      const signInUrl = locale && locale !== 'dashboard'
        ? `/${locale}/sign-in`
        : '/sign-in';

      return Response.redirect(new URL(signInUrl, req.url));
    }
  }

  return intlMiddleware(req);
});

export const config = {
  matcher: ['/((?!.+\\.[\\w]+$|_next|monitoring).*)', '/', '/(api|trpc)(.*)'], // Also exclude tunnelRoute used in Sentry from the matcher
};

{"Index": {"meta_title": "SaaS Template - The perfect SaaS template to build and scale your business with ease.", "meta_description": "A free and open-source landing page template for your SaaS business, built with React, TypeScript, Shadcn UI, and Tailwind CSS."}, "Navbar": {"sign_in": "Sign In", "sign_up": "Sign Up", "product": "Product", "docs": "Docs", "blog": "Blog", "community": "Community", "company": "Company"}, "Hero": {"follow_twitter": "Follow @Ixartz on Twitter", "title": "The perfect <important>SaaS template</important> to build and scale your business with ease.", "description": "A free and open-source landing page template for your SaaS business, built with React, TypeScript, Shadcn UI, and Tailwind CSS.", "primary_button": "Get Started", "secondary_button": "Star on GitHub"}, "Sponsors": {"title": "Sponsored by"}, "Features": {"section_subtitle": "Features", "section_title": "Unlock the Full Potential of the SaaS Template", "section_description": "A free and open-source landing page template for your SaaS business, built with React, TypeScript, Shadcn UI, and Tailwind CSS.", "feature1_title": "Node.js", "feature2_title": "React", "feature3_title": "Tailwind CSS", "feature4_title": "TypeScript", "feature5_title": "Shadcn UI", "feature6_title": "ESLint", "feature_description": "A free and open-source landing page template for your SaaS business, built with React, TypeScript, Shadcn UI, and Tailwind CSS."}, "Pricing": {"section_subtitle": "Features", "section_title": "Unlock the Full Potential of the SaaS Template", "section_description": "A free and open-source landing page template for your SaaS business, built with React, TypeScript, Shadcn UI, and Tailwind CSS.", "button_text": "Get Started"}, "PricingPlan": {"free_plan_name": "Free", "premium_plan_name": "Premium", "enterprise_plan_name": "Enterprise", "free_plan_description": "For individuals", "premium_plan_description": "For small teams", "enterprise_plan_description": "For industry leaders", "feature_team_member": "{number} Team Members", "feature_website": "{number} Websites", "feature_storage": "{number} GB Storage", "feature_transfer": "{number} TB Transfer", "feature_email_support": "Email Support", "plan_interval_month": "month", "plan_interval_year": "year", "next_renew_date": "Your subscription renews on {date}"}, "FAQ": {"question": "What features are available to users on the Free Plan?", "answer": "The Free Plan offers basic AI anonymization features, including blur, pixelation, and basic editor tools. You'll receive a one-time offer of 5 credits to try our service, and you can preview the blur effect before applying it."}, "CTA": {"title": "Ready to get started?", "description": "Join thousands of users who trust our service for their privacy needs.", "button_text": "Start for free"}, "Footer": {"product": "Product", "docs": "Docs", "blog": "Blog", "community": "Community", "company": "Company", "terms_of_service": "Terms Of Service", "privacy_policy": "Privacy Policy", "designed_by": "Designed by <author></author>."}, "ProtectFallback": {"not_enough_permission": "You do not have the permissions to perform this action"}, "SignIn": {"meta_title": "Sign in", "meta_description": "Seamlessly sign in to your account with our user-friendly login process."}, "SignUp": {"meta_title": "Sign up", "meta_description": "Effortlessly create an account through our intuitive sign-up process."}, "DashboardLayout": {"home": "Home", "todos": "Todos", "members": "Members", "billing": "Billing", "settings": "Settings"}, "Dashboard": {"meta_title": "SaaS Template Dashboard", "meta_description": "A free and open-source landing page template for your SaaS business, built with Next.js, TypeScript, Shadcn UI, and Tailwind CSS."}, "DashboardIndex": {"title_bar": "Dashboard", "title_bar_description": "Welcome to your dashboard", "message_state_title": "Let's get started", "message_state_description": "You can customize this page by editing the file at <code>dashboard/page.tsx</code>", "message_state_button": "Star on GitHub", "message_state_alternative": "Want more features using the same stack? Try <url></url>."}, "UserProfile": {"title_bar": "User Profile", "title_bar_description": "View and manage your user profile"}, "OrganizationProfile": {"title_bar": "Organization Management", "title_bar_description": "Manage your organization"}, "Billing": {"title_bar": "Billing", "title_bar_description": "Manage your billing and subscription", "current_section_title": "Current Plan", "current_section_description": "Adjust your payment plan to best suit your requirements", "manage_subscription_button": "Manage Subscription"}, "BillingOptions": {"current_plan": "Current Plan", "upgrade_plan": "Get Started"}, "CheckoutConfirmation": {"title_bar": "Payment Confirmation", "message_state_title": "Payment successful", "message_state_description": "Your payment has been successfully processed. Thank you for your purchase!", "message_state_button": "Go back to Billing"}, "DataTable": {"no_results": "No results."}, "Todos": {"title_bar": "Todo List", "title_bar_description": "View and manage your todo list", "add_todo_button": "New todo"}, "TodoTableColumns": {"open_menu": "Open menu", "edit": "Edit", "delete": "Delete", "title_header": "Title", "message_header": "Message", "created_at_header": "Created at"}, "AddTodo": {"title_bar": "Add <PERSON>", "add_todo_section_title": "Create a new todo", "add_todo_section_description": "Fill in the form below to create a new todo"}, "EditTodo": {"title_bar": "Edit todo", "edit_todo_section_title": "Modify todo", "edit_todo_section_description": "Fill in the form below to edit the todo"}, "TodoForm": {"title_label": "Title", "title_description": "Enter a descriptive title for your todo.", "message_title": "Message", "message_description": "Enter a detailed message for your todo.", "submit_button": "Submit"}}
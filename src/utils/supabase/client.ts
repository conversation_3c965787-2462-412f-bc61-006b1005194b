// Client-side Supabase client that uses Clerk's session token
import { createClient as createSupabaseClient } from '@supabase/supabase-js';
import { useSession } from '@clerk/nextjs';
import { useMemo } from 'react';

// Simple client for non-authenticated requests
export function createClient() {
  return createSupabaseClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  );
}

// This function creates a Supabase client that includes the Clerk session token
// using the official Clerk-Supabase integration approach
export function useSupabaseClient() {
  const { session } = useSession();

  // Create a memoized Supabase client that uses the Clerk token
  const supabaseClient = useMemo(() => {
    return createSupabaseClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        // Use the accessToken option for the official Clerk-Supabase integration
        async accessToken() {
          return session?.getToken() ?? null;
        },
      }
    );
  }, [session]);

  return supabaseClient;
}

// Alternative function that follows the exact pattern from the Clerk documentation
export function createClerkSupabaseClient() {
  const { session } = useSession();

  return createSupabaseClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      async accessToken() {
        return session?.getToken() ?? null;
      },
    }
  );
}

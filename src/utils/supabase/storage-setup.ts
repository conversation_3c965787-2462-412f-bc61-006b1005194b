import { createServiceRoleSupabaseClient } from './server';

/**
 * Sets up the required storage buckets in Supabase
 */
export async function setupStorageBuckets() {
  try {
    const supabase = createServiceRoleSupabaseClient();
    
    // Check if original-uploads bucket exists
    const { data: originalBucket, error: originalError } = await supabase
      .storage
      .getBucket('original-uploads');
    
    // Create original-uploads bucket if it doesn't exist
    if (!originalBucket) {
      console.log('Creating original-uploads bucket...');
      const { error } = await supabase
        .storage
        .createBucket('original-uploads', {
          public: false,
          fileSizeLimit: 52428800, // 50MB
          allowedMimeTypes: ['image/jpeg', 'image/png', 'image/gif', 'video/mp4', 'video/quicktime']
        });
      
      if (error) {
        console.error('Error creating original-uploads bucket:', error);
      } else {
        console.log('Successfully created original-uploads bucket');
      }
    } else {
      console.log('original-uploads bucket already exists');
    }
    
    // Check if processed-uploads bucket exists
    const { data: processedBucket, error: processedError } = await supabase
      .storage
      .getBucket('processed-uploads');
    
    // Create processed-uploads bucket if it doesn't exist
    if (!processedBucket) {
      console.log('Creating processed-uploads bucket...');
      const { error } = await supabase
        .storage
        .createBucket('processed-uploads', {
          public: true, // Processed files can be public for easy access
          fileSizeLimit: 104857600, // 100MB
          allowedMimeTypes: ['image/jpeg', 'image/png', 'image/gif', 'video/mp4', 'video/quicktime']
        });
      
      if (error) {
        console.error('Error creating processed-uploads bucket:', error);
      } else {
        console.log('Successfully created processed-uploads bucket');
      }
    } else {
      console.log('processed-uploads bucket already exists');
    }
    
    // Set up RLS policies for the buckets
    await setupStoragePolicies();
    
    return { success: true };
  } catch (error: any) {
    console.error('Error setting up storage buckets:', error);
    return { success: false, error: error.message };
  }
}

/**
 * Sets up Row Level Security policies for storage buckets
 */
async function setupStoragePolicies() {
  try {
    const supabase = createServiceRoleSupabaseClient();
    
    // Create policy for original-uploads bucket
    // Users can only access their own files
    const { error: originalPolicyError } = await supabase
      .storage
      .from('original-uploads')
      .createPolicy('User can access their own files', {
        name: 'User can access their own files',
        definition: {
          match_owner: true
        }
      });
    
    if (originalPolicyError) {
      console.error('Error creating policy for original-uploads:', originalPolicyError);
    }
    
    // Create policy for processed-uploads bucket
    // Files are public but only owners can modify them
    const { error: processedPolicyError } = await supabase
      .storage
      .from('processed-uploads')
      .createPolicy('Files are public but only owners can modify', {
        name: 'Files are public but only owners can modify',
        definition: {
          read_access: true,
          match_owner_on_write: true
        }
      });
    
    if (processedPolicyError) {
      console.error('Error creating policy for processed-uploads:', processedPolicyError);
    }
    
    return { success: true };
  } catch (error: any) {
    console.error('Error setting up storage policies:', error);
    return { success: false, error: error.message };
  }
}

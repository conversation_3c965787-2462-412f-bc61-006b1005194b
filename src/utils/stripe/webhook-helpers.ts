import Stripe from 'stripe';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2024-06-20',
});

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

export async function getUserByCustomerId(supabase: any, customerId: string) {
  try {
    // First try to find user by stripe_customer_id
    const { data: user, error } = await supabase
      .from('users')
      .select('*')
      .eq('stripe_customer_id', customerId)
      .single();

    if (!error && user) {
      return user;
    }

    // If not found, try to find by email
    const customer = await stripe.customers.retrieve(customerId);
    if (!customer || customer.deleted || !customer.email) {
      return null;
    }

    const { data: userByEmail, error: emailError } = await supabase
      .from('users')
      .select('*')
      .eq('email', customer.email)
      .single();

    if (!emailError && userByEmail) {
      // Update the user with the stripe_customer_id for future lookups
      await supabase
        .from('users')
        .update({ stripe_customer_id: customerId })
        .eq('id', userByEmail.id);

      return userByEmail;
    }

    return null;
  } catch (error) {
    console.error('Error finding user by customer ID:', error);
    return null;
  }
}

export async function recordPayment(
  supabase: any,
  userId: string,
  paymentIntent: Stripe.PaymentIntent,
  status: string
) {
  try {
    await supabase
      .from('payment_history')
      .insert({
        user_id: userId,
        stripe_payment_intent_id: paymentIntent.id,
        amount: paymentIntent.amount,
        currency: paymentIntent.currency,
        status: status,
        payment_method: paymentIntent.payment_method_types?.[0] || 'unknown',
        description: `Payment ${status}: $${(paymentIntent.amount / 100).toFixed(2)}`,
        metadata: {
          payment_method_id: paymentIntent.payment_method,
          client_secret: paymentIntent.client_secret,
        },
      });

    console.log(`✅ Payment recorded: ${paymentIntent.id} - ${status}`);
  } catch (error) {
    console.error('❌ Error recording payment:', error);
    throw error;
  }
}

export async function processSubscription(
  supabase: any,
  userId: string,
  subscription: Stripe.Subscription
) {
  try {
    const priceId = subscription.items.data[0]?.price.id;
    const planId = getPlanIdFromPriceId(priceId);

    if (!planId) {
      console.error('❌ Unknown price ID:', priceId);
      return;
    }

    console.log(`🔄 Processing subscription for user ${userId}: ${planId} plan`);

    // Try using the database function first
    try {
      const { error } = await supabase.rpc('update_user_subscription', {
        p_user_id: userId,
        p_plan_id: planId,
        p_stripe_customer_id: subscription.customer,
        p_stripe_subscription_id: subscription.id,
        p_stripe_price_id: priceId,
        p_subscription_status: subscription.status,
        p_period_start: new Date(subscription.current_period_start * 1000).toISOString(),
        p_period_end: new Date(subscription.current_period_end * 1000).toISOString(),
      });

      if (error) {
        throw error;
      }

      console.log(`✅ Subscription processed using database function for user ${userId}: ${planId} plan`);
    } catch (dbError: any) {
      console.error('❌ Database function failed, using manual approach:', dbError);

      // Fallback: Update subscription manually
      await processSubscriptionManually(supabase, userId, subscription, planId, priceId);
    }
  } catch (error) {
    console.error('❌ Error processing subscription:', error);
    throw error;
  }
}

async function processSubscriptionManually(
  supabase: any,
  userId: string,
  subscription: Stripe.Subscription,
  planId: string,
  priceId: string
) {
  console.log(`🔧 Processing subscription manually for user ${userId}`);

  // Get plan details
  const { data: plan, error: planError } = await supabase
    .from('subscription_plans')
    .select('*')
    .eq('id', planId)
    .single();

  if (planError || !plan) {
    console.error('❌ Plan not found:', planId);
    throw new Error(`Plan not found: ${planId}`);
  }

  // Update user subscription details
  const { error: userUpdateError } = await supabase
    .from('users')
    .update({
      subscription_type: plan.name,
      subscription_status: subscription.status,
      stripe_customer_id: subscription.customer,
      stripe_subscription_id: subscription.id,
      stripe_price_id: priceId,
      subscription_period_start: new Date(subscription.current_period_start * 1000).toISOString(),
      subscription_period_end: new Date(subscription.current_period_end * 1000).toISOString(),
      last_payment_date: subscription.status === 'active' ? new Date().toISOString() : null,
      next_billing_date: new Date(subscription.current_period_end * 1000).toISOString(),
      updated_at: new Date().toISOString(),
    })
    .eq('id', userId);

  if (userUpdateError) {
    console.error('❌ Error updating user:', userUpdateError);
    throw userUpdateError;
  }

  // Add credits if it's a paid plan
  if (plan.credits_included > 0) {
    const expiresAt = plan.credits_expire_days > 0
      ? new Date(Date.now() + plan.credits_expire_days * 24 * 60 * 60 * 1000).toISOString()
      : null;

    // Insert credit transaction
    const { error: creditError } = await supabase
      .from('credit_transactions')
      .insert({
        user_id: userId,
        amount: plan.credits_included,
        description: `Subscription: ${plan.name} plan`,
        transaction_type: 'subscription',
        expires_at: expiresAt,
        plan_id: planId,
      });

    if (creditError) {
      console.error('❌ Error adding credits:', creditError);
      throw creditError;
    }

    // Update user's total credits
    const { data: totalCredits, error: totalError } = await supabase
      .from('credit_transactions')
      .select('amount')
      .eq('user_id', userId)
      .eq('is_expired', false);

    if (!totalError) {
      const newTotal = totalCredits?.reduce((sum, t) => sum + t.amount, 0) || 0;

      await supabase
        .from('users')
        .update({
          credits: newTotal,
          credits_expire_at: expiresAt,
        })
        .eq('id', userId);
    }

    console.log(`✅ Added ${plan.credits_included} credits to user ${userId}`);
  }

  // Record subscription history
  const { error: historyError } = await supabase
    .from('subscription_history')
    .insert({
      user_id: userId,
      plan_id: planId,
      action: 'created',
      stripe_subscription_id: subscription.id,
      effective_date: new Date().toISOString(),
    });

  if (historyError) {
    console.error('❌ Error recording subscription history:', historyError);
    // Don't throw here as it's not critical
  }

  console.log(`✅ Subscription processed manually for user ${userId}: ${planId} plan`);
}

export function getPlanIdFromPriceId(priceId: string): string | null {
  // Map your Stripe price IDs to plan IDs
  // UPDATE THESE WITH YOUR ACTUAL STRIPE PRICE IDs
  const priceMapping: Record<string, string> = {
    // Monthly prices - UPDATE THESE WITH YOUR ACTUAL PRICE IDs FROM STRIPE DASHBOARD
    'price_1RSIGpR6OeqomohOPQNu7awg': 'standard', // Standard $1/month
    'price_1RSIHaR6OeqomohOzOEwFOgZ': 'pro',      // Pro $26/month
    'price_1RSIICR6OeqomohOLsmbhNj8': 'premium',  // Premium $78/month

    // ADD YOUR ACTUAL PRICE IDs HERE:
    // Copy from Stripe Dashboard → Products → Your Product → Price ID
    // Example:
    // 'price_1ABC123DEF456GHI789': 'standard',
    // 'price_1XYZ789ABC123DEF456': 'pro',
    // 'price_1QWE456RTY789UIO123': 'premium',
  };

  console.log(`🔍 Looking up price ID: ${priceId}`);
  console.log(`📋 Available mappings:`, Object.keys(priceMapping));

  const planId = priceMapping[priceId];
  if (!planId) {
    console.error(`❌ Unknown price ID: ${priceId}. Please add it to the price mapping.`);
    console.error(`💡 Add this line to priceMapping: '${priceId}': 'standard', // or 'pro' or 'premium'`);
    console.error(`🔧 Go to Stripe Dashboard → Products to find your actual price IDs`);

    // For now, let's default to standard plan so the webhook doesn't completely fail
    console.log(`⚠️ Defaulting to 'standard' plan for unknown price ID`);
    return 'standard';
  } else {
    console.log(`✅ Mapped ${priceId} to plan: ${planId}`);
  }

  return planId;
}

export async function handleOneTimePayment(
  supabase: any,
  userId: string,
  session: Stripe.Checkout.Session
) {
  try {
    // Handle one-time credit purchases
    const amount = session.amount_total || 0;
    const creditsToAdd = calculateCreditsFromAmount(amount);

    if (creditsToAdd > 0) {
      // Use the database function to add credits with expiration
      const { error } = await supabase.rpc('add_credits_with_expiration', {
        p_user_id: userId,
        p_amount: creditsToAdd,
        p_description: `One-time credit purchase: $${(amount / 100).toFixed(2)}`,
        p_transaction_type: 'purchase',
        p_expires_days: 30, // Credits expire in 30 days
      });

      if (error) {
        console.error('❌ Error adding one-time credits:', error);
        throw error;
      }

      console.log(`✅ Added ${creditsToAdd} credits to user ${userId} (expires in 30 days)`);
    }
  } catch (error) {
    console.error('❌ Error handling one-time payment:', error);
    throw error;
  }
}

export function calculateCreditsFromAmount(amountInCents: number): number {
  // Define your credit pricing
  // Example: $1 = 100 credits
  const creditsPerDollar = 100;
  const dollars = amountInCents / 100;
  return Math.floor(dollars * creditsPerDollar);
}

export async function handleFailedPayment(
  supabase: any,
  userId: string,
  subscriptionId: string | null
) {
  try {
    // Get current user data
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('subscription_type, subscription_status')
      .eq('id', userId)
      .single();

    if (userError || !user) {
      console.error('❌ User not found for failed payment handling');
      return;
    }

    // If this is the first failed payment, mark as past_due
    // If multiple failures, downgrade to free plan
    if (subscriptionId) {
      const subscription = await stripe.subscriptions.retrieve(subscriptionId);

      if (subscription.status === 'past_due') {
        // First failure - mark as past due but keep access
        await supabase
          .from('users')
          .update({
            subscription_status: 'past_due',
            updated_at: new Date().toISOString(),
          })
          .eq('id', userId);

        console.log(`⚠️ User ${userId} marked as past due`);
      } else if (subscription.status === 'unpaid') {
        // Multiple failures - downgrade to free
        await downgradeToFree(supabase, userId);
        console.log(`⬇️ User ${userId} downgraded to free due to failed payments`);
      }
    }
  } catch (error) {
    console.error('❌ Error handling failed payment:', error);
    throw error;
  }
}

export async function downgradeToFree(supabase: any, userId: string) {
  try {
    // Use the database function to update to free plan
    const { error } = await supabase.rpc('update_user_subscription', {
      p_user_id: userId,
      p_plan_id: 'free',
      p_subscription_status: 'canceled',
    });

    if (error) {
      console.error('❌ Error downgrading to free:', error);
      throw error;
    }

    // Record the downgrade in subscription history
    await supabase
      .from('subscription_history')
      .insert({
        user_id: userId,
        plan_id: 'free',
        action: 'downgraded',
        effective_date: new Date().toISOString(),
        metadata: { reason: 'failed_payment' },
      });

    console.log(`✅ User ${userId} downgraded to free plan`);
  } catch (error) {
    console.error('❌ Error in downgradeToFree:', error);
    throw error;
  }
}

export async function cancelSubscriptionInStripe(subscriptionId: string) {
  try {
    const subscription = await stripe.subscriptions.cancel(subscriptionId);
    console.log(`✅ Stripe subscription cancelled: ${subscriptionId}`);
    return subscription;
  } catch (error) {
    console.error('❌ Error cancelling Stripe subscription:', error);
    throw error;
  }
}

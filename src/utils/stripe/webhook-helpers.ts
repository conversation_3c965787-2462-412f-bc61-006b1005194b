import Stripe from 'stripe';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2024-06-20',
});

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

export async function getUserByCustomerId(supabase: any, customerId: string) {
  try {
    // First try to find user by stripe_customer_id
    const { data: user, error } = await supabase
      .from('users')
      .select('*')
      .eq('stripe_customer_id', customerId)
      .single();

    if (!error && user) {
      return user;
    }

    // If not found, try to find by email
    const customer = await stripe.customers.retrieve(customerId);
    if (!customer || customer.deleted || !customer.email) {
      return null;
    }

    const { data: userByEmail, error: emailError } = await supabase
      .from('users')
      .select('*')
      .eq('email', customer.email)
      .single();

    if (!emailError && userByEmail) {
      // Update the user with the stripe_customer_id for future lookups
      await supabase
        .from('users')
        .update({ stripe_customer_id: customerId })
        .eq('id', userByEmail.id);

      return userByEmail;
    }

    return null;
  } catch (error) {
    console.error('Error finding user by customer ID:', error);
    return null;
  }
}

export async function recordPayment(
  supabase: any,
  userId: string,
  paymentIntent: Stripe.PaymentIntent,
  status: string
) {
  try {
    await supabase
      .from('payment_history')
      .insert({
        user_id: userId,
        stripe_payment_intent_id: paymentIntent.id,
        amount: paymentIntent.amount,
        currency: paymentIntent.currency,
        status: status,
        payment_method: paymentIntent.payment_method_types?.[0] || 'unknown',
        description: `Payment ${status}: $${(paymentIntent.amount / 100).toFixed(2)}`,
        metadata: {
          payment_method_id: paymentIntent.payment_method,
          client_secret: paymentIntent.client_secret,
        },
      });

    console.log(`✅ Payment recorded: ${paymentIntent.id} - ${status}`);
  } catch (error) {
    console.error('❌ Error recording payment:', error);
    throw error;
  }
}

export async function processSubscription(
  supabase: any,
  userId: string,
  subscription: Stripe.Subscription
) {
  try {
    const priceId = subscription.items.data[0]?.price.id;
    const planId = getPlanIdFromPriceId(priceId);

    if (!planId) {
      console.error('❌ Unknown price ID:', priceId);
      return;
    }

    // Use the database function to update subscription
    const { error } = await supabase.rpc('update_user_subscription', {
      p_user_id: userId,
      p_plan_id: planId,
      p_stripe_customer_id: subscription.customer,
      p_stripe_subscription_id: subscription.id,
      p_stripe_price_id: priceId,
      p_subscription_status: subscription.status,
      p_period_start: new Date(subscription.current_period_start * 1000).toISOString(),
      p_period_end: new Date(subscription.current_period_end * 1000).toISOString(),
    });

    if (error) {
      console.error('❌ Error updating subscription:', error);
      throw error;
    }

    console.log(`✅ Subscription processed for user ${userId}: ${planId} plan`);
  } catch (error) {
    console.error('❌ Error processing subscription:', error);
    throw error;
  }
}

export function getPlanIdFromPriceId(priceId: string): string | null {
  // Map your Stripe price IDs to plan IDs
  // UPDATE THESE WITH YOUR ACTUAL STRIPE PRICE IDs
  const priceMapping: Record<string, string> = {
    // Monthly prices - UPDATE THESE WITH YOUR ACTUAL PRICE IDs FROM STRIPE DASHBOARD
    'price_1RSIGpR6OeqomohOPQNu7awg': 'standard', // Standard $1/month
    'price_1RSIHaR6OeqomohOzOEwFOgZ': 'pro',      // Pro $26/month
    'price_1RSIICR6OeqomohOLsmbhNj8': 'premium',  // Premium $78/month

    // ADD YOUR ACTUAL PRICE IDs HERE:
    // Copy from Stripe Dashboard → Products → Your Product → Price ID
    // Example:
    // 'price_1ABC123DEF456GHI789': 'standard',
    // 'price_1XYZ789ABC123DEF456': 'pro',
    // 'price_1QWE456RTY789UIO123': 'premium',
  };

  console.log(`🔍 Looking up price ID: ${priceId}`);
  console.log(`📋 Available mappings:`, Object.keys(priceMapping));

  const planId = priceMapping[priceId];
  if (!planId) {
    console.error(`❌ Unknown price ID: ${priceId}. Please add it to the price mapping.`);
    console.error(`💡 Add this line to priceMapping: '${priceId}': 'standard', // or 'pro' or 'premium'`);
    console.error(`🔧 Go to Stripe Dashboard → Products to find your actual price IDs`);

    // For now, let's default to standard plan so the webhook doesn't completely fail
    console.log(`⚠️ Defaulting to 'standard' plan for unknown price ID`);
    return 'standard';
  } else {
    console.log(`✅ Mapped ${priceId} to plan: ${planId}`);
  }

  return planId;
}

export async function handleOneTimePayment(
  supabase: any,
  userId: string,
  session: Stripe.Checkout.Session
) {
  try {
    // Handle one-time credit purchases
    const amount = session.amount_total || 0;
    const creditsToAdd = calculateCreditsFromAmount(amount);

    if (creditsToAdd > 0) {
      // Use the database function to add credits with expiration
      const { error } = await supabase.rpc('add_credits_with_expiration', {
        p_user_id: userId,
        p_amount: creditsToAdd,
        p_description: `One-time credit purchase: $${(amount / 100).toFixed(2)}`,
        p_transaction_type: 'purchase',
        p_expires_days: 30, // Credits expire in 30 days
      });

      if (error) {
        console.error('❌ Error adding one-time credits:', error);
        throw error;
      }

      console.log(`✅ Added ${creditsToAdd} credits to user ${userId} (expires in 30 days)`);
    }
  } catch (error) {
    console.error('❌ Error handling one-time payment:', error);
    throw error;
  }
}

export function calculateCreditsFromAmount(amountInCents: number): number {
  // Define your credit pricing
  // Example: $1 = 100 credits
  const creditsPerDollar = 100;
  const dollars = amountInCents / 100;
  return Math.floor(dollars * creditsPerDollar);
}

export async function handleFailedPayment(
  supabase: any,
  userId: string,
  subscriptionId: string | null
) {
  try {
    // Get current user data
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('subscription_type, subscription_status')
      .eq('id', userId)
      .single();

    if (userError || !user) {
      console.error('❌ User not found for failed payment handling');
      return;
    }

    // If this is the first failed payment, mark as past_due
    // If multiple failures, downgrade to free plan
    if (subscriptionId) {
      const subscription = await stripe.subscriptions.retrieve(subscriptionId);

      if (subscription.status === 'past_due') {
        // First failure - mark as past due but keep access
        await supabase
          .from('users')
          .update({
            subscription_status: 'past_due',
            updated_at: new Date().toISOString(),
          })
          .eq('id', userId);

        console.log(`⚠️ User ${userId} marked as past due`);
      } else if (subscription.status === 'unpaid') {
        // Multiple failures - downgrade to free
        await downgradeToFree(supabase, userId);
        console.log(`⬇️ User ${userId} downgraded to free due to failed payments`);
      }
    }
  } catch (error) {
    console.error('❌ Error handling failed payment:', error);
    throw error;
  }
}

export async function downgradeToFree(supabase: any, userId: string) {
  try {
    // Use the database function to update to free plan
    const { error } = await supabase.rpc('update_user_subscription', {
      p_user_id: userId,
      p_plan_id: 'free',
      p_subscription_status: 'canceled',
    });

    if (error) {
      console.error('❌ Error downgrading to free:', error);
      throw error;
    }

    // Record the downgrade in subscription history
    await supabase
      .from('subscription_history')
      .insert({
        user_id: userId,
        plan_id: 'free',
        action: 'downgraded',
        effective_date: new Date().toISOString(),
        metadata: { reason: 'failed_payment' },
      });

    console.log(`✅ User ${userId} downgraded to free plan`);
  } catch (error) {
    console.error('❌ Error in downgradeToFree:', error);
    throw error;
  }
}

export async function cancelSubscriptionInStripe(subscriptionId: string) {
  try {
    const subscription = await stripe.subscriptions.cancel(subscriptionId);
    console.log(`✅ Stripe subscription cancelled: ${subscriptionId}`);
    return subscription;
  } catch (error) {
    console.error('❌ Error cancelling Stripe subscription:', error);
    throw error;
  }
}

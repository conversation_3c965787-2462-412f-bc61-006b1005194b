import Stripe from 'stripe';

// Initialize Stripe with your secret key
export const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2024-12-18.acacia',
  typescript: true,
});

// Pricing configuration matching your plans (monthly only for now)
export const PRICING_PLANS = {
  FREE: {
    name: 'Free',
    credits: 5,
    price: 0,
  },
  STANDARD: {
    name: 'Standard',
    credits: 700,
    monthlyPrice: 5, // €5 as per your Stripe setup
    monthlyPriceId: process.env.STRIPE_PRICE_ID_STANDARD_MONTHLY,
  },
  PRO: {
    name: 'Pro',
    credits: 3500,
    monthlyPrice: 26,
    monthlyPriceId: process.env.STRIPE_PRICE_ID_PRO_MONTHLY,
  },
  PREMIUM: {
    name: 'Premium',
    credits: 14500,
    monthlyPrice: 78,
    monthlyPriceId: process.env.STRIPE_PRICE_ID_PREMIUM_MONTHLY,
  },
} as const;

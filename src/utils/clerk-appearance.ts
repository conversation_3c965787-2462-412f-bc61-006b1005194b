/**
 * Shared appearance configuration for Clerk components
 * This ensures consistent styling across all Clerk components in the application
 */
export const clerkAppearance = {
  elements: {
    // Base elements
    rootBox: 'w-full',
    cardBox: 'w-full flex',
    card: 'bg-navy-dark border border-navy-light text-white shadow-lg rounded-lg overflow-hidden',
    
    // Navigation
    navbar: 'bg-navy-dark border-b border-navy-light mb-6 rounded-t-lg',
    navbarButton: 'text-gray-300 hover:text-white hover:bg-navy-light px-4 py-2 rounded-md transition-colors',
    activeNavbarButton: 'text-white bg-navy-light',
    
    // Headers
    headerTitle: 'text-white text-xl font-bold',
    headerSubtitle: 'text-gray-300 mt-1',
    pageScrollBox: 'bg-navy-dark p-6',
    page: 'space-y-6',
    
    // Form elements
    formFieldLabel: 'text-white font-medium mb-1 block',
    formFieldInput: 'bg-navy border border-navy-light text-white rounded-md px-3 py-2 w-full focus:outline-none focus:ring-2 focus:ring-green-400 focus:border-transparent',
    formFieldInputShowPasswordButton: 'text-gray-300 hover:text-white',
    formFieldError: 'text-red-400 text-sm mt-1',
    formFieldSuccess: 'text-green-400 text-sm mt-1',
    formButtonPrimary: 'bg-green-400 text-navy hover:bg-green-500 font-medium py-2 px-4 rounded-md transition-colors',
    formButtonReset: 'text-gray-300 hover:text-white border border-navy-light hover:bg-navy-light py-2 px-4 rounded-md transition-colors',
    
    // Tables
    table: 'w-full border-collapse',
    tableHead: 'bg-navy border-b border-navy-light',
    tableHeadCell: 'text-left text-gray-300 font-medium py-3 px-4',
    tableBody: '',
    tableRow: 'border-b border-navy-light hover:bg-navy-light transition-colors',
    tableCell: 'py-3 px-4 text-white',
    
    // Organization elements
    organizationPreviewTextContainer: 'text-white',
    organizationSwitcherTrigger: 'text-white',
    organizationSwitcherPopoverCard: 'bg-navy-dark border border-navy-light text-white shadow-lg rounded-lg',
    organizationSwitcherPopoverAction: 'text-gray-300 hover:text-white hover:bg-navy-light',
    organizationSwitcherPopoverActionActive: 'bg-navy-light text-white',
    organizationPreviewSecondaryIdentifier: 'text-gray-400',
    
    // Member management
    membershipList: 'space-y-4',
    membershipListItem: 'bg-navy border border-navy-light rounded-lg p-4 flex items-center justify-between',
    membershipListItemInfo: 'flex items-center space-x-3',
    membershipListItemInfoText: 'text-white',
    membershipListItemActions: 'flex items-center space-x-2',
    membershipRole: 'text-gray-300 text-sm',
    membershipRoleAdmin: 'text-green-400',
    
    // User profile elements
    userPreviewTextContainer: 'text-white',
    userButtonTrigger: 'text-white',
    userButtonPopoverCard: 'bg-navy-dark border border-navy-light text-white shadow-lg rounded-lg',
    userButtonPopoverAction: 'text-gray-300 hover:text-white hover:bg-navy-light',
    userButtonPopoverActionActive: 'bg-navy-light text-white',
    userPreviewSecondaryIdentifier: 'text-gray-400',
    
    // Avatar
    avatarBox: 'bg-navy-light rounded-full overflow-hidden border border-navy-light',
    avatarImageActionsUpload: 'bg-green-400 text-navy hover:bg-green-500 transition-colors',
    
    // Invitation
    invitationList: 'space-y-4',
    invitationListItem: 'bg-navy border border-navy-light rounded-lg p-4 flex items-center justify-between',
    invitationListItemInfo: 'flex items-center space-x-3',
    invitationListItemInfoText: 'text-white',
    invitationListItemActions: 'flex items-center space-x-2',
    
    // Buttons and actions
    button: 'rounded-md font-medium transition-colors',
    buttonPrimary: 'bg-green-400 text-navy hover:bg-green-500 py-2 px-4',
    buttonSecondary: 'border border-navy-light text-gray-300 hover:text-white hover:bg-navy-light py-2 px-4',
    buttonDanger: 'bg-red-500 text-white hover:bg-red-600 py-2 px-4',
    
    // Alerts and messages
    alert: 'rounded-lg p-4 mb-4',
    alertSuccess: 'bg-green-400/20 text-green-400 border border-green-400/30',
    alertWarning: 'bg-yellow-400/20 text-yellow-400 border border-yellow-400/30',
    alertError: 'bg-red-400/20 text-red-400 border border-red-400/30',
    
    // Misc
    badge: 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
    badgeSuccess: 'bg-green-400/20 text-green-400',
    badgeWarning: 'bg-yellow-400/20 text-yellow-400',
    badgeError: 'bg-red-400/20 text-red-400',
    divider: 'border-t border-navy-light my-4',
  },
  variables: {
    colorPrimary: '#4ADE80',
    colorBackground: '#061120',
    colorText: '#FFFFFF',
    colorTextSecondary: '#94A3B8',
    colorDanger: '#EF4444',
    colorSuccess: '#4ADE80',
    borderRadius: '0.375rem',
  }
};

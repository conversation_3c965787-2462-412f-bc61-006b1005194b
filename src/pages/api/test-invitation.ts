import { clerkClient } from '@clerk/nextjs/server';
import type { NextApiRequest, NextApiResponse } from 'next';

type ResponseData = {
  success: boolean;
  invitation?: any;
  error?: string;
  message?: string;
};

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<ResponseData>
) {
  if (req.method !== 'POST') {
    return res.status(405).json({ 
      success: false,
      error: 'Method not allowed' 
    });
  }

  try {
    const { email } = req.body;

    if (!email || typeof email !== 'string') {
      return res.status(400).json({ 
        success: false,
        error: 'Valid email is required'
      });
    }

    console.log(`Creating invitation for email: ${email}`);

    try {
      // Create an invitation using Clerk's API
      const invitation = await clerkClient.invitations.createInvitation({
        emailAddress: email,
        publicMetadata: { source: 'api-test' },
        redirectUrl: process.env.NEXT_PUBLIC_CLERK_SIGN_UP_URL || 'https://accounts.clerk.dev/sign-up',
      });

      return res.status(200).json({
        success: true,
        invitation,
        message: `Invitation created and sent to ${email}`
      });
    } catch (clerkError: any) {
      console.error('Error creating invitation with Clerk:', clerkError);
      return res.status(500).json({
        success: false,
        error: 'Error creating invitation',
        message: clerkError.message || 'Unknown Clerk error',
      });
    }
  } catch (error: any) {
    console.error('Error in test-invitation API:', error);
    return res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: error.message || 'Unknown error',
    });
  }
}

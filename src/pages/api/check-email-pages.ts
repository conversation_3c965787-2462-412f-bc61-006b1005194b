import type { NextApiRequest, NextApiResponse } from 'next';
import { clerkClient } from '@clerk/nextjs/server';

type ResponseData = {
  exists: boolean;
  message?: string;
  error?: string;
};

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<ResponseData>
) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ exists: false, error: 'Method not allowed' });
  }

  try {
    const { email } = req.body;

    if (!email) {
      return res.status(400).json({ exists: false, error: 'Email is required' });
    }

    console.log(`API: Checking if email exists: ${email}`);

    try {
      // Check if the email exists in Clerk
      const users = await clerkClient.users.getUserList({
        emailAddress: [email],
        limit: 1,
      });

      // If we find any users with this email, it exists
      const exists = users.data.length > 0;
      
      console.log(`API: Email ${email} exists: ${exists}`);
      
      return res.status(200).json({
        exists,
        message: exists ? 'Email already exists' : 'Email is available',
      });
    } catch (err: any) {
      console.error('API: Error checking email:', err);
      
      // In case of error, return false to allow signup attempt
      return res.status(200).json({
        exists: false,
        error: 'Could not verify email status',
      });
    }
  } catch (err: any) {
    console.error('API: Server error:', err);
    
    return res.status(500).json({
      exists: false,
      error: err.message || 'Internal server error',
    });
  }
}

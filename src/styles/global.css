@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import v0 styles */
@import './v0-styles.css';

/* Float animation for particles */
@keyframes float {
  0% {
    transform: translateY(0) translateX(0);
  }
  50% {
    transform: translateY(-20px) translateX(10px);
  }
  100% {
    transform: translateY(0) translateX(0);
  }
}

/* Rainbow border animation */
@keyframes rainbow-rotate {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.rainbow-border {
  position: absolute;
  inset: -3px;
  border-radius: 9999px;
  padding: 3px;
  background: linear-gradient(
    90deg,
    #ff0000, #ff8000, #ffff00, #80ff00, #00ff00, #00ff80,
    #00ffff, #0080ff, #0000ff, #8000ff, #ff00ff, #ff0080, #ff0000
  );
  background-size: 400% 400%;
  animation: rainbow-rotate 3s linear infinite;
  content: "";
  z-index: 0;
  pointer-events: none;
}

/* Add a pseudo-element for the button background */
.rainbow-border::after {
  content: "";
  position: absolute;
  inset: 3px;
  background: #0f172a; /* Navy background color */
  border-radius: inherit;
  z-index: 1;
}

/* Login button hover effects */
.login-glow {
  mask-image: radial-gradient(ellipse, black, transparent);
  -webkit-mask-image: radial-gradient(ellipse, black, transparent);
}

.login-fill {
  mask-image: radial-gradient(ellipse at center, black 70%, transparent 100%);
  -webkit-mask-image: radial-gradient(ellipse at center, black 70%, transparent 100%);
  mask-size: 100% 100%;
  -webkit-mask-size: 100% 100%;
  mask-repeat: no-repeat;
  -webkit-mask-repeat: no-repeat;
  transform: scale(1.01); /* Slightly larger to ensure full coverage */
}

/* Button glow effects that extend from the button */
.blue-button-glow {
  position: relative;
  overflow: hidden;
}

.blue-button-glow::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, rgba(96, 165, 250, 0), rgba(96, 165, 250, 0.5), rgba(96, 165, 250, 0));
  background-size: 200% 100%;
  border-radius: inherit;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.blue-button-glow:hover::before {
  opacity: 1;
  animation: shimmer 1.5s infinite;
}

/* Purple button glow effect */
.purple-button-glow {
  position: relative;
  overflow: hidden;
}

.purple-button-glow::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, rgba(167, 139, 250, 0), rgba(167, 139, 250, 0.5), rgba(167, 139, 250, 0));
  background-size: 200% 100%;
  border-radius: inherit;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.purple-button-glow:hover::before {
  opacity: 1;
  animation: shimmer 1.5s infinite;
}

/* Green button glow effect */
.green-button-glow {
  position: relative;
  overflow: hidden;
}

.green-button-glow::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, rgba(74, 222, 128, 0), rgba(74, 222, 128, 0.5), rgba(74, 222, 128, 0));
  background-size: 200% 100%;
  border-radius: inherit;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.green-button-glow:hover::before {
  opacity: 1;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
}

'use client';

import { useState, useEffect } from 'react';
import { useSignUp, useClerk } from '@clerk/nextjs';
import { useRouter } from 'next/navigation';

export default function FixedSignUp() {
  const { isLoaded, signUp } = useSignUp();
  const clerk = useClerk();
  const router = useRouter();

  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [verifying, setVerifying] = useState(false);
  const [code, setCode] = useState('');
  const [envStatus, setEnvStatus] = useState<any>(null);

  // Check environment variables on load
  useEffect(() => {
    async function checkEnv() {
      try {
        const res = await fetch('/api/check-env');
        const data = await res.json();
        setEnvStatus(data);

        if (!data.isPublishableKeyValid || !data.isSecretKeyValid) {
          setError('Invalid API keys detected. Please check your environment variables.');
        }
      } catch (err) {
        console.error('Failed to check environment:', err);
      }
    }

    checkEnv();
  }, []);

  // Check if email already exists
  const checkEmailExists = async (email: string): Promise<{ exists: boolean, error?: string }> => {
    try {
      const response = await fetch('/api/check-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      const data = await response.json();

      // Handle rate limiting
      if (response.status === 429) {
        console.warn('Rate limit exceeded when checking email');
        return { exists: false, error: 'Too many requests. Please try again in a moment.' };
      }

      // Handle other errors
      if (!response.ok) {
        console.warn(`Failed to check email existence: ${response.status}`, data);
        // For other errors, we'll proceed with normal signup
        return { exists: false };
      }

      return { exists: data.exists === true };
    } catch (err) {
      console.error('Error checking email existence:', err);
      // If there's an error, we'll proceed with normal signup
      return { exists: false };
    }
  };

  // Handle sign-up
  const handleSignUp = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!isLoaded) return;

    try {
      setLoading(true);
      setError('');

      // Validate password
      if (password.length < 8) {
        setError('Password must be at least 8 characters long');
        return;
      }

      // First check if the email already exists
      const { exists: emailExists, error: emailCheckError } = await checkEmailExists(email);

      // Handle rate limiting or other errors from the email check
      if (emailCheckError) {
        setError(emailCheckError);
        setLoading(false);
        return;
      }

      if (emailExists) {
        setError('This email address is already registered. Please try signing in instead.');
        setLoading(false);
        return;
      }

      // Log attempt
      console.log('Attempting to sign up with:', { email, firstName, lastName });

      // Create user with error handling
      try {
        const result = await signUp.create({
          emailAddress: email,
          password,
          firstName,
          lastName,
        });

        console.log('Sign-up creation successful:', result);

        // Prepare verification
        await signUp.prepareEmailAddressVerification({ strategy: 'email_code' });
        console.log('Verification preparation successful');

        // Switch to verification mode
        setVerifying(true);
      } catch (err: any) {
        console.error('Sign-up error:', err);

        // Handle specific error cases
        if (err.message === 'is unknown') {
          setError('There was an issue with the sign-up process. This might be due to API key configuration or network issues. Please try again or contact support.');
        } else if (err.errors && Array.isArray(err.errors)) {
          setError(err.errors[0]?.message || 'An error occurred during sign-up');
        } else if (err.message) {
          setError(err.message);
        } else {
          setError('An unknown error occurred. Please try again later.');
        }

        throw err; // Re-throw to prevent continuing
      }
    } catch (err) {
      // This catch block is for any errors not caught in the inner try/catch
      console.error('Outer error handler:', err);
    } finally {
      setLoading(false);
    }
  };

  // Handle verification
  const handleVerification = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!isLoaded) return;

    try {
      setLoading(true);
      setError('');

      // Verify the code
      const result = await signUp.attemptEmailAddressVerification({
        code,
      });

      console.log('Verification result:', result);

      if (result.status === 'complete') {
        // Sign-up successful, redirect to dashboard
        router.push('/dashboard');
      } else if (result.status === 'missing_requirements') {
        // Handle missing username requirement
        console.log('Missing requirements detected:', result);

        try {
          // Handle missing requirements without username
          console.log('Missing requirements detected, but we are not using usernames');
          setError('Account creation requires additional information. Please contact support.');

          // Try verification again
          const verifyResult = await signUp.attemptEmailAddressVerification({
            code,
          });

          console.log('Second verification attempt result:', verifyResult);

          if (verifyResult.status === 'complete') {
            router.push('/dashboard');
          } else {
            setError(`Verification still incomplete. Status: ${verifyResult.status}`);
          }
        } catch (updateErr: any) {
          console.error('Error updating user requirements:', updateErr);
          setError(updateErr.errors?.[0]?.message || 'Failed to complete signup requirements');
        }
      } else {
        setError(`Verification not complete. Status: ${result.status}`);
      }
    } catch (err: any) {
      console.error('Verification error:', err);

      if (err.errors && Array.isArray(err.errors)) {
        setError(err.errors[0]?.message || 'Verification failed');
      } else if (err.message) {
        setError(err.message);
      } else {
        setError('An unknown verification error occurred');
      }
    } finally {
      setLoading(false);
    }
  };

  // Handle direct sign-in
  const handleDirectSignIn = async () => {
    try {
      await clerk.openSignIn();
    } catch (err) {
      console.error('Failed to open sign-in:', err);
    }
  };

  if (!isLoaded) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-900">
        <div className="text-white">Loading authentication...</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-900 p-4">
      <div className="w-full max-w-md bg-gray-800 p-8 rounded-lg shadow-lg">
        <h1 className="text-2xl font-bold text-white mb-2">
          {verifying ? 'Verify Your Email' : 'Create Your Account'}
        </h1>
        <p className="text-gray-400 mb-6">
          {verifying ? 'Enter the code sent to your email' : 'Sign up for a new account'}
        </p>

        {envStatus && (
          <div className="mb-4 p-3 bg-blue-500/20 border border-blue-500 rounded text-blue-300 text-sm">
            <p>API Key Status:</p>
            <ul className="list-disc list-inside">
              <li>Publishable Key: {envStatus.isPublishableKeyValid ? '✅ Valid' : '❌ Invalid'}</li>
              <li>Secret Key: {envStatus.isSecretKeyValid ? '✅ Valid' : '❌ Invalid'}</li>
            </ul>
          </div>
        )}

        {error && (
          <div className="mb-4 p-3 bg-red-500/20 border border-red-500 rounded text-red-300">
            {error}
          </div>
        )}

        {!verifying ? (
          <form onSubmit={handleSignUp} className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-gray-300 mb-1">First Name</label>
                <input
                  type="text"
                  value={firstName}
                  onChange={(e) => setFirstName(e.target.value)}
                  className="w-full p-2 bg-gray-700 border border-gray-600 rounded text-white"
                  required
                />
              </div>

              <div>
                <label className="block text-gray-300 mb-1">Last Name</label>
                <input
                  type="text"
                  value={lastName}
                  onChange={(e) => setLastName(e.target.value)}
                  className="w-full p-2 bg-gray-700 border border-gray-600 rounded text-white"
                  required
                />
              </div>
            </div>



            <div>
              <label className="block text-gray-300 mb-1">Email</label>
              <input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="w-full p-2 bg-gray-700 border border-gray-600 rounded text-white"
                required
              />
            </div>

            <div>
              <label className="block text-gray-300 mb-1">Password</label>
              <input
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="w-full p-2 bg-gray-700 border border-gray-600 rounded text-white"
                required
              />
              <p className="text-xs text-gray-400 mt-1">Must be at least 8 characters</p>
            </div>

            <button
              type="submit"
              disabled={loading}
              className="w-full py-2 bg-blue-600 hover:bg-blue-700 text-white rounded font-medium"
            >
              {loading ? 'Signing up...' : 'Sign Up'}
            </button>

            <div className="text-center">
              <button
                type="button"
                onClick={handleDirectSignIn}
                className="text-blue-400 hover:text-blue-300 text-sm"
              >
                Already have an account? Sign in
              </button>
            </div>
          </form>
        ) : (
          <form onSubmit={handleVerification} className="space-y-4">
            <div>
              <label className="block text-gray-300 mb-1">Verification Code</label>
              <input
                type="text"
                value={code}
                onChange={(e) => setCode(e.target.value)}
                className="w-full p-2 bg-gray-700 border border-gray-600 rounded text-white"
                required
              />
              <p className="text-xs text-gray-400 mt-1">Enter the code sent to your email</p>
            </div>

            <button
              type="submit"
              disabled={loading}
              className="w-full py-2 bg-blue-600 hover:bg-blue-700 text-white rounded font-medium"
            >
              {loading ? 'Verifying...' : 'Verify Email'}
            </button>
          </form>
        )}

        <div className="mt-6 text-center text-sm text-gray-400">
          <p>This is an optimized sign-up page designed to work around common Clerk issues.</p>
        </div>
      </div>
    </div>
  );
}

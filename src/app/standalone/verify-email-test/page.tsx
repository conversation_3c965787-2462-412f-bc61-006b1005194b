'use client';

import { useState } from 'react';
import { useSignUp, useSignIn } from '@clerk/nextjs';

export default function VerifyEmailTest() {
  const { isLoaded: isSignUpLoaded, signUp } = useSignUp();
  const { isLoaded: isSignInLoaded, signIn } = useSignIn();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [result, setResult] = useState<any>(null);
  const [rawError, setRawError] = useState<any>(null);
  const [action, setAction] = useState<'signup' | 'signin' | 'check'>('check');

  const checkEmail = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!isSignUpLoaded || !isSignInLoaded) return;

    try {
      setLoading(true);
      setError('');
      setResult(null);
      setRawError(null);
      setAction('check');

      // First, try to check if the email exists using Clerk's client API
      const response = await fetch('/api/check-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      const data = await response.json();
      setResult(data);

      if (data.exists) {
        setResult({
          ...data,
          recommendation: 'This email already exists. Try signing in instead.'
        });
      } else {
        setResult({
          ...data,
          recommendation: 'This email is available for signup.'
        });
      }
    } catch (err: any) {
      console.error('Error checking email:', err);
      setError(err.message || 'An error occurred');
      setRawError(err);
    } finally {
      setLoading(false);
    }
  };

  const handleSignUp = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!isSignUpLoaded) return;

    try {
      setLoading(true);
      setError('');
      setResult(null);
      setRawError(null);
      setAction('signup');

      // Log attempt
      console.log('Attempting to sign up with:', { email, firstName, lastName });

      // Create user
      const result = await signUp.create({
        emailAddress: email,
        password,
        firstName,
        lastName,
      });

      console.log('Sign-up result:', result);
      setResult(result);

      // Prepare verification
      await signUp.prepareEmailAddressVerification({ strategy: 'email_code' });
      setResult((prev: any) => ({ ...prev, verificationPrepared: true }));

    } catch (err: any) {
      console.error('Sign-up error:', err);
      
      // Log detailed error information
      console.log('Error type:', typeof err);
      console.log('Error constructor:', err?.constructor?.name);
      console.log('Error properties:', Object.keys(err || {}));
      console.log('Error JSON:', JSON.stringify(err, null, 2));
      
      setError(err.message || 'An error occurred');
      setRawError(err);
    } finally {
      setLoading(false);
    }
  };

  const handleSignIn = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!isSignInLoaded) return;

    try {
      setLoading(true);
      setError('');
      setResult(null);
      setRawError(null);
      setAction('signin');

      // Log attempt
      console.log('Attempting to sign in with:', { email });

      // Try to sign in
      const result = await signIn.create({
        identifier: email,
        password,
      });

      console.log('Sign-in result:', result);
      setResult(result);
    } catch (err: any) {
      console.error('Sign-in error:', err);
      setError(err.message || 'An error occurred');
      setRawError(err);
    } finally {
      setLoading(false);
    }
  };

  if (!isSignUpLoaded || !isSignInLoaded) {
    return <div>Loading...</div>;
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-900 p-4">
      <div className="w-full max-w-md bg-gray-800 p-8 rounded-lg shadow-lg">
        <h1 className="text-2xl font-bold text-white mb-6">Email Verification Test</h1>
        <p className="text-gray-300 mb-4">
          This tool helps diagnose issues with already verified emails in Clerk.
        </p>

        {error && (
          <div className="mb-4 p-3 bg-red-500/20 border border-red-500 rounded text-red-300">
            <p className="font-bold">Error:</p>
            <p>{error}</p>
          </div>
        )}

        {result && (
          <div className="mb-4 p-3 bg-green-500/20 border border-green-500 rounded text-green-300">
            <p className="font-bold">Result:</p>
            <pre className="text-xs overflow-auto whitespace-pre-wrap">{JSON.stringify(result, null, 2)}</pre>
            
            {result.recommendation && (
              <div className="mt-2 p-2 bg-blue-500/20 border border-blue-500 rounded text-blue-300">
                <p className="font-bold">Recommendation:</p>
                <p>{result.recommendation}</p>
              </div>
            )}
          </div>
        )}

        {rawError && (
          <div className="mb-4 p-3 bg-yellow-500/20 border border-yellow-500 rounded text-yellow-300">
            <p className="font-bold">Raw Error Data:</p>
            <pre className="text-xs overflow-auto whitespace-pre-wrap">{JSON.stringify(rawError, null, 2)}</pre>
          </div>
        )}

        <form onSubmit={checkEmail} className="space-y-4 mb-4">
          <div>
            <label className="block text-gray-300 mb-1">Email</label>
            <input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="w-full p-2 bg-gray-700 border border-gray-600 rounded text-white"
              required
            />
          </div>

          <button
            type="submit"
            disabled={loading && action === 'check'}
            className="w-full py-2 bg-blue-600 hover:bg-blue-700 text-white rounded font-medium"
          >
            {loading && action === 'check' ? 'Checking...' : 'Check Email Status'}
          </button>
        </form>

        <div className="border-t border-gray-700 my-6"></div>

        <form onSubmit={handleSignUp} className="space-y-4 mb-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-gray-300 mb-1">First Name</label>
              <input
                type="text"
                value={firstName}
                onChange={(e) => setFirstName(e.target.value)}
                className="w-full p-2 bg-gray-700 border border-gray-600 rounded text-white"
                required
              />
            </div>

            <div>
              <label className="block text-gray-300 mb-1">Last Name</label>
              <input
                type="text"
                value={lastName}
                onChange={(e) => setLastName(e.target.value)}
                className="w-full p-2 bg-gray-700 border border-gray-600 rounded text-white"
                required
              />
            </div>
          </div>

          <div>
            <label className="block text-gray-300 mb-1">Password</label>
            <input
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="w-full p-2 bg-gray-700 border border-gray-600 rounded text-white"
              required
            />
          </div>

          <button
            type="submit"
            disabled={loading && action === 'signup'}
            className="w-full py-2 bg-green-600 hover:bg-green-700 text-white rounded font-medium"
          >
            {loading && action === 'signup' ? 'Signing up...' : 'Test Sign Up'}
          </button>
        </form>

        <div className="border-t border-gray-700 my-6"></div>

        <form onSubmit={handleSignIn} className="space-y-4">
          <div>
            <label className="block text-gray-300 mb-1">Password</label>
            <input
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="w-full p-2 bg-gray-700 border border-gray-600 rounded text-white"
              required
            />
          </div>

          <button
            type="submit"
            disabled={loading && action === 'signin'}
            className="w-full py-2 bg-purple-600 hover:bg-purple-700 text-white rounded font-medium"
          >
            {loading && action === 'signin' ? 'Signing in...' : 'Test Sign In'}
          </button>
        </form>
      </div>
    </div>
  );
}

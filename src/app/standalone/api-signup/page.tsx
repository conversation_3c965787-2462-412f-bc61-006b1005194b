'use client';

import { useState } from 'react';

export default function ApiSignUp() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [result, setResult] = useState<any>(null);

  // Check if email already exists
  const checkEmailExists = async (email: string): Promise<boolean> => {
    try {
      const response = await fetch('/api/check-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      if (!response.ok) {
        // If the API call fails, we'll proceed with normal signup
        console.warn('Failed to check email existence, proceeding with normal signup');
        return false;
      }

      const data = await response.json();
      return data.exists === true;
    } catch (err) {
      console.error('Error checking email existence:', err);
      // If there's an error, we'll proceed with normal signup
      return false;
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      setIsLoading(true);
      setError('');
      setResult(null);

      // First check if the email already exists
      const emailExists = await checkEmailExists(email);
      if (emailExists) {
        setError('This email address is already registered. Please try signing in instead.');
        setIsLoading(false);
        return;
      }

      // Log the attempt
      console.log('Attempting to sign up with API:', { email, firstName, lastName });

      // Call our API endpoint
      const response = await fetch('/api/clerk-signup', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          password,
          firstName,
          lastName,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to sign up');
      }

      console.log('Sign-up result:', data);
      setResult(data);

    } catch (err: any) {
      console.error('Sign-up error:', err);
      setError(err.message || 'An unknown error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const handleTestClerkApi = async () => {
    try {
      setIsLoading(true);
      setError('');
      setResult(null);

      const response = await fetch('/api/clerk-test');
      const data = await response.json();

      console.log('Clerk API test result:', data);
      setResult(data);

    } catch (err: any) {
      console.error('API test error:', err);
      setError(err.message || 'An unknown error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-900 text-white p-4">
      <div className="w-full max-w-md p-8 space-y-8 bg-gray-800 rounded-lg shadow-lg">
        <div className="text-center">
          <h1 className="text-2xl font-bold">API Sign Up Test</h1>
          <p className="text-gray-400">Testing Clerk API sign-up functionality</p>
        </div>

        {error && (
          <div className="bg-red-500/20 border border-red-500 p-4 rounded-md text-red-300">
            {error}
          </div>
        )}

        {result && (
          <div className="bg-green-500/20 border border-green-500 p-4 rounded-md text-green-300">
            <pre className="whitespace-pre-wrap text-xs">{JSON.stringify(result, null, 2)}</pre>
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <label className="block text-sm font-medium text-gray-400">First Name</label>
            <input
              type="text"
              value={firstName}
              onChange={(e) => setFirstName(e.target.value)}
              className="mt-1 block w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-400">Last Name</label>
            <input
              type="text"
              value={lastName}
              onChange={(e) => setLastName(e.target.value)}
              className="mt-1 block w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white"
              required
            />
          </div>



          <div>
            <label className="block text-sm font-medium text-gray-400">Email</label>
            <input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="mt-1 block w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-400">Password</label>
            <input
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="mt-1 block w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white"
              required
            />
            <p className="mt-1 text-xs text-gray-400">Password must be at least 8 characters</p>
          </div>

          <div className="flex gap-4">
            <button
              type="submit"
              disabled={isLoading}
              className="flex-1 py-2 px-4 bg-blue-600 hover:bg-blue-700 rounded-md font-medium text-white"
            >
              {isLoading ? 'Signing up...' : 'Sign Up via API'}
            </button>

            <button
              type="button"
              onClick={handleTestClerkApi}
              disabled={isLoading}
              className="flex-1 py-2 px-4 bg-purple-600 hover:bg-purple-700 rounded-md font-medium text-white"
            >
              Test Clerk API
            </button>
          </div>
        </form>

        <div className="text-center text-sm text-gray-400">
          <p>This page tests the Clerk API directly through server-side endpoints.</p>
        </div>
      </div>
    </div>
  );
}

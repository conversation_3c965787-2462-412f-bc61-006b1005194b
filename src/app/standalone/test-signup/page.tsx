'use client';

import { useState } from 'react';
import { useSignUp } from '@clerk/nextjs';

export default function TestSignUp() {
  const { isLoaded, signUp } = useSignUp();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [result, setResult] = useState<any>(null);

  // Check if email already exists
  const checkEmailExists = async (email: string): Promise<boolean> => {
    try {
      const response = await fetch('/api/check-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      if (!response.ok) {
        // If the API call fails, we'll proceed with normal signup
        console.warn('Failed to check email existence, proceeding with normal signup');
        return false;
      }

      const data = await response.json();
      setResult({
        emailCheck: data
      });
      return data.exists === true;
    } catch (err) {
      console.error('Error checking email existence:', err);
      // If there's an error, we'll proceed with normal signup
      return false;
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!isLoaded) return;

    try {
      setLoading(true);
      setError('');
      setResult(null);

      // First check if the email already exists
      const emailExists = await checkEmailExists(email);
      if (emailExists) {
        setError('This email address is already registered. Please try signing in instead.');
        setLoading(false);
        return;
      }

      // Log attempt
      console.log('Attempting to sign up with:', { email, firstName, lastName });

      // Create user
      const result = await signUp.create({
        emailAddress: email,
        password,
        firstName,
        lastName,
      });

      console.log('Sign-up result:', result);
      setResult(result);

      // Prepare verification
      await signUp.prepareEmailAddressVerification({ strategy: 'email_code' });
      setResult((prev: any) => ({ ...prev, verificationPrepared: true }));

    } catch (err: any) {
      console.error('Sign-up error:', err);

      // Log detailed error information
      console.log('Error type:', typeof err);
      console.log('Error constructor:', err?.constructor?.name);
      console.log('Error properties:', Object.keys(err || {}));
      console.log('Error JSON:', JSON.stringify(err, null, 2));

      if (err.errors && Array.isArray(err.errors)) {
        setError(err.errors[0]?.message || 'An error occurred');
      } else if (err.message) {
        setError(err.message);
      } else {
        setError('An unknown error occurred');
      }
    } finally {
      setLoading(false);
    }
  };

  if (!isLoaded) {
    return <div>Loading...</div>;
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-900 p-4">
      <div className="w-full max-w-md bg-gray-800 p-8 rounded-lg shadow-lg">
        <h1 className="text-2xl font-bold text-white mb-6">Test Sign Up</h1>

        {error && (
          <div className="mb-4 p-3 bg-red-500/20 border border-red-500 rounded text-red-300">
            {error}
          </div>
        )}

        {result && (
          <div className="mb-4 p-3 bg-green-500/20 border border-green-500 rounded text-green-300">
            <pre className="text-xs overflow-auto">{JSON.stringify(result, null, 2)}</pre>
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-gray-300 mb-1">First Name</label>
            <input
              type="text"
              value={firstName}
              onChange={(e) => setFirstName(e.target.value)}
              className="w-full p-2 bg-gray-700 border border-gray-600 rounded text-white"
              required
            />
          </div>

          <div>
            <label className="block text-gray-300 mb-1">Last Name</label>
            <input
              type="text"
              value={lastName}
              onChange={(e) => setLastName(e.target.value)}
              className="w-full p-2 bg-gray-700 border border-gray-600 rounded text-white"
              required
            />
          </div>



          <div>
            <label className="block text-gray-300 mb-1">Email</label>
            <input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="w-full p-2 bg-gray-700 border border-gray-600 rounded text-white"
              required
            />
          </div>

          <div>
            <label className="block text-gray-300 mb-1">Password</label>
            <input
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="w-full p-2 bg-gray-700 border border-gray-600 rounded text-white"
              required
            />
            <p className="text-xs text-gray-400 mt-1">Must be at least 8 characters</p>
          </div>

          <button
            type="submit"
            disabled={loading}
            className="w-full py-2 bg-blue-600 hover:bg-blue-700 text-white rounded font-medium"
          >
            {loading ? 'Signing up...' : 'Sign Up'}
          </button>
        </form>

        <div className="mt-6 text-center text-sm text-gray-400">
          <p>This is a minimal test page to diagnose Clerk sign-up issues.</p>
          <p className="mt-2">Check the browser console for detailed error information.</p>
        </div>
      </div>
    </div>
  );
}

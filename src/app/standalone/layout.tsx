'use client';

import { <PERSON><PERSON><PERSON><PERSON> } from '@clerk/nextjs';
import { enUS } from '@clerk/localizations';
import { clerkAppearance } from '@/utils/clerk-config';

export default function StandaloneLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <ClerkProvider
      localization={enUS}
      publishableKey={process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY}
      appearance={clerkAppearance}
    >
      {children}
    </ClerkProvider>
  );
}

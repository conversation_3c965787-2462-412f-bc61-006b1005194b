import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';
import { clerkClient } from '@clerk/nextjs/server';

// Simple in-memory cache with expiration
type CacheEntry = {
  data: any;
  timestamp: number;
};

const cache = new Map<string, CacheEntry>();
const CACHE_TTL = 60 * 1000; // 1 minute cache
const RATE_LIMIT_WINDOW = 60 * 1000; // 1 minute window
const MAX_REQUESTS_PER_WINDOW = 10; // Max 10 requests per minute per IP

// Rate limiting tracker
const rateLimitTracker = new Map<string, { count: number, resetTime: number }>();

export async function POST(req: NextRequest) {
  try {
    // Get client IP for rate limiting
    const ip = req.headers.get('x-forwarded-for') || 'unknown';

    // Check rate limit
    const now = Date.now();
    const rateLimit = rateLimitTracker.get(ip) || { count: 0, resetTime: now + RATE_LIMIT_WINDOW };

    // Reset counter if window has passed
    if (now > rateLimit.resetTime) {
      rateLimit.count = 0;
      rateLimit.resetTime = now + RATE_LIMIT_WINDOW;
    }

    // Increment counter and check limit
    rateLimit.count++;
    rateLimitTracker.set(ip, rateLimit);

    if (rateLimit.count > MAX_REQUESTS_PER_WINDOW) {
      return NextResponse.json(
        { error: 'Too many requests. Please try again later.' },
        { status: 429 }
      );
    }

    const { email } = await req.json();

    if (!email) {
      return NextResponse.json(
        { error: 'Email is required' },
        { status: 400 }
      );
    }

    // Check cache first
    const cacheKey = `email:${email.toLowerCase()}`;
    const cachedResult = cache.get(cacheKey);

    if (cachedResult && (now - cachedResult.timestamp) < CACHE_TTL) {
      console.log('Returning cached result for email:', email);
      return NextResponse.json(cachedResult.data);
    }

    // Log attempt (without sensitive data)
    console.log('Checking if email exists:', email);

    // Try to find users with this email
    const users = await clerkClient.users.getUserList({
      emailAddress: [email],
      limit: 1, // Only need to know if at least one exists
    });

    const exists = users.data.length > 0;

    // Get more details about the first user if it exists
    let userDetails = null;
    if (exists && users.data[0]) {
      const user = users.data[0];
      userDetails = {
        id: user.id,
        firstName: user.firstName,
        lastName: user.lastName,
        emailVerified: user.emailAddresses.some(e =>
          e.emailAddress.toLowerCase() === email.toLowerCase() && e.verification?.status === 'verified'
        ),
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
        lastSignInAt: user.lastSignInAt,
      };
    }

    const result = {
      exists,
      userDetails,
      message: exists
        ? 'Email already exists in the system'
        : 'Email is available for registration',
    };

    // Cache the result
    cache.set(cacheKey, { data: result, timestamp: now });

    return NextResponse.json(result);
  } catch (error: any) {
    console.error('Error checking email:', error);

    return NextResponse.json({
      success: false,
      error: error.message || 'Unknown error',
      details: error.errors || [],
    }, { status: 500 });
  }
}

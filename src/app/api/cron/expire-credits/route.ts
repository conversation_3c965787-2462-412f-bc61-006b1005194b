import { NextResponse } from 'next/server';
import { createServiceRoleSupabaseClient } from '@/utils/supabase/server';

// This endpoint should be called by a cron job (e.g., Vercel Cron, GitHub Actions, or external service)
// to automatically expire credits and handle subscription renewals

export async function POST(request: Request) {
  try {
    // Verify the request is from a trusted source (optional but recommended)
    const authHeader = request.headers.get('authorization');
    const expectedToken = process.env.CRON_SECRET_TOKEN;
    
    if (expectedToken && authHeader !== `Bearer ${expectedToken}`) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const supabase = createServiceRoleSupabaseClient();

    console.log('🕐 Starting credit expiration and subscription maintenance job...');

    // 1. Expire credits that have passed their expiration date
    await expireCredits(supabase);

    // 2. Handle subscription renewals and billing cycles
    await handleSubscriptionMaintenance(supabase);

    // 3. Clean up old data (optional)
    await cleanupOldData(supabase);

    console.log('✅ Credit expiration and subscription maintenance completed successfully');

    return NextResponse.json({
      success: true,
      timestamp: new Date().toISOString(),
      message: 'Credit expiration and subscription maintenance completed'
    });

  } catch (error: any) {
    console.error('❌ Error in credit expiration job:', error);
    return NextResponse.json({
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

// Also allow GET for manual testing
export async function GET() {
  return POST(new Request('http://localhost/api/cron/expire-credits', { method: 'POST' }));
}

async function expireCredits(supabase: any) {
  console.log('🔄 Expiring credits...');

  try {
    // Use the database function to expire credits
    const { error } = await supabase.rpc('expire_credits');

    if (error) {
      console.error('❌ Error expiring credits:', error);
      throw error;
    }

    // Get count of expired credits for logging
    const { data: expiredCredits, error: countError } = await supabase
      .from('credit_transactions')
      .select('id, user_id, amount')
      .eq('is_expired', true)
      .gte('updated_at', new Date(Date.now() - 60000).toISOString()); // Last minute

    if (!countError && expiredCredits?.length > 0) {
      console.log(`✅ Expired ${expiredCredits.length} credit transactions`);
    }

  } catch (error) {
    console.error('❌ Error in expireCredits:', error);
    throw error;
  }
}

async function handleSubscriptionMaintenance(supabase: any) {
  console.log('🔄 Handling subscription maintenance...');

  try {
    // 1. Check for subscriptions that should be renewed
    const { data: subscriptions, error: subError } = await supabase
      .from('users')
      .select('id, email, stripe_subscription_id, subscription_status, next_billing_date')
      .not('stripe_subscription_id', 'is', null)
      .in('subscription_status', ['active', 'trialing'])
      .lt('next_billing_date', new Date().toISOString());

    if (subError) {
      console.error('❌ Error fetching subscriptions:', subError);
      return;
    }

    if (subscriptions && subscriptions.length > 0) {
      console.log(`🔄 Found ${subscriptions.length} subscriptions to check`);

      // Note: In a real implementation, you might want to sync with Stripe
      // to get the latest subscription status, but webhooks should handle most cases
      for (const subscription of subscriptions) {
        console.log(`Checking subscription for user ${subscription.id}`);
        // Additional subscription maintenance logic can go here
      }
    }

    // 2. Check for past due subscriptions that need attention
    const { data: pastDueUsers, error: pastDueError } = await supabase
      .from('users')
      .select('id, email, subscription_status, last_payment_date')
      .eq('subscription_status', 'past_due')
      .lt('last_payment_date', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString()); // 7 days ago

    if (!pastDueError && pastDueUsers && pastDueUsers.length > 0) {
      console.log(`⚠️ Found ${pastDueUsers.length} users with past due subscriptions over 7 days`);
      
      // Downgrade users who have been past due for more than 7 days
      for (const user of pastDueUsers) {
        console.log(`Downgrading user ${user.id} due to extended past due status`);
        
        // Use the database function to downgrade to free
        const { error: downgradeError } = await supabase.rpc('update_user_subscription', {
          p_user_id: user.id,
          p_plan_id: 'free',
          p_subscription_status: 'canceled',
        });

        if (downgradeError) {
          console.error(`❌ Error downgrading user ${user.id}:`, downgradeError);
        } else {
          console.log(`✅ Downgraded user ${user.id} to free plan`);
        }
      }
    }

  } catch (error) {
    console.error('❌ Error in handleSubscriptionMaintenance:', error);
    throw error;
  }
}

async function cleanupOldData(supabase: any) {
  console.log('🧹 Cleaning up old data...');

  try {
    // 1. Clean up old payment history (keep last 2 years)
    const twoYearsAgo = new Date();
    twoYearsAgo.setFullYear(twoYearsAgo.getFullYear() - 2);

    const { error: paymentCleanupError } = await supabase
      .from('payment_history')
      .delete()
      .lt('created_at', twoYearsAgo.toISOString())
      .neq('status', 'succeeded'); // Keep successful payments for records

    if (paymentCleanupError) {
      console.error('❌ Error cleaning up payment history:', paymentCleanupError);
    } else {
      console.log('✅ Cleaned up old payment history');
    }

    // 2. Clean up old subscription history (keep last 1 year)
    const oneYearAgo = new Date();
    oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1);

    const { error: subHistoryCleanupError } = await supabase
      .from('subscription_history')
      .delete()
      .lt('created_at', oneYearAgo.toISOString())
      .neq('action', 'created'); // Keep subscription creation records

    if (subHistoryCleanupError) {
      console.error('❌ Error cleaning up subscription history:', subHistoryCleanupError);
    } else {
      console.log('✅ Cleaned up old subscription history');
    }

    // 3. Clean up expired credit transactions (keep for audit trail but mark as processed)
    const { error: creditCleanupError } = await supabase
      .from('credit_transactions')
      .update({ metadata: { processed: true } })
      .eq('is_expired', true)
      .is('metadata->processed', null);

    if (creditCleanupError) {
      console.error('❌ Error marking expired credits as processed:', creditCleanupError);
    } else {
      console.log('✅ Marked expired credits as processed');
    }

  } catch (error) {
    console.error('❌ Error in cleanupOldData:', error);
    // Don't throw here as cleanup is not critical
  }
}

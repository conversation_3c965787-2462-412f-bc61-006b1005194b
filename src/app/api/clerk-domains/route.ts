import { NextResponse } from 'next/server';

export async function GET() {
  try {
    // Get environment variables
    const envVars = {
      NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY: process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY?.substring(0, 10) + '...',
      CLERK_SECRET_KEY: process.env.CLERK_SECRET_KEY ? process.env.CLERK_SECRET_KEY.substring(0, 10) + '...' : 'missing',
      CLERK_ENCRYPTION_KEY: process.env.CLERK_ENCRYPTION_KEY ? 'present' : 'missing',
      NODE_ENV: process.env.NODE_ENV,
    };

    // Extract domain from publishable key
    let domain = 'unknown';
    try {
      const key = process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY || '';
      // Clerk keys are base64 encoded and end with the domain
      const decoded = Buffer.from(key, 'base64').toString();
      domain = decoded.split('$').pop() || 'unknown';
    } catch (e) {
      console.error('Error decoding publishable key:', e);
    }

    return NextResponse.json({
      success: true,
      message: 'Clerk domain information',
      domain,
      envVars,
      currentHost: {
        hostname: process.env.NEXT_PUBLIC_VERCEL_URL || 'localhost',
        port: process.env.PORT || '3000',
      }
    });
  } catch (error: any) {
    console.error('Clerk domain check failed:', error);

    return NextResponse.json({
      success: false,
      error: error.message || 'Unknown error',
      stack: error.stack,
    }, { status: 500 });
  }
}

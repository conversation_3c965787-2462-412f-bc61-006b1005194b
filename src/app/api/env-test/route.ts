import { NextResponse } from 'next/server';

export async function GET() {
  // Get all environment variables related to Clerk
  const envVars = {
    NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY: process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY || 'missing',
    CLERK_SECRET_KEY: process.env.CLERK_SECRET_KEY || 'missing',
    CLERK_WEBHOOK_SECRET: process.env.CLERK_WEBHOOK_SECRET || 'missing',
    CLERK_ENCRYPTION_KEY: process.env.CLERK_ENCRYPTION_KEY || 'missing',
    
    // Check if we can see the first few characters of each key
    publishableKeyPrefix: process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY 
      ? process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY.substring(0, 10) + '...' 
      : 'missing',
    secretKeyPrefix: process.env.CLERK_SECRET_KEY 
      ? process.env.CLERK_SECRET_KEY.substring(0, 10) + '...' 
      : 'missing',
    
    // Check if the keys are being loaded from .env or .env.local
    NODE_ENV: process.env.NODE_ENV || 'missing',
  };

  return NextResponse.json(envVars);
}

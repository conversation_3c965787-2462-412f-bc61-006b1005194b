import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';

export async function POST(request: Request) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { planId, isYearly } = await request.json();

    // For now, we'll simulate a successful plan selection
    // This will work until you set up actual Clerk billing
    console.log(`User ${userId} selected plan: ${planId}, yearly: ${isYearly}`);

    // Store the plan selection in user metadata (optional)
    try {
      // You can uncomment this when ready to store plan info
      // await clerkClient.users.updateUserMetadata(userId, {
      //   publicMetadata: {
      //     selectedPlan: planId,
      //     billingCycle: isYearly ? 'yearly' : 'monthly',
      //     planSelectedAt: new Date().toISOString()
      //   }
      // });
    } catch (metadataError) {
      console.log('Could not update metadata:', metadataError);
    }

    // Return success response
    return NextResponse.json({
      success: true,
      planId,
      billingCycle: isYearly ? 'yearly' : 'monthly',
      message: `Successfully selected ${planId} plan!`,
      nextStep: 'redirect_to_dashboard'
    });

  } catch (error) {
    console.error('Subscription creation error:', error);
    return NextResponse.json(
      { error: 'Failed to process plan selection' },
      { status: 500 }
    );
  }
}

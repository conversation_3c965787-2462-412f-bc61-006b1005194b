import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { createServiceRoleSupabaseClient } from '@/utils/supabase/server';

export async function POST() {
  try {
    // Get the current user and session claims
    const { userId, sessionClaims } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    console.log(`🔄 Syncing Clerk subscription for user ${userId}`);
    console.log(`📋 Session claims:`, sessionClaims);

    const supabase = createServiceRoleSupabaseClient();

    // Extract plan from Clerk's session claims
    const clerkPlan = sessionClaims?.pla as string; // e.g., "u:standard", "u:free_user", "u:pro"
    
    if (!clerkPlan) {
      return NextResponse.json({
        success: false,
        error: 'No plan information found in session claims'
      }, { status: 400 });
    }

    console.log(`📋 Clerk plan from session claims: ${clerkPlan}`);

    // Parse the plan (remove "u:" prefix)
    const planName = clerkPlan.replace('u:', '').replace('_user', '');
    
    // Map Clerk plan names to our plan IDs
    const planMapping: Record<string, string> = {
      'free': 'free',
      'standard': 'standard',
      'pro': 'pro',
      'premium': 'premium',
    };

    const planId = planMapping[planName] || 'free';
    
    console.log(`🔄 Mapping Clerk plan "${planName}" to our plan "${planId}"`);

    // Get current user data from our database
    const { data: currentUser, error: currentUserError } = await supabase
      .from('users')
      .select('subscription_type, credits, last_payment_date')
      .eq('id', userId)
      .single();

    if (currentUserError) {
      console.error('❌ Error fetching current user:', currentUserError);
      return NextResponse.json({
        success: false,
        error: 'Failed to fetch current user data'
      }, { status: 500 });
    }

    // Check if user already has this plan
    const currentPlanId = currentUser?.subscription_type?.toLowerCase() || 'free';
    const targetPlanName = planId === 'free' ? 'Free' : planId.charAt(0).toUpperCase() + planId.slice(1);

    if (currentPlanId === planId) {
      console.log(`✅ User already has ${planId} plan, no sync needed`);
      return NextResponse.json({
        success: true,
        message: `User already has ${targetPlanName} plan`,
        currentPlan: targetPlanName,
        noChangeNeeded: true,
      });
    }

    console.log(`🔄 Upgrading user from ${currentPlanId} to ${planId}`);

    // Get plan details from our database
    const { data: plan, error: planError } = await supabase
      .from('subscription_plans')
      .select('*')
      .eq('id', planId)
      .single();

    if (planError || !plan) {
      console.error('❌ Plan not found:', planId);
      return NextResponse.json({
        success: false,
        error: `Plan not found: ${planId}`
      }, { status: 400 });
    }

    // Update user subscription details
    const { error: userUpdateError } = await supabase
      .from('users')
      .update({
        subscription_type: plan.name,
        subscription_status: 'active',
        last_payment_date: planId !== 'free' ? new Date().toISOString() : currentUser.last_payment_date,
        updated_at: new Date().toISOString(),
      })
      .eq('id', userId);

    if (userUpdateError) {
      console.error('❌ Error updating user subscription:', userUpdateError);
      return NextResponse.json({
        success: false,
        error: 'Failed to update user subscription'
      }, { status: 500 });
    }

    // Add credits if it's a paid plan and user doesn't already have them
    let creditsAdded = 0;
    if (plan.credits_included > 0 && planId !== 'free') {
      // Check if user already received credits for this plan recently
      const { data: recentCredits } = await supabase
        .from('credit_transactions')
        .select('id')
        .eq('user_id', userId)
        .eq('plan_id', planId)
        .eq('transaction_type', 'subscription')
        .gte('created_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()) // Last 24 hours
        .limit(1);

      if (!recentCredits || recentCredits.length === 0) {
        const expiresAt = plan.credits_expire_days > 0 
          ? new Date(Date.now() + plan.credits_expire_days * 24 * 60 * 60 * 1000).toISOString()
          : null;

        // Insert credit transaction
        const { error: creditError } = await supabase
          .from('credit_transactions')
          .insert({
            user_id: userId,
            amount: plan.credits_included,
            description: `Clerk subscription sync: ${plan.name} plan`,
            transaction_type: 'subscription',
            expires_at: expiresAt,
            plan_id: planId,
          });

        if (creditError) {
          console.error('❌ Error adding credits:', creditError);
        } else {
          // Update user's total credits
          const { data: totalCredits, error: totalError } = await supabase
            .from('credit_transactions')
            .select('amount')
            .eq('user_id', userId)
            .eq('is_expired', false);

          if (!totalError) {
            const newTotal = totalCredits?.reduce((sum: number, t: any) => sum + t.amount, 0) || 0;
            
            await supabase
              .from('users')
              .update({ 
                credits: newTotal,
                credits_expire_at: expiresAt,
              })
              .eq('id', userId);

            creditsAdded = plan.credits_included;
          }
        }
      } else {
        console.log('⚠️ User already received credits for this plan recently, skipping');
      }
    }

    // Record subscription history
    await supabase
      .from('subscription_history')
      .insert({
        user_id: userId,
        plan_id: planId,
        action: 'clerk_sync',
        effective_date: new Date().toISOString(),
        metadata: {
          clerk_plan: clerkPlan,
          sync_method: 'session_claims',
          credits_added: creditsAdded,
        },
      });

    // Get updated user data
    const { data: updatedUser, error: userError } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single();

    console.log(`✅ Clerk subscription synced successfully for user ${userId}: ${planId} plan`);

    return NextResponse.json({
      success: true,
      message: `Successfully synced to ${plan.name} plan`,
      results: {
        clerkPlan: clerkPlan,
        mappedPlan: planId,
        planName: plan.name,
        creditsAdded: creditsAdded,
        user: updatedUser,
      },
      timestamp: new Date().toISOString(),
    });

  } catch (error: any) {
    console.error('❌ Error syncing Clerk subscription:', error);
    return NextResponse.json({
      success: false,
      error: error.message,
      stack: error.stack
    }, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Clerk subscription sync endpoint',
    description: 'Syncs user subscription from Clerk session claims to database',
    usage: 'POST to sync current user subscription',
    note: 'Reads from sessionClaims.pla field to determine subscription status',
  });
}

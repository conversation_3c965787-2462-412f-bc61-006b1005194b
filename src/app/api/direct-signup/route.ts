import { NextRequest, NextResponse } from 'next/server';
import { clerkClient } from '@clerk/nextjs/server';

export async function POST(req: NextRequest) {
  try {
    const { firstName, lastName, email, password } = await req.json();

    // Validate input
    if (!email || !password || !firstName || !lastName) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Log attempt (without sensitive data)
    console.log('Attempting server-side sign-up for:', email);

    // Create user with Clerk Node SDK
    const user = await clerkClient.users.createUser({
      firstName,
      lastName,
      emailAddress: [email],
      password,
      skipPasswordChecks: true,
    });

    // Log success
    console.log('User created successfully:', user.id);

    return NextResponse.json({
      success: true,
      userId: user.id,
      message: 'User created successfully. Please check your email for verification instructions.',
    });
  } catch (error: any) {
    // Log error
    console.error('Server-side sign-up error:', error);

    // Check for already verified or existing email errors
    const errorMessage = error.errors?.[0]?.message || error.message || '';
    const errorCode = error.errors?.[0]?.code || '';
    const isEmailAlreadyUsed =
      errorMessage.includes('already exists') ||
      errorMessage.includes('verified') ||
      errorCode === 'form_identifier_exists';

    // Set appropriate status code and message
    const statusCode = isEmailAlreadyUsed ? 409 : (error.status || 500); // 409 Conflict for already used email
    const userMessage = isEmailAlreadyUsed
      ? 'This email address is already registered. Please try signing in instead.'
      : errorMessage;

    return NextResponse.json(
      {
        error: userMessage,
        details: error.errors || [],
        code: errorCode,
        clerkTraceId: error.clerkTraceId
      },
      { status: statusCode }
    );
  }
}

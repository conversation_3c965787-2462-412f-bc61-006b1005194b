import { NextRequest, NextResponse } from 'next/server';
import { clerkClient } from '@clerk/nextjs/server';

export async function POST(req: NextRequest) {
  try {
    const { email, password, action = 'check' } = await req.json();

    if (!email) {
      return NextResponse.json(
        { error: 'Email is required' },
        { status: 400 }
      );
    }

    console.log(`Attempting to diagnose and fix "already verified" issue for: ${email}, action: ${action}`);

    // Step 1: Check if the email exists in Clerk's system
    const users = await clerkClient.users.getUserList({
      emailAddress: [email],
      limit: 10,
    });

    // If we find users with this email, return their details
    if (users.data.length > 0) {
      // If the action is 'delete_user' and we found a user, delete it
      if (action === 'delete_user' && users.data[0]?.id) {
        try {
          const userId = users.data[0].id;
          console.log(`Attempting to delete user with ID: ${userId}`);

          // Delete the user
          await clerkClient.users.deleteUser(userId);

          return NextResponse.json({
            success: true,
            message: `User with ID ${userId} has been deleted. You should now be able to sign up with this email.`,
            deletedUserId: userId,
          });
        } catch (deleteError: any) {
          console.error('Error deleting user:', deleteError);
          return NextResponse.json({
            success: false,
            error: deleteError.message || 'Failed to delete user',
            errorDetails: deleteError.errors || [],
          }, { status: 500 });
        }
      }

      // Otherwise just return user details
      return NextResponse.json({
        exists: true,
        users: users.data.map(user => ({
          id: user.id,
          firstName: user.firstName,
          lastName: user.lastName,
          emailAddresses: user.emailAddresses,
          createdAt: user.createdAt,
          updatedAt: user.updatedAt,
          lastSignInAt: user.lastSignInAt,
        })),
        recommendation: 'Email exists in Clerk system. Try signing in instead or delete the user account.',
        canDelete: true,
      });
    }

    // Step 2: If no users found but we suspect "already verified" issue,
    // try to create a new user with special handling
    if (action === 'create_user' && password) {
      try {
        // Create a new user with the email and password
        const newUser = await clerkClient.users.createUser({
          emailAddress: [email],
          password,
          skipPasswordChecks: true,
          skipPasswordRequirement: true, // Correct property name
        });

        return NextResponse.json({
          success: true,
          message: 'Successfully created new user despite "already verified" issue',
          userId: newUser.id,
        });
      } catch (createError: any) {
        // If we get an "already verified" error, we need special handling
        const isAlreadyVerifiedError =
          createError.message?.includes('verified') ||
          createError.message?.includes('already exists') ||
          createError.errors?.some((e: any) =>
            e.message?.includes('verified') || e.message?.includes('already exists')
          );

        if (isAlreadyVerifiedError) {
          // Step 3: Try to delete the email address directly using Clerk's API
          if (action === 'create_user') {
            try {
              // This is a more advanced approach - try to use Clerk's API to delete the email address
              // Note: This is a theoretical approach and may not work depending on Clerk's API
              return NextResponse.json({
                success: false,
                isAlreadyVerifiedError: true,
                error: createError.message || 'Email appears to be already verified',
                recommendation: 'The email exists in Clerk\'s system but no user account was found. This is likely a "ghost" email address.',
                alternativeSolution: 'Try using a different email address or contact Clerk support to purge this email from their system.',
                technicalDetails: createError.errors || [],
              });
            } catch (purgeError: any) {
              console.error('Error purging email address:', purgeError);
              return NextResponse.json({
                success: false,
                error: 'Failed to purge email address',
                details: purgeError.message || 'Unknown error',
              }, { status: 500 });
            }
          }
        }

        // For other errors, return the details
        return NextResponse.json({
          success: false,
          error: createError.message || 'Unknown error',
          errorDetails: createError.errors || [],
        }, { status: 500 });
      }
    }

    // If we get here, the email doesn't exist in Clerk's system but might be a ghost email
    return NextResponse.json({
      exists: false,
      message: 'No user found with this email address.',
      recommendation: 'If you\'re experiencing "already verified" errors, try creating a new account with this email and password.',
      nextStep: 'Use the "Fix Verified Issue" button to attempt creating a new user.',
    });

  } catch (error: any) {
    console.error('Clerk fix verified check failed:', error);

    return NextResponse.json({
      success: false,
      error: error.message || 'Unknown error',
    }, { status: 500 });
  }
}
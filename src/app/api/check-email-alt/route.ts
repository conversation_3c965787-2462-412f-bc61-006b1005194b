import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';
import { clerkClient } from '@clerk/nextjs/server';

export async function POST(req: NextRequest) {
  try {
    const { email } = await req.json();

    if (!email) {
      return NextResponse.json(
        { error: 'Email is required' },
        { status: 400 }
      );
    }

    console.log(`API: Checking if email exists: ${email}`);

    try {
      // Check if the email exists in Clerk
      const users = await clerkClient.users.getUserList({
        emailAddress: [email],
        limit: 1,
      });

      // If we find any users with this email, it exists
      const exists = users.data.length > 0;
      
      console.log(`API: Email ${email} exists: ${exists}`);
      
      return NextResponse.json({
        exists,
        message: exists ? 'Email already exists' : 'Email is available',
      });
    } catch (err: any) {
      console.error('API: Error checking email:', err);
      
      // In case of error, return false to allow signup attempt
      return NextResponse.json({
        exists: false,
        error: 'Could not verify email status',
      });
    }
  } catch (err: any) {
    console.error('API: Server error:', err);
    
    return NextResponse.json(
      { error: err.message || 'Internal server error' },
      { status: 500 }
    );
  }
}

import { auth } from '@clerk/nextjs/server';
import { NextResponse } from 'next/server';

// Make this route dynamic to avoid static generation errors
export const dynamic = 'force-dynamic';

// This is a callback handler for Google OAuth
export async function GET(request: Request) {
  try {
    // Get the URL and extract any query parameters
    const url = new URL(request.url);
    const code = url.searchParams.get('code');
    const state = url.searchParams.get('state');

    console.log('Google OAuth callback received', {
      hasCode: !!code,
      hasState: !!state,
      url: url.toString()
    });

    // Get the current user session
    const { userId } = auth();
    console.log('Current user ID:', userId);

    // If we have a user ID, they're authenticated, so redirect to dashboard
    if (userId) {
      console.log('User is authenticated, redirecting to dashboard');
      return NextResponse.redirect(new URL('/dashboard', url.origin));
    }

    // If we don't have a user ID, redirect to sign-in
    console.log('User is not authenticated, redirecting to sign-in');
    return NextResponse.redirect(new URL('/sign-in', url.origin));
  } catch (error) {
    console.error('Error in Google OAuth callback:', error);

    // Redirect to sign-in page on error
    return NextResponse.redirect(new URL('/sign-in', new URL(request.url).origin));
  }
}

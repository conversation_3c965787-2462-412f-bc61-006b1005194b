import { auth } from '@clerk/nextjs/server';
import { NextResponse } from 'next/server';

// Make this route dynamic to avoid static generation errors
export const dynamic = 'force-dynamic';

// This is a test route to verify OAuth callback functionality
export async function GET(request: Request) {
  try {
    // Get the current user session
    const { userId } = auth();
    
    // Return the user ID and authentication status
    return NextResponse.json({
      isAuthenticated: !!userId,
      userId,
      message: userId 
        ? 'User is authenticated' 
        : 'User is not authenticated',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Error in OAuth test route:', error);
    
    // Return error information
    return NextResponse.json({
      error: 'An error occurred',
      message: error instanceof Error ? error.message : 'Unknown error',
      isAuthenticated: false,
    }, { status: 500 });
  }
}

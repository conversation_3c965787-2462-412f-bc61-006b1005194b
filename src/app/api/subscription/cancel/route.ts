import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import <PERSON><PERSON> from 'stripe';
import { createServiceRoleSupabaseClient } from '@/utils/supabase/server';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2024-06-20',
});

export async function POST(request: Request) {
  try {
    // Authenticate the user
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { subscriptionId } = await request.json();

    if (!subscriptionId) {
      return NextResponse.json({ error: 'Subscription ID is required' }, { status: 400 });
    }

    const supabase = createServiceRoleSupabaseClient();

    // Verify the subscription belongs to the user
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id, stripe_subscription_id, email')
      .eq('id', userId)
      .eq('stripe_subscription_id', subscriptionId)
      .single();

    if (userError || !user) {
      return NextResponse.json({ error: 'Subscription not found or unauthorized' }, { status: 404 });
    }

    console.log(`🗑️ Canceling subscription ${subscriptionId} for user ${userId}`);

    // Cancel the subscription in Stripe (at period end)
    const canceledSubscription = await stripe.subscriptions.update(subscriptionId, {
      cancel_at_period_end: true,
    });

    // Update the user's subscription status in the database
    const { error: updateError } = await supabase
      .from('users')
      .update({
        subscription_cancel_at: new Date(canceledSubscription.cancel_at! * 1000).toISOString(),
        updated_at: new Date().toISOString(),
      })
      .eq('id', userId);

    if (updateError) {
      console.error('❌ Error updating user subscription status:', updateError);
      return NextResponse.json({ error: 'Failed to update subscription status' }, { status: 500 });
    }

    // Record the cancellation in subscription history
    await supabase
      .from('subscription_history')
      .insert({
        user_id: userId,
        plan_id: 'canceled',
        action: 'canceled',
        stripe_subscription_id: subscriptionId,
        effective_date: new Date().toISOString(),
        metadata: {
          cancel_at_period_end: true,
          cancel_at: canceledSubscription.cancel_at,
          canceled_by: 'user',
        },
      });

    console.log(`✅ Subscription ${subscriptionId} canceled successfully for user ${userId}`);

    return NextResponse.json({
      success: true,
      message: 'Subscription canceled successfully. You will retain access until the end of your billing period.',
      cancelAt: new Date(canceledSubscription.cancel_at! * 1000).toISOString(),
    });

  } catch (error: any) {
    console.error('❌ Error canceling subscription:', error);
    
    if (error.type === 'StripeInvalidRequestError') {
      return NextResponse.json({ 
        error: 'Invalid subscription or already canceled' 
      }, { status: 400 });
    }

    return NextResponse.json({ 
      error: 'Failed to cancel subscription' 
    }, { status: 500 });
  }
}

// Reactivate subscription endpoint
export async function PUT(request: Request) {
  try {
    // Authenticate the user
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { subscriptionId } = await request.json();

    if (!subscriptionId) {
      return NextResponse.json({ error: 'Subscription ID is required' }, { status: 400 });
    }

    const supabase = createServiceRoleSupabaseClient();

    // Verify the subscription belongs to the user
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id, stripe_subscription_id, email')
      .eq('id', userId)
      .eq('stripe_subscription_id', subscriptionId)
      .single();

    if (userError || !user) {
      return NextResponse.json({ error: 'Subscription not found or unauthorized' }, { status: 404 });
    }

    console.log(`🔄 Reactivating subscription ${subscriptionId} for user ${userId}`);

    // Reactivate the subscription in Stripe
    const reactivatedSubscription = await stripe.subscriptions.update(subscriptionId, {
      cancel_at_period_end: false,
    });

    // Update the user's subscription status in the database
    const { error: updateError } = await supabase
      .from('users')
      .update({
        subscription_cancel_at: null,
        subscription_status: reactivatedSubscription.status,
        updated_at: new Date().toISOString(),
      })
      .eq('id', userId);

    if (updateError) {
      console.error('❌ Error updating user subscription status:', updateError);
      return NextResponse.json({ error: 'Failed to update subscription status' }, { status: 500 });
    }

    // Record the reactivation in subscription history
    await supabase
      .from('subscription_history')
      .insert({
        user_id: userId,
        plan_id: reactivatedSubscription.items.data[0]?.price.id || 'unknown',
        action: 'reactivated',
        stripe_subscription_id: subscriptionId,
        effective_date: new Date().toISOString(),
        metadata: {
          reactivated_by: 'user',
          status: reactivatedSubscription.status,
        },
      });

    console.log(`✅ Subscription ${subscriptionId} reactivated successfully for user ${userId}`);

    return NextResponse.json({
      success: true,
      message: 'Subscription reactivated successfully.',
      subscription: {
        status: reactivatedSubscription.status,
        currentPeriodEnd: new Date(reactivatedSubscription.current_period_end * 1000).toISOString(),
      },
    });

  } catch (error: any) {
    console.error('❌ Error reactivating subscription:', error);
    
    if (error.type === 'StripeInvalidRequestError') {
      return NextResponse.json({ 
        error: 'Invalid subscription or cannot be reactivated' 
      }, { status: 400 });
    }

    return NextResponse.json({ 
      error: 'Failed to reactivate subscription' 
    }, { status: 500 });
  }
}

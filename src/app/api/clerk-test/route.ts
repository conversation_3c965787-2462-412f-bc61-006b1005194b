import { NextResponse } from 'next/server';
import { clerkClient } from '@clerk/nextjs/server';

export async function GET() {
  try {
    // Get environment variables
    const envVars = {
      NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY: process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY?.substring(0, 10) + '...',
      CLERK_SECRET_KEY: process.env.CLERK_SECRET_KEY ? process.env.CLERK_SECRET_KEY.substring(0, 10) + '...' : 'missing',
      NODE_ENV: process.env.NODE_ENV,
    };

    // Test Clerk API
    const users = await clerkClient.users.getUserList({
      limit: 1,
    });

    return NextResponse.json({
      success: true,
      message: 'Clerk API is working correctly',
      userCount: users.data.length,
      envVars,
    });
  } catch (error: any) {
    console.error('Clerk API test failed:', error);

    return NextResponse.json({
      success: false,
      error: error.message || 'Unknown error',
      stack: error.stack,
      envVars: {
        NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY: process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY?.substring(0, 10) + '...',
        CLERK_SECRET_KEY: process.env.CLERK_SECRET_KEY ? 'present (first 10 chars: ' + process.env.CLERK_SECRET_KEY.substring(0, 10) + ')' : 'missing',
        NODE_ENV: process.env.NODE_ENV,
      }
    }, { status: 500 });
  }
}

import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { createServiceRoleSupabaseClient } from '@/utils/supabase/server';
import Stripe from 'stripe';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2024-06-20',
});

export async function POST(request: Request) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    const { targetPlan, changeType } = await request.json();

    if (!targetPlan || !['free', 'standard', 'pro', 'premium'].includes(targetPlan)) {
      return NextResponse.json({
        error: 'Invalid target plan',
        validPlans: ['free', 'standard', 'pro', 'premium']
      }, { status: 400 });
    }

    if (!changeType || !['upgrade', 'downgrade', 'switch'].includes(changeType)) {
      return NextResponse.json({
        error: 'Invalid change type',
        validTypes: ['upgrade', 'downgrade', 'switch']
      }, { status: 400 });
    }

    console.log(`🔄 Plan change request: ${changeType} to ${targetPlan} for user ${userId}`);

    const supabase = createServiceRoleSupabaseClient();

    // Get current user data
    const { data: currentUser, error: userError } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single();

    if (userError || !currentUser) {
      return NextResponse.json({
        error: 'Failed to fetch user data',
        details: userError?.message
      }, { status: 500 });
    }

    const currentPlan = currentUser.subscription_type?.toLowerCase() || 'free';

    // Get target plan details
    const { data: targetPlanData, error: planError } = await supabase
      .from('subscription_plans')
      .select('*')
      .eq('id', targetPlan)
      .single();

    if (planError || !targetPlanData) {
      return NextResponse.json({
        error: `Target plan '${targetPlan}' not found`,
        details: planError?.message
      }, { status: 404 });
    }

    // Handle different plan change scenarios
    if (targetPlan === 'free') {
      // Downgrade to free
      return await handleDowngradeToFree(supabase, userId, currentUser, currentPlan);
    } else if (currentPlan === 'free') {
      // Upgrade from free (requires payment)
      return await handleUpgradeFromFree(supabase, userId, targetPlan, targetPlanData);
    } else {
      // Plan switch between paid plans
      return await handlePaidPlanSwitch(supabase, userId, currentUser, currentPlan, targetPlan, targetPlanData, changeType);
    }

  } catch (error: any) {
    console.error('❌ Error changing plan:', error);
    return NextResponse.json({
      success: false,
      error: error.message,
      stack: error.stack
    }, { status: 500 });
  }
}

async function handleDowngradeToFree(supabase: any, userId: string, currentUser: any, currentPlan: string) {
  console.log(`⬇️ Downgrading user ${userId} from ${currentPlan} to free`);

  // Update user to free plan
  const { error: updateError } = await supabase
    .from('users')
    .update({
      subscription_type: 'Free',
      subscription_status: 'active',
      last_payment_date: currentUser.last_payment_date, // Keep last payment date
      updated_at: new Date().toISOString(),
    })
    .eq('id', userId);

  if (updateError) {
    throw new Error(`Failed to update user to free plan: ${updateError.message}`);
  }

  // Add free plan credits (50) if user doesn't have them
  const { data: freeCredits } = await supabase
    .from('credit_transactions')
    .select('id')
    .eq('user_id', userId)
    .eq('plan_id', 'free')
    .limit(1);

  if (!freeCredits || freeCredits.length === 0) {
    await supabase
      .from('credit_transactions')
      .insert({
        user_id: userId,
        amount: 50,
        description: `Downgrade to Free plan`,
        transaction_type: 'subscription',
        expires_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
        plan_id: 'free',
      });
  }

  // Recalculate total credits
  await recalculateUserCredits(supabase, userId);

  // Record plan change
  await recordPlanChange(supabase, userId, currentPlan, 'free', 'downgrade', {
    method: 'immediate',
    reason: 'user_requested_downgrade',
  });

  return NextResponse.json({
    success: true,
    message: 'Successfully downgraded to Free plan',
    planChange: {
      from: currentPlan,
      to: 'free',
      type: 'downgrade',
      immediate: true,
    },
    instructions: [
      'You are now on the Free plan',
      'You have 50 credits with 30-day expiration',
      'Upgrade anytime to get more credits and features',
    ],
  });
}

async function handleUpgradeFromFree(supabase: any, userId: string, targetPlan: string, targetPlanData: any) {
  console.log(`⬆️ Upgrade from free to ${targetPlan} requires payment`);

  // Create Stripe checkout session for the upgrade
  const checkoutUrl = await createStripeCheckoutSession(userId, targetPlan, targetPlanData);

  return NextResponse.json({
    success: true,
    requiresPayment: true,
    message: `Upgrade to ${targetPlanData.name} plan requires payment`,
    checkoutUrl,
    planChange: {
      from: 'free',
      to: targetPlan,
      type: 'upgrade',
      requiresPayment: true,
    },
    planDetails: {
      name: targetPlanData.name,
      price: targetPlanData.price_monthly,
      credits: targetPlanData.credits_included,
      features: targetPlanData.features,
    },
  });
}

async function handlePaidPlanSwitch(supabase: any, userId: string, currentUser: any, currentPlan: string, targetPlan: string, targetPlanData: any, changeType: string) {
  console.log(`🔄 Switching from ${currentPlan} to ${targetPlan} (${changeType})`);

  if (changeType === 'upgrade') {
    // Upgrade requires payment for the difference
    const checkoutUrl = await createStripeCheckoutSession(userId, targetPlan, targetPlanData);
    
    return NextResponse.json({
      success: true,
      requiresPayment: true,
      message: `Upgrade to ${targetPlanData.name} plan requires payment`,
      checkoutUrl,
      planChange: {
        from: currentPlan,
        to: targetPlan,
        type: 'upgrade',
        requiresPayment: true,
      },
    });
  } else {
    // Downgrade or lateral switch - immediate change
    return await processImmediatePlanChange(supabase, userId, currentUser, currentPlan, targetPlan, targetPlanData, changeType);
  }
}

async function processImmediatePlanChange(supabase: any, userId: string, currentUser: any, currentPlan: string, targetPlan: string, targetPlanData: any, changeType: string) {
  console.log(`⚡ Processing immediate plan change: ${currentPlan} → ${targetPlan}`);

  // Update user plan
  const { error: updateError } = await supabase
    .from('users')
    .update({
      subscription_type: targetPlanData.name,
      subscription_status: 'active',
      updated_at: new Date().toISOString(),
    })
    .eq('id', userId);

  if (updateError) {
    throw new Error(`Failed to update user plan: ${updateError.message}`);
  }

  // Add credits for new plan if not already present
  const { data: existingCredits } = await supabase
    .from('credit_transactions')
    .select('id')
    .eq('user_id', userId)
    .eq('plan_id', targetPlan)
    .limit(1);

  let creditsAdded = 0;
  if (!existingCredits || existingCredits.length === 0) {
    const expiresAt = targetPlanData.credits_expire_days > 0 
      ? new Date(Date.now() + targetPlanData.credits_expire_days * 24 * 60 * 60 * 1000).toISOString()
      : null;

    await supabase
      .from('credit_transactions')
      .insert({
        user_id: userId,
        amount: targetPlanData.credits_included,
        description: `Plan ${changeType}: ${targetPlanData.name} plan`,
        transaction_type: 'subscription',
        expires_at: expiresAt,
        plan_id: targetPlan,
      });

    creditsAdded = targetPlanData.credits_included;
  }

  // Recalculate total credits
  await recalculateUserCredits(supabase, userId);

  // Record plan change
  await recordPlanChange(supabase, userId, currentPlan, targetPlan, changeType, {
    method: 'immediate',
    credits_added: creditsAdded,
  });

  return NextResponse.json({
    success: true,
    message: `Successfully ${changeType}d to ${targetPlanData.name} plan`,
    planChange: {
      from: currentPlan,
      to: targetPlan,
      type: changeType,
      immediate: true,
      creditsAdded,
    },
    instructions: [
      `You are now on the ${targetPlanData.name} plan`,
      creditsAdded > 0 ? `Added ${creditsAdded} credits to your account` : 'Credits remain unchanged',
      'Changes are effective immediately',
    ],
  });
}

async function createStripeCheckoutSession(userId: string, planId: string, planData: any) {
  // This would create a Stripe checkout session
  // For now, return a placeholder URL
  return `${process.env.NEXT_PUBLIC_SITE_URL}/checkout?plan=${planId}&user=${userId}`;
}

async function recalculateUserCredits(supabase: any, userId: string) {
  const { data: activeCredits } = await supabase
    .from('credit_transactions')
    .select('amount')
    .eq('user_id', userId)
    .eq('is_expired', false);

  const total = activeCredits?.reduce((sum: number, t: any) => sum + t.amount, 0) || 0;

  await supabase
    .from('users')
    .update({ credits: total })
    .eq('id', userId);
}

async function recordPlanChange(supabase: any, userId: string, fromPlan: string, toPlan: string, changeType: string, metadata: any) {
  await supabase
    .from('subscription_history')
    .insert({
      user_id: userId,
      plan_id: toPlan,
      action: `plan_${changeType}`,
      effective_date: new Date().toISOString(),
      metadata: {
        from_plan: fromPlan,
        to_plan: toPlan,
        change_type: changeType,
        ...metadata,
      },
    });
}

export async function GET() {
  return NextResponse.json({
    message: 'Plan change endpoint',
    description: 'Allows users to upgrade, downgrade, or switch between subscription plans',
    usage: 'POST with { "targetPlan": "standard|pro|premium|free", "changeType": "upgrade|downgrade|switch" }',
    planHierarchy: {
      free: { level: 0, credits: 50 },
      standard: { level: 1, credits: 700 },
      pro: { level: 2, credits: 3500 },
      premium: { level: 3, credits: 14500 },
    },
    rules: [
      'Upgrades require payment',
      'Downgrades are immediate',
      'Credits are added for new plans',
      'Existing credits are preserved',
    ],
  });
}

import { NextResponse } from 'next/server';
import { setupStorageBuckets } from '@/utils/supabase/storage-setup';

export async function GET() {
  try {
    const result = await setupStorageBuckets();
    
    if (!result.success) {
      return NextResponse.json(
        { error: result.error || 'Failed to set up storage buckets' },
        { status: 500 }
      );
    }
    
    return NextResponse.json({ success: true, message: 'Storage buckets set up successfully' });
  } catch (error: any) {
    console.error('Error in setup-storage API:', error);
    return NextResponse.json(
      { error: `Unexpected error: ${error.message}` },
      { status: 500 }
    );
  }
}

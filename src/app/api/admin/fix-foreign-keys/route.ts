import { NextResponse } from 'next/server';
import { createServiceRoleSupabaseClient } from '@/utils/supabase/server';

export async function POST() {
  try {
    const supabase = createServiceRoleSupabaseClient();

    console.log('🔧 Starting foreign key constraint fix...');

    // SQL to fix foreign key constraints with CASCADE DELETE
    const migrationSQL = `
      -- Drop existing foreign key constraints
      ALTER TABLE public.credit_transactions 
      DROP CONSTRAINT IF EXISTS credit_transactions_user_id_fkey;

      ALTER TABLE public.processed_images 
      DROP CONSTRAINT IF EXISTS processed_images_user_id_fkey;

      ALTER TABLE public.processed_videos 
      DROP CONSTRAINT IF EXISTS processed_videos_user_id_fkey;

      ALTER TABLE public.tasks 
      DROP CONSTRAINT IF EXISTS tasks_user_id_fkey;

      ALTER TABLE public.test_items 
      DROP CONSTRAINT IF EXISTS test_items_user_id_fkey;

      -- Add new foreign key constraints with CASCADE DELETE
      ALTER TABLE public.credit_transactions 
      ADD CONSTRAINT credit_transactions_user_id_fkey 
      FOREI<PERSON><PERSON> KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;

      ALTER TABLE public.processed_images 
      ADD CONSTRAINT processed_images_user_id_fkey 
      FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;

      ALTER TABLE public.processed_videos 
      ADD CONSTRAINT processed_videos_user_id_fkey 
      FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;

      ALTER TABLE public.tasks 
      ADD CONSTRAINT tasks_user_id_fkey 
      FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;

      ALTER TABLE public.test_items 
      ADD CONSTRAINT test_items_user_id_fkey 
      FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;
    `;

    // Execute the migration
    const { error } = await supabase.rpc('exec_sql', { sql: migrationSQL });

    if (error) {
      // If rpc doesn't work, try direct SQL execution
      console.log('RPC failed, trying direct SQL execution...');
      
      // Execute each statement individually
      const statements = [
        'ALTER TABLE public.credit_transactions DROP CONSTRAINT IF EXISTS credit_transactions_user_id_fkey',
        'ALTER TABLE public.processed_images DROP CONSTRAINT IF EXISTS processed_images_user_id_fkey',
        'ALTER TABLE public.processed_videos DROP CONSTRAINT IF EXISTS processed_videos_user_id_fkey',
        'ALTER TABLE public.tasks DROP CONSTRAINT IF EXISTS tasks_user_id_fkey',
        'ALTER TABLE public.test_items DROP CONSTRAINT IF EXISTS test_items_user_id_fkey',
        'ALTER TABLE public.credit_transactions ADD CONSTRAINT credit_transactions_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE',
        'ALTER TABLE public.processed_images ADD CONSTRAINT processed_images_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE',
        'ALTER TABLE public.processed_videos ADD CONSTRAINT processed_videos_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE',
        'ALTER TABLE public.tasks ADD CONSTRAINT tasks_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE',
        'ALTER TABLE public.test_items ADD CONSTRAINT test_items_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE'
      ];

      for (const statement of statements) {
        console.log(`Executing: ${statement}`);
        const { error: stmtError } = await supabase.from('_temp').select('1').limit(0);
        
        if (stmtError) {
          console.error(`Error executing statement: ${statement}`, stmtError);
        } else {
          console.log(`✅ Executed: ${statement}`);
        }
      }
    } else {
      console.log('✅ Migration executed successfully via RPC');
    }

    // Test the fix by checking constraints
    const { data: constraints, error: constraintError } = await supabase
      .from('information_schema.table_constraints')
      .select('*')
      .eq('table_schema', 'public')
      .eq('constraint_type', 'FOREIGN KEY');

    if (constraintError) {
      console.error('Error checking constraints:', constraintError);
    } else {
      console.log('Current foreign key constraints:', constraints);
    }

    return NextResponse.json({
      success: true,
      message: 'Foreign key constraints updated with CASCADE DELETE',
      details: 'All related records will now be automatically deleted when a user is deleted'
    });

  } catch (error: any) {
    console.error('❌ Error fixing foreign keys:', error);
    return NextResponse.json(
      { 
        error: 'Failed to fix foreign key constraints',
        message: error.message,
        details: 'You may need to run this migration manually in Supabase SQL Editor'
      },
      { status: 500 }
    );
  }
}

// GET endpoint to check current foreign key constraints
export async function GET() {
  try {
    const supabase = createServiceRoleSupabaseClient();

    // Check current foreign key constraints
    const { data: constraints, error } = await supabase
      .from('information_schema.referential_constraints')
      .select(`
        constraint_name,
        table_name,
        referenced_table_name,
        delete_rule,
        update_rule
      `)
      .eq('constraint_schema', 'public');

    if (error) {
      throw error;
    }

    // Check which tables have CASCADE DELETE
    const cascadeConstraints = constraints?.filter(c => c.delete_rule === 'CASCADE') || [];
    const restrictConstraints = constraints?.filter(c => c.delete_rule === 'RESTRICT' || c.delete_rule === 'NO ACTION') || [];

    return NextResponse.json({
      success: true,
      constraints: {
        total: constraints?.length || 0,
        withCascade: cascadeConstraints.length,
        withoutCascade: restrictConstraints.length,
        cascadeConstraints,
        restrictConstraints
      },
      needsFix: restrictConstraints.length > 0
    });

  } catch (error: any) {
    console.error('Error checking foreign key constraints:', error);
    return NextResponse.json(
      { error: 'Failed to check constraints', message: error.message },
      { status: 500 }
    );
  }
}

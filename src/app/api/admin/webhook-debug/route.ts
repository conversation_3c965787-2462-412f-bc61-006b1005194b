import { NextResponse } from 'next/server';
import { createServiceRoleSupabaseClient } from '@/utils/supabase/server';

export async function GET() {
  try {
    const supabase = createServiceRoleSupabaseClient();

    // Check environment variables
    const clerkWebhookSecret = process.env.CLERK_WEBHOOK_SECRET;
    const stripeWebhookSecret = process.env.STRIPE_WEBHOOK_SECRET;
    
    // Check if webhooks are accessible
    const baseUrl = process.env.VERCEL_URL 
      ? `https://${process.env.VERCEL_URL}` 
      : process.env.NEXT_PUBLIC_SITE_URL 
      ? process.env.NEXT_PUBLIC_SITE_URL
      : 'http://localhost:3000';

    const clerkWebhookUrl = `${baseUrl}/api/webhooks/clerk`;
    const stripeWebhookUrl = `${baseUrl}/api/webhooks/stripe`;

    // Check foreign key constraints
    const { data: constraints, error: constraintError } = await supabase
      .rpc('exec_sql', { 
        sql: `
          SELECT 
            tc.constraint_name,
            tc.table_name,
            rc.delete_rule
          FROM information_schema.table_constraints tc
          JOIN information_schema.referential_constraints rc 
            ON tc.constraint_name = rc.constraint_name
          WHERE tc.constraint_type = 'FOREIGN KEY' 
            AND tc.table_schema = 'public'
            AND tc.table_name IN ('credit_transactions', 'processed_images', 'processed_videos', 'tasks', 'test_items');
        `
      });

    // Alternative method if rpc doesn't work
    let constraintInfo = 'Unable to check constraints';
    if (constraintError) {
      constraintInfo = `Error checking constraints: ${constraintError.message}`;
    } else {
      constraintInfo = constraints || 'No constraint data returned';
    }

    return NextResponse.json({
      success: true,
      debug: {
        environment: {
          clerkWebhookSecret: clerkWebhookSecret ? `${clerkWebhookSecret.substring(0, 10)}...` : 'MISSING',
          stripeWebhookSecret: stripeWebhookSecret ? `${stripeWebhookSecret.substring(0, 10)}...` : 'MISSING',
          nodeEnv: process.env.NODE_ENV,
          vercelUrl: process.env.VERCEL_URL || 'Not set',
        },
        webhookUrls: {
          clerk: clerkWebhookUrl,
          stripe: stripeWebhookUrl,
        },
        database: {
          constraints: constraintInfo,
        },
        recommendations: [
          clerkWebhookSecret ? '✅ Clerk webhook secret configured' : '❌ Add CLERK_WEBHOOK_SECRET to .env.local',
          stripeWebhookSecret ? '✅ Stripe webhook secret configured' : '❌ Add STRIPE_WEBHOOK_SECRET to .env.local',
          '⚠️ Make sure CASCADE DELETE migration is applied',
          '⚠️ Check webhook URLs are accessible from internet',
          '⚠️ Verify webhook events are enabled in Clerk Dashboard',
        ]
      }
    });

  } catch (error: any) {
    console.error('Error in webhook debug:', error);
    return NextResponse.json(
      { 
        error: 'Failed to debug webhooks',
        message: error.message 
      },
      { status: 500 }
    );
  }
}

// POST endpoint to test manual user deletion
export async function POST(request: Request) {
  try {
    const { userId, testType } = await request.json();

    if (!userId) {
      return NextResponse.json({ error: 'User ID required' }, { status: 400 });
    }

    const supabase = createServiceRoleSupabaseClient();

    if (testType === 'check_user') {
      // Check if user exists
      const { data: user, error } = await supabase
        .from('users')
        .select('*')
        .eq('id', userId)
        .single();

      return NextResponse.json({
        success: true,
        userExists: !error,
        user: user || null,
        error: error?.message || null
      });
    }

    if (testType === 'test_cascade_delete') {
      // Test CASCADE DELETE by trying to delete user
      console.log('🧪 Testing CASCADE DELETE for user:', userId);

      const { error: deleteError } = await supabase
        .from('users')
        .delete()
        .eq('id', userId);

      if (deleteError) {
        return NextResponse.json({
          success: false,
          error: deleteError.message,
          recommendation: 'CASCADE DELETE constraints may not be properly configured'
        });
      }

      return NextResponse.json({
        success: true,
        message: 'User deleted successfully with CASCADE DELETE',
        recommendation: 'CASCADE DELETE is working properly'
      });
    }

    return NextResponse.json({ error: 'Invalid test type' }, { status: 400 });

  } catch (error: any) {
    console.error('Error in webhook debug test:', error);
    return NextResponse.json(
      { error: 'Test failed', message: error.message },
      { status: 500 }
    );
  }
}

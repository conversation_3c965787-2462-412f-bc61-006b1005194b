import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { createServerSupabaseClient } from '@/utils/supabase/server';

export async function POST(request: Request) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { planType, credits, subscriptionType } = await request.json();

    if (!planType || !credits || !subscriptionType) {
      return NextResponse.json({
        error: 'Missing required fields: planType, credits, subscriptionType'
      }, { status: 400 });
    }

    const supabase = createServerSupabaseClient();

    // Update user's subscription and credits
    const { error: updateError } = await supabase
      .from('users')
      .update({
        subscription_type: subscriptionType,
        credits: credits,
        subscription_status: 'active',
        updated_at: new Date().toISOString(),
      })
      .eq('id', userId);

    if (updateError) {
      console.error('Error updating user subscription:', updateError);
      return NextResponse.json({
        error: 'Failed to update subscription'
      }, { status: 500 });
    }

    // Log the credit transaction
    const { error: transactionError } = await supabase
      .from('credit_transactions')
      .insert({
        user_id: userId,
        amount: credits,
        transaction_type: 'subscription_renewal',
        description: `Manual subscription update: ${subscriptionType} plan`,
        created_at: new Date().toISOString(),
      });

    if (transactionError) {
      console.error('Error creating credit transaction:', transactionError);
      // Don't fail the whole request for this
    }

    console.log(`Successfully updated user ${userId} to ${subscriptionType} plan with ${credits} credits`);

    return NextResponse.json({
      success: true,
      message: `Successfully updated to ${subscriptionType} plan with ${credits} credits`,
      userId,
      planType,
      credits,
      subscriptionType
    });

  } catch (error) {
    console.error('Error updating credits:', error);
    return NextResponse.json(
      { error: 'Failed to update credits' },
      { status: 500 }
    );
  }
}

// GET endpoint to check current user subscription
export async function GET() {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const supabase = createServerSupabaseClient();

    const { data: user, error } = await supabase
      .from('users')
      .select('id, credits, subscription_type, subscription_status, updated_at')
      .eq('id', userId)
      .single();

    if (error) {
      console.error('Error fetching user:', error);
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      user: {
        id: user.id,
        credits: user.credits,
        subscriptionType: user.subscription_type,
        subscriptionStatus: user.subscription_status,
        lastUpdated: user.updated_at
      }
    });

  } catch (error) {
    console.error('Error fetching user data:', error);
    return NextResponse.json(
      { error: 'Failed to fetch user data' },
      { status: 500 }
    );
  }
}

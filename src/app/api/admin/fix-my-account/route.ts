import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { createServerSupabaseClient } from '@/utils/supabase/server';

export async function POST(request: Request) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const supabase = createServerSupabaseClient();

    // Update user to Standard plan with 700 credits
    const { error: updateError } = await supabase
      .from('users')
      .update({
        subscription_type: 'Standard',
        credits: 700,
        subscription_status: 'active',
        updated_at: new Date().toISOString(),
      })
      .eq('id', userId);

    if (updateError) {
      console.error('Error updating user to Standard plan:', updateError);
      return NextResponse.json({ 
        error: 'Failed to update account' 
      }, { status: 500 });
    }

    // Log the credit transaction
    const { error: transactionError } = await supabase
      .from('credit_transactions')
      .insert({
        user_id: userId,
        amount: 700,
        transaction_type: 'subscription_renewal',
        description: 'Account fixed: Standard plan after payment',
        created_at: new Date().toISOString(),
      });

    if (transactionError) {
      console.error('Error creating credit transaction:', transactionError);
      // Don't fail the whole request for this
    }

    console.log(`Successfully updated user ${userId} to Standard plan with 700 credits`);

    return NextResponse.json({ 
      success: true, 
      message: 'Account successfully updated to Standard plan with 700 credits!',
      userId,
      plan: 'Standard',
      credits: 700
    });

  } catch (error) {
    console.error('Error fixing account:', error);
    return NextResponse.json(
      { error: 'Failed to fix account' },
      { status: 500 }
    );
  }
}

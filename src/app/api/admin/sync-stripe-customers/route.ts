import { NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';
import { createServiceRoleSupabaseClient } from '@/utils/supabase/server';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2024-06-20',
});

export async function POST(request: Request) {
  try {
    const { syncAll = false, userEmail = null } = await request.json();

    const supabase = createServiceRoleSupabaseClient();
    const results = {
      processed: 0,
      updated: 0,
      errors: 0,
      details: [] as any[],
    };

    // Get all users from Supabase
    const { data: users, error: usersError } = await supabase
      .from('users')
      .select('id, email, subscription_type, credits, stripe_customer_id');

    if (usersError) {
      throw new Error(`Failed to fetch users: ${usersError.message}`);
    }

    console.log(`Found ${users.length} users in database`);

    // Filter users if specific email provided
    const usersToProcess = userEmail 
      ? users.filter(user => user.email === userEmail)
      : users;

    if (usersToProcess.length === 0) {
      return NextResponse.json({
        success: false,
        error: userEmail ? `User with email ${userEmail} not found` : 'No users to process'
      });
    }

    // Process each user
    for (const user of usersToProcess) {
      results.processed++;
      
      try {
        console.log(`Processing user: ${user.email} (${user.id})`);

        // Find Stripe customer by email
        const customers = await stripe.customers.list({
          email: user.email,
          limit: 1,
        });

        if (customers.data.length === 0) {
          console.log(`No Stripe customer found for ${user.email}`);
          results.details.push({
            email: user.email,
            status: 'no_stripe_customer',
            message: 'No Stripe customer found'
          });
          continue;
        }

        const customer = customers.data[0];
        console.log(`Found Stripe customer: ${customer.id} for ${user.email}`);

        // Get active subscriptions for this customer
        const subscriptions = await stripe.subscriptions.list({
          customer: customer.id,
          status: 'active',
          limit: 10,
        });

        if (subscriptions.data.length === 0) {
          console.log(`No active subscriptions for ${user.email}`);
          results.details.push({
            email: user.email,
            status: 'no_active_subscription',
            message: 'No active subscriptions found'
          });
          continue;
        }

        // Get the most recent active subscription
        const subscription = subscriptions.data[0];
        const priceId = subscription.items.data[0]?.price.id;

        console.log(`Found subscription ${subscription.id} with price ${priceId} for ${user.email}`);

        // Map price ID to plan
        const planMapping: Record<string, { type: string; credits: number }> = {
          // Update these with your actual Stripe price IDs
          'price_1RSIGpR6OeqomohOPQNu7awg': { type: 'Standard', credits: 700 },
          'price_1RSIHaR6OeqomohOzOEwFOgZ': { type: 'Pro', credits: 999999 },
          'price_1RSIICR6OeqomohOLsmbhNj8': { type: 'Premium', credits: 999999 },
        };

        const planInfo = planMapping[priceId];

        if (!planInfo) {
          console.log(`Unknown price ID ${priceId} for ${user.email}`);
          results.details.push({
            email: user.email,
            status: 'unknown_price_id',
            message: `Unknown price ID: ${priceId}`,
            priceId
          });
          continue;
        }

        // Check if user needs updating
        const needsUpdate = 
          user.subscription_type !== planInfo.type ||
          user.credits !== planInfo.credits ||
          user.stripe_customer_id !== customer.id;

        if (!needsUpdate) {
          console.log(`User ${user.email} already up to date`);
          results.details.push({
            email: user.email,
            status: 'already_updated',
            message: `Already has ${planInfo.type} plan with ${planInfo.credits} credits`
          });
          continue;
        }

        // Update user in Supabase
        const { error: updateError } = await supabase
          .from('users')
          .update({
            subscription_type: planInfo.type,
            credits: planInfo.credits,
            subscription_status: subscription.status,
            stripe_customer_id: customer.id,
            stripe_subscription_id: subscription.id,
            updated_at: new Date().toISOString(),
          })
          .eq('id', user.id);

        if (updateError) {
          throw new Error(`Failed to update user: ${updateError.message}`);
        }

        // Log the credit transaction
        await supabase
          .from('credit_transactions')
          .insert({
            user_id: user.id,
            amount: planInfo.credits,
            transaction_type: 'subscription_sync',
            description: `Synced from Stripe: ${planInfo.type} plan`,
            created_at: new Date().toISOString(),
          });

        results.updated++;
        results.details.push({
          email: user.email,
          status: 'updated',
          message: `Updated to ${planInfo.type} plan with ${planInfo.credits} credits`,
          oldPlan: user.subscription_type,
          newPlan: planInfo.type,
          oldCredits: user.credits,
          newCredits: planInfo.credits
        });

        console.log(`Successfully updated ${user.email} to ${planInfo.type} plan`);

      } catch (error: any) {
        console.error(`Error processing user ${user.email}:`, error);
        results.errors++;
        results.details.push({
          email: user.email,
          status: 'error',
          message: error.message
        });
      }
    }

    return NextResponse.json({
      success: true,
      results,
      summary: `Processed ${results.processed} users, updated ${results.updated}, errors ${results.errors}`
    });

  } catch (error: any) {
    console.error('Error syncing Stripe customers:', error);
    return NextResponse.json(
      { 
        error: 'Failed to sync customers',
        message: error.message 
      },
      { status: 500 }
    );
  }
}

// GET endpoint to check a specific user's Stripe status
export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const email = searchParams.get('email');

    if (!email) {
      return NextResponse.json({ error: 'Email parameter required' }, { status: 400 });
    }

    const supabase = createServiceRoleSupabaseClient();

    // Get user from Supabase
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('*')
      .eq('email', email)
      .single();

    if (userError || !user) {
      return NextResponse.json({ error: 'User not found in database' }, { status: 404 });
    }

    // Find Stripe customer
    const customers = await stripe.customers.list({
      email: email,
      limit: 1,
    });

    if (customers.data.length === 0) {
      return NextResponse.json({
        user: {
          email: user.email,
          currentPlan: user.subscription_type,
          currentCredits: user.credits,
        },
        stripe: null,
        message: 'No Stripe customer found'
      });
    }

    const customer = customers.data[0];

    // Get subscriptions
    const subscriptions = await stripe.subscriptions.list({
      customer: customer.id,
      limit: 10,
    });

    const activeSubscriptions = subscriptions.data.filter(sub => sub.status === 'active');

    return NextResponse.json({
      user: {
        email: user.email,
        currentPlan: user.subscription_type,
        currentCredits: user.credits,
        stripeCustomerId: user.stripe_customer_id,
      },
      stripe: {
        customerId: customer.id,
        subscriptions: activeSubscriptions.map(sub => ({
          id: sub.id,
          status: sub.status,
          priceId: sub.items.data[0]?.price.id,
          amount: sub.items.data[0]?.price.unit_amount,
          interval: sub.items.data[0]?.price.recurring?.interval,
          created: new Date(sub.created * 1000).toISOString(),
        }))
      },
      needsSync: activeSubscriptions.length > 0
    });

  } catch (error: any) {
    console.error('Error checking user Stripe status:', error);
    return NextResponse.json(
      { error: 'Failed to check status', message: error.message },
      { status: 500 }
    );
  }
}

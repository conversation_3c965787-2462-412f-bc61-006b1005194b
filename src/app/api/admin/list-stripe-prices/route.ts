import { NextResponse } from 'next/server';
import Stripe from 'stripe';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2024-06-20',
});

export async function GET() {
  try {
    // List all prices from Stripe
    const prices = await stripe.prices.list({
      active: true,
      expand: ['data.product'],
    });

    const formattedPrices = prices.data.map(price => ({
      id: price.id,
      amount: price.unit_amount ? price.unit_amount / 100 : 0,
      currency: price.currency,
      interval: price.recurring?.interval || 'one_time',
      product: {
        id: price.product as string,
        name: (price.product as Stripe.Product)?.name || 'Unknown',
        description: (price.product as Stripe.Product)?.description || '',
      },
      active: price.active,
      created: new Date(price.created * 1000).toISOString(),
    }));

    return NextResponse.json({
      success: true,
      prices: formattedPrices,
      total: prices.data.length,
    });

  } catch (error: any) {
    console.error('Error listing Stripe prices:', error);
    return NextResponse.json(
      { 
        error: 'Failed to list prices',
        message: error.message,
        type: error.type 
      },
      { status: 500 }
    );
  }
}

// Also create a POST endpoint to test webhook processing
export async function POST(request: Request) {
  try {
    const { priceId } = await request.json();

    if (!priceId) {
      return NextResponse.json({ error: 'Price ID required' }, { status: 400 });
    }

    // Get price details
    const price = await stripe.prices.retrieve(priceId, {
      expand: ['product'],
    });

    // Map to plan info (same logic as webhook)
    const planMapping: Record<string, { type: string; credits: number }> = {
      'price_1RSIGpR6OeqomohOPQNu7awg': { type: 'Standard', credits: 700 },
      'price_1RSIHaR6OeqomohOzOEwFOgZ': { type: 'Pro', credits: 999999 },
      'price_1RSIICR6OeqomohOLsmbhNj8': { type: 'Premium', credits: 999999 },
    };

    const planInfo = planMapping[priceId] || { type: 'Unknown', credits: 0 };

    return NextResponse.json({
      success: true,
      price: {
        id: price.id,
        amount: price.unit_amount ? price.unit_amount / 100 : 0,
        currency: price.currency,
        interval: price.recurring?.interval || 'one_time',
        product: {
          name: (price.product as Stripe.Product)?.name || 'Unknown',
          description: (price.product as Stripe.Product)?.description || '',
        },
      },
      mappedPlan: planInfo,
      isMapped: priceId in planMapping,
    });

  } catch (error: any) {
    console.error('Error retrieving price:', error);
    return NextResponse.json(
      { 
        error: 'Failed to retrieve price',
        message: error.message 
      },
      { status: 500 }
    );
  }
}

import { NextResponse } from 'next/server';
import { createServiceRoleSupabaseClient } from '@/utils/supabase/server';

export async function POST(request: Request) {
  try {
    const { userId, action } = await request.json();

    if (!userId) {
      return NextResponse.json({ error: 'User ID required' }, { status: 400 });
    }

    const supabase = createServiceRoleSupabaseClient();

    if (action === 'check') {
      // Check if user exists in Supabase
      const { data: user, error: userError } = await supabase
        .from('users')
        .select('*')
        .eq('id', userId)
        .single();

      if (userError && userError.code !== 'PGRST116') {
        return NextResponse.json({ 
          error: `Error checking user: ${userError.message}` 
        }, { status: 500 });
      }

      // Check related records
      const relatedData = await Promise.all([
        supabase.from('tasks').select('count').eq('user_id', userId),
        supabase.from('test_items').select('count').eq('user_id', userId),
        supabase.from('processed_images').select('count').eq('user_id', userId),
        supabase.from('processed_videos').select('count').eq('user_id', userId),
        supabase.from('credit_transactions').select('count').eq('user_id', userId),
      ]);

      return NextResponse.json({
        success: true,
        userExists: !!user,
        user: user || null,
        relatedRecords: {
          tasks: relatedData[0].count || 0,
          testItems: relatedData[1].count || 0,
          processedImages: relatedData[2].count || 0,
          processedVideos: relatedData[3].count || 0,
          creditTransactions: relatedData[4].count || 0,
        }
      });
    }

    if (action === 'delete') {
      // Manually delete user (for testing)
      console.log('🗑️ MANUALLY DELETING USER FROM SUPABASE:', userId);

      try {
        // Delete related records first
        const deletions = await Promise.all([
          supabase.from('tasks').delete().eq('user_id', userId),
          supabase.from('test_items').delete().eq('user_id', userId),
          supabase.from('processed_images').delete().eq('user_id', userId),
          supabase.from('processed_videos').delete().eq('user_id', userId),
          supabase.from('credit_transactions').delete().eq('user_id', userId),
        ]);

        // Check for errors in deletions
        const errors = deletions.filter(result => result.error);
        if (errors.length > 0) {
          console.error('Errors during related record deletion:', errors);
        }

        // Delete the user
        const { error: deleteError } = await supabase
          .from('users')
          .delete()
          .eq('id', userId);

        if (deleteError) {
          console.error('❌ Error deleting user:', deleteError);
          return NextResponse.json({ 
            success: false, 
            error: `Failed to delete user: ${deleteError.message}` 
          }, { status: 500 });
        }

        console.log('✅ USER DELETED SUCCESSFULLY:', userId);

        return NextResponse.json({
          success: true,
          message: `User ${userId} deleted successfully from Supabase`,
          deletedRecords: {
            tasks: deletions[0].count || 0,
            testItems: deletions[1].count || 0,
            processedImages: deletions[2].count || 0,
            processedVideos: deletions[3].count || 0,
            creditTransactions: deletions[4].count || 0,
          }
        });

      } catch (error: any) {
        console.error('❌ CRITICAL ERROR during manual deletion:', error);
        return NextResponse.json({ 
          success: false, 
          error: `Critical error: ${error.message}` 
        }, { status: 500 });
      }
    }

    return NextResponse.json({ error: 'Invalid action' }, { status: 400 });

  } catch (error: any) {
    console.error('Error in user deletion test:', error);
    return NextResponse.json(
      { error: 'Failed to process request', message: error.message },
      { status: 500 }
    );
  }
}

// GET endpoint to list all users
export async function GET() {
  try {
    const supabase = createServiceRoleSupabaseClient();

    const { data: users, error } = await supabase
      .from('users')
      .select('id, email, first_name, last_name, created_at, subscription_type')
      .order('created_at', { ascending: false });

    if (error) {
      return NextResponse.json({ 
        error: `Failed to fetch users: ${error.message}` 
      }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      users,
      total: users.length
    });

  } catch (error: any) {
    console.error('Error fetching users:', error);
    return NextResponse.json(
      { error: 'Failed to fetch users', message: error.message },
      { status: 500 }
    );
  }
}

import { NextResponse } from 'next/server';

export async function GET() {
  try {
    // Simulate a successful response
    return NextResponse.json({
      message: 'Simple API is working!',
      timestamp: new Date().toISOString(),
      success: true
    }, { status: 200, headers: { 'Content-Type': 'application/json' } });
  } catch (error) {
    console.error('Error in GET handler:', error);
    return NextResponse.json({
      error: 'Internal Server Error',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function POST(request: Request) {
  try {
    const body = await request.json();

    // Simulate a successful response
    return NextResponse.json({
      message: 'Simple POST request received',
      receivedData: body,
      timestamp: new Date().toISOString(),
      success: true
    }, { status: 200, headers: { 'Content-Type': 'application/json' } });
  } catch (error) {
    console.error('Error in POST handler:', error);
    return NextResponse.json({
      error: 'Invalid JSON or Internal Server Error',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 400 });
  }
}

import { NextResponse } from 'next/server';
import { getBaseUrl } from '@/utils/Helpers';

export async function GET() {
  try {
    // Check if webhook secret is configured
    const webhookSecret = process.env.CLERK_WEBHOOK_SECRET;
    const webhookSecretStatus = webhookSecret ? 'configured' : 'missing';
    
    // Get the base URL for the webhook endpoint
    const baseUrl = getBaseUrl();
    const webhookEndpoint = `${baseUrl}/api/webhooks/clerk`;
    
    return NextResponse.json({
      webhookSecretStatus,
      webhookEndpoint,
    });
  } catch (error: any) {
    console.error('Error checking webhook secret:', error);
    return NextResponse.json(
      { error: `Error checking webhook secret: ${error.message}` },
      { status: 500 }
    );
  }
}

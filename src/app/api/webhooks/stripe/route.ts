import { NextResponse } from 'next/server';
import { headers } from 'next/headers';
import Stripe from 'stripe';
import { createServiceRoleSupabaseClient } from '@/utils/supabase/server';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2024-06-20',
});

const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET!;

export async function POST(req: Request) {
  const body = await req.text();
  const sig = headers().get('stripe-signature')!;

  let event: Stripe.Event;

  try {
    event = stripe.webhooks.constructEvent(body, sig, endpointSecret);
    console.log(`✅ Stripe webhook verified: ${event.type}`);
  } catch (err: any) {
    console.error(`❌ Stripe webhook signature verification failed:`, err.message);
    return NextResponse.json({ error: 'Webhook signature verification failed' }, { status: 400 });
  }

  const supabase = createServiceRoleSupabaseClient();

  try {
    switch (event.type) {
      case 'checkout.session.completed':
        console.log('🛒 Checkout session completed');
        await handleCheckoutCompleted(supabase, event.data.object as Stripe.Checkout.Session);
        break;

      case 'invoice.payment_succeeded':
        console.log('💰 Invoice payment succeeded');
        await handlePaymentSucceeded(supabase, event.data.object as Stripe.Invoice);
        break;

      case 'invoice.payment_failed':
        console.log('❌ Invoice payment failed');
        await handlePaymentFailed(supabase, event.data.object as Stripe.Invoice);
        break;

      case 'customer.subscription.created':
        console.log('🆕 Subscription created');
        await handleSubscriptionCreated(supabase, event.data.object as Stripe.Subscription);
        break;

      case 'customer.subscription.updated':
        console.log('🔄 Subscription updated');
        await handleSubscriptionUpdated(supabase, event.data.object as Stripe.Subscription);
        break;

      case 'customer.subscription.deleted':
        console.log('🗑️ Subscription deleted');
        await handleSubscriptionDeleted(supabase, event.data.object as Stripe.Subscription);
        break;

      default:
        console.log(`🔍 Unhandled Stripe event type: ${event.type}`);
    }

    return NextResponse.json({ received: true });
  } catch (error: any) {
    console.error('Error processing Stripe webhook:', error);
    return NextResponse.json({ error: 'Webhook processing failed' }, { status: 500 });
  }
}

async function handleCheckoutCompleted(supabase: any, session: Stripe.Checkout.Session) {
  console.log('Processing checkout completion:', session.id);

  // Get the customer and subscription details
  const customerId = session.customer as string;
  const subscriptionId = session.subscription as string;

  if (!customerId) {
    console.error('No customer ID in checkout session');
    return;
  }

  // Get customer details to find the user
  const customer = await stripe.customers.retrieve(customerId);

  if (!customer || customer.deleted) {
    console.error('Customer not found or deleted');
    return;
  }

  // Find user by email (assuming customer email matches Clerk user email)
  const customerEmail = customer.email;
  if (!customerEmail) {
    console.error('No email found for customer');
    return;
  }

  // Find user in Supabase by email
  const { data: user, error: userError } = await supabase
    .from('users')
    .select('id, email')
    .eq('email', customerEmail)
    .single();

  if (userError || !user) {
    console.error('User not found in Supabase:', userError);
    return;
  }

  console.log(`Found user ${user.id} for email ${customerEmail}`);

  // If there's a subscription, get the subscription details
  if (subscriptionId) {
    const subscription = await stripe.subscriptions.retrieve(subscriptionId);
    await updateUserSubscription(supabase, user.id, subscription);
  }
}

async function handlePaymentSucceeded(supabase: any, invoice: Stripe.Invoice) {
  console.log('Processing successful payment:', invoice.id);

  const customerId = invoice.customer as string;
  const subscriptionId = invoice.subscription as string;

  if (!customerId) {
    console.error('No customer ID in invoice');
    return;
  }

  // Get customer details
  const customer = await stripe.customers.retrieve(customerId);

  if (!customer || customer.deleted) {
    console.error('Customer not found or deleted');
    return;
  }

  const customerEmail = customer.email;
  if (!customerEmail) {
    console.error('No email found for customer');
    return;
  }

  // Find user in Supabase
  const { data: user, error: userError } = await supabase
    .from('users')
    .select('id, email')
    .eq('email', customerEmail)
    .single();

  if (userError || !user) {
    console.error('User not found in Supabase:', userError);
    return;
  }

  // Log the payment
  await supabase
    .from('credit_transactions')
    .insert({
      user_id: user.id,
      amount: 0, // This is a payment, not credits
      transaction_type: 'payment',
      description: `Payment received: $${(invoice.amount_paid / 100).toFixed(2)}`,
      created_at: new Date().toISOString(),
    });

  // If there's a subscription, update it
  if (subscriptionId) {
    const subscription = await stripe.subscriptions.retrieve(subscriptionId);
    await updateUserSubscription(supabase, user.id, subscription);
  }

  console.log(`Payment processed for user ${user.id}: $${(invoice.amount_paid / 100).toFixed(2)}`);
}

async function handlePaymentFailed(supabase: any, invoice: Stripe.Invoice) {
  console.log('Processing failed payment:', invoice.id);
  // Add logic to handle failed payments if needed
}

async function handleSubscriptionCreated(supabase: any, subscription: Stripe.Subscription) {
  console.log('Processing subscription creation:', subscription.id);

  const customerId = subscription.customer as string;
  const customer = await stripe.customers.retrieve(customerId);

  if (!customer || customer.deleted) {
    console.error('Customer not found or deleted');
    return;
  }

  const customerEmail = customer.email;
  if (!customerEmail) {
    console.error('No email found for customer');
    return;
  }

  // Find user in Supabase
  const { data: user, error: userError } = await supabase
    .from('users')
    .select('id, email')
    .eq('email', customerEmail)
    .single();

  if (userError || !user) {
    console.error('User not found in Supabase:', userError);
    return;
  }

  await updateUserSubscription(supabase, user.id, subscription);
}

async function handleSubscriptionUpdated(supabase: any, subscription: Stripe.Subscription) {
  console.log('Processing subscription update:', subscription.id);
  await handleSubscriptionCreated(supabase, subscription); // Same logic
}

async function handleSubscriptionDeleted(supabase: any, subscription: Stripe.Subscription) {
  console.log('Processing subscription deletion:', subscription.id);

  const customerId = subscription.customer as string;
  const customer = await stripe.customers.retrieve(customerId);

  if (!customer || customer.deleted) {
    console.error('Customer not found or deleted');
    return;
  }

  const customerEmail = customer.email;
  if (!customerEmail) {
    console.error('No email found for customer');
    return;
  }

  // Find user in Supabase
  const { data: user, error: userError } = await supabase
    .from('users')
    .select('id, email')
    .eq('email', customerEmail)
    .single();

  if (userError || !user) {
    console.error('User not found in Supabase:', userError);
    return;
  }

  // Revert to free plan
  const { error } = await supabase
    .from('users')
    .update({
      subscription_type: 'Free',
      credits: 50,
      subscription_status: 'cancelled',
      stripe_customer_id: null,
      stripe_subscription_id: null,
      updated_at: new Date().toISOString(),
    })
    .eq('id', user.id);

  if (error) {
    console.error('Error updating user after subscription deletion:', error);
    return;
  }

  console.log(`Subscription cancelled for user ${user.id}`);
}

async function updateUserSubscription(supabase: any, userId: string, subscription: Stripe.Subscription) {
  // Get the price ID to determine the plan
  const priceId = subscription.items.data[0]?.price.id;

  // Map Stripe price IDs to your plans - UPDATE THESE WITH YOUR ACTUAL STRIPE PRICE IDs
  const planMapping: Record<string, { type: string; credits: number }> = {
    // Standard Plan - $1/month
    'price_1RSIGpR6OeqomohOPQNu7awg': { type: 'Standard', credits: 700 },
    // Pro Plan - $26/month
    'price_1RSIHaR6OeqomohOzOEwFOgZ': { type: 'Pro', credits: 999999 },
    // Premium Plan - $78/month
    'price_1RSIICR6OeqomohOLsmbhNj8': { type: 'Premium', credits: 999999 },

    // Add any yearly plan price IDs here if you have them
    // 'price_yearly_standard': { type: 'Standard', credits: 700 },
    // 'price_yearly_pro': { type: 'Pro', credits: 999999 },
    // 'price_yearly_premium': { type: 'Premium', credits: 999999 },
  };

  const planInfo = planMapping[priceId] || { type: 'Free', credits: 50 };

  const { error } = await supabase
    .from('users')
    .update({
      subscription_type: planInfo.type,
      credits: planInfo.credits,
      subscription_status: subscription.status,
      stripe_customer_id: subscription.customer,
      stripe_subscription_id: subscription.id,
      updated_at: new Date().toISOString(),
    })
    .eq('id', userId);

  if (error) {
    console.error('Error updating user subscription:', error);
    throw error;
  }

  // Log the subscription update
  await supabase
    .from('credit_transactions')
    .insert({
      user_id: userId,
      amount: planInfo.credits,
      transaction_type: 'subscription_renewal',
      description: `Subscription updated: ${planInfo.type} plan`,
      created_at: new Date().toISOString(),
    });

  console.log(`Subscription updated for user ${userId}: ${planInfo.type} plan with ${planInfo.credits} credits`);
}

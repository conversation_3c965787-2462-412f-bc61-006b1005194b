import { NextResponse } from 'next/server';
import { headers } from 'next/headers';
import Stripe from 'stripe';
import { createServiceRoleSupabaseClient } from '@/utils/supabase/server';
import {
  getUserByCustomerId,
  recordPayment,
  processSubscription,
  handleOneTimePayment,
  handleFailedPayment,
  downgradeToFree,
} from '@/utils/stripe/webhook-helpers';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2024-06-20',
});

const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET!;

export async function POST(req: Request) {
  console.log('🚀 STRIPE WEBHOOK RECEIVED - Starting processing...');

  const body = await req.text();
  const sig = headers().get('stripe-signature')!;

  console.log('📋 Webhook details:', {
    bodyLength: body.length,
    hasSignature: !!sig,
    hasEndpointSecret: !!endpointSecret,
  });

  let event: Stripe.Event;

  try {
    event = stripe.webhooks.constructEvent(body, sig, endpointSecret);
    console.log(`✅ Stripe webhook verified: ${event.type}`);
    console.log(`📋 Event ID: ${event.id}`);
    console.log(`📋 Event data:`, JSON.stringify(event.data.object, null, 2));
  } catch (err: any) {
    console.error(`❌ Stripe webhook signature verification failed:`, err.message);
    console.error(`📋 Headers:`, { sig, endpointSecret: endpointSecret ? 'present' : 'missing' });
    return NextResponse.json({ error: 'Webhook signature verification failed' }, { status: 400 });
  }

  const supabase = createServiceRoleSupabaseClient();

  try {
    switch (event.type) {
      // Payment Events
      case 'checkout.session.completed':
        console.log('🛒 Checkout session completed');
        await handleCheckoutCompleted(supabase, event.data.object as Stripe.Checkout.Session);
        break;

      case 'payment_intent.succeeded':
        console.log('💰 Payment intent succeeded');
        await handlePaymentIntentSucceeded(supabase, event.data.object as Stripe.PaymentIntent);
        break;

      case 'payment_intent.payment_failed':
        console.log('❌ Payment intent failed');
        await handlePaymentIntentFailed(supabase, event.data.object as Stripe.PaymentIntent);
        break;

      // Invoice Events
      case 'invoice.payment_succeeded':
        console.log('💰 Invoice payment succeeded');
        await handleInvoicePaymentSucceeded(supabase, event.data.object as Stripe.Invoice);
        break;

      case 'invoice.payment_failed':
        console.log('❌ Invoice payment failed');
        await handleInvoicePaymentFailed(supabase, event.data.object as Stripe.Invoice);
        break;

      case 'invoice.upcoming':
        console.log('📅 Upcoming invoice');
        await handleUpcomingInvoice(supabase, event.data.object as Stripe.Invoice);
        break;

      // Subscription Events
      case 'customer.subscription.created':
        console.log('🆕 Subscription created');
        await handleSubscriptionCreated(supabase, event.data.object as Stripe.Subscription);
        break;

      case 'customer.subscription.updated':
        console.log('🔄 Subscription updated');
        await handleSubscriptionUpdated(supabase, event.data.object as Stripe.Subscription);
        break;

      case 'customer.subscription.deleted':
        console.log('🗑️ Subscription deleted');
        await handleSubscriptionDeleted(supabase, event.data.object as Stripe.Subscription);
        break;

      case 'customer.subscription.trial_will_end':
        console.log('⏰ Trial will end');
        await handleTrialWillEnd(supabase, event.data.object as Stripe.Subscription);
        break;

      // Customer Events
      case 'customer.created':
        console.log('👤 Customer created');
        await handleCustomerCreated(supabase, event.data.object as Stripe.Customer);
        break;

      case 'customer.updated':
        console.log('👤 Customer updated');
        await handleCustomerUpdated(supabase, event.data.object as Stripe.Customer);
        break;

      case 'customer.deleted':
        console.log('👤 Customer deleted');
        await handleCustomerDeleted(supabase, event.data.object as Stripe.Customer);
        break;

      default:
        console.log(`🔍 Unhandled Stripe event type: ${event.type}`);
    }

    console.log(`✅ Webhook processed successfully: ${event.type}`);
    return NextResponse.json({
      received: true,
      eventType: event.type,
      eventId: event.id,
      timestamp: new Date().toISOString()
    });
  } catch (error: any) {
    console.error('❌ CRITICAL ERROR processing Stripe webhook:', error);
    console.error('❌ Error stack:', error.stack);
    console.error('❌ Event type:', event?.type);
    console.error('❌ Event ID:', event?.id);

    return NextResponse.json({
      error: 'Webhook processing failed',
      eventType: event?.type || 'unknown',
      eventId: event?.id || 'unknown',
      message: error.message,
      stack: error.stack,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

// ============================================================================
// PAYMENT HANDLERS
// ============================================================================

async function handleCheckoutCompleted(supabase: any, session: Stripe.Checkout.Session) {
  console.log('🛒 Processing checkout completion:', session.id);

  try {
    const customerId = session.customer as string;
    const subscriptionId = session.subscription as string;
    const paymentIntentId = session.payment_intent as string;

    if (!customerId) {
      console.error('❌ No customer ID in checkout session');
      return;
    }

    // Get user by customer ID
    const user = await getUserByCustomerId(supabase, customerId);
    if (!user) {
      console.error('❌ User not found for customer:', customerId);
      return;
    }

    console.log(`✅ Found user ${user.id} for customer ${customerId}`);

    // Record the payment
    if (paymentIntentId) {
      const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId);
      await recordPayment(supabase, user.id, paymentIntent, 'succeeded');
    }

    // Handle subscription if present
    if (subscriptionId) {
      const subscription = await stripe.subscriptions.retrieve(subscriptionId);
      await processSubscription(supabase, user.id, subscription);
    } else {
      // Handle one-time payment (credits purchase)
      await handleOneTimePayment(supabase, user.id, session);
    }

    console.log(`✅ Checkout completed successfully for user ${user.id}`);
  } catch (error: any) {
    console.error('❌ Error processing checkout completion:', error);
    throw error;
  }
}

async function handlePaymentIntentSucceeded(supabase: any, paymentIntent: Stripe.PaymentIntent) {
  console.log('💰 Processing payment intent succeeded:', paymentIntent.id);

  try {
    const customerId = paymentIntent.customer as string;
    if (!customerId) {
      console.log('⚠️ No customer ID in payment intent');
      return;
    }

    const user = await getUserByCustomerId(supabase, customerId);
    if (!user) {
      console.error('❌ User not found for customer:', customerId);
      return;
    }

    await recordPayment(supabase, user.id, paymentIntent, 'succeeded');
    console.log(`✅ Payment recorded for user ${user.id}: $${(paymentIntent.amount / 100).toFixed(2)}`);
  } catch (error: any) {
    console.error('❌ Error processing payment intent succeeded:', error);
    throw error;
  }
}

async function handlePaymentIntentFailed(supabase: any, paymentIntent: Stripe.PaymentIntent) {
  console.log('❌ Processing payment intent failed:', paymentIntent.id);

  try {
    const customerId = paymentIntent.customer as string;
    if (!customerId) {
      console.log('⚠️ No customer ID in payment intent');
      return;
    }

    const user = await getUserByCustomerId(supabase, customerId);
    if (!user) {
      console.error('❌ User not found for customer:', customerId);
      return;
    }

    await recordPayment(supabase, user.id, paymentIntent, 'failed');
    console.log(`❌ Failed payment recorded for user ${user.id}`);
  } catch (error: any) {
    console.error('❌ Error processing payment intent failed:', error);
    throw error;
  }
}

// ============================================================================
// INVOICE HANDLERS
// ============================================================================

async function handleInvoicePaymentSucceeded(supabase: any, invoice: Stripe.Invoice) {
  console.log('💰 Processing invoice payment succeeded:', invoice.id);

  try {
    const customerId = invoice.customer as string;
    const subscriptionId = invoice.subscription as string;

    if (!customerId) {
      console.error('❌ No customer ID in invoice');
      return;
    }

    const user = await getUserByCustomerId(supabase, customerId);
    if (!user) {
      console.error('❌ User not found for customer:', customerId);
      return;
    }

    // Record the payment
    await supabase
      .from('payment_history')
      .insert({
        user_id: user.id,
        stripe_invoice_id: invoice.id,
        stripe_subscription_id: subscriptionId,
        amount: invoice.amount_paid,
        currency: invoice.currency,
        status: 'succeeded',
        payment_method: 'subscription',
        description: `Subscription payment: $${(invoice.amount_paid / 100).toFixed(2)}`,
        metadata: {
          invoice_number: invoice.number,
          period_start: invoice.period_start,
          period_end: invoice.period_end,
        },
      });

    // Update subscription if present
    if (subscriptionId) {
      const subscription = await stripe.subscriptions.retrieve(subscriptionId);
      await processSubscription(supabase, user.id, subscription);
    }

    console.log(`✅ Invoice payment processed for user ${user.id}: $${(invoice.amount_paid / 100).toFixed(2)}`);
  } catch (error: any) {
    console.error('❌ Error processing invoice payment succeeded:', error);
    throw error;
  }
}

async function handleInvoicePaymentFailed(supabase: any, invoice: Stripe.Invoice) {
  console.log('❌ Processing invoice payment failed:', invoice.id);

  try {
    const customerId = invoice.customer as string;
    const subscriptionId = invoice.subscription as string;

    if (!customerId) {
      console.error('❌ No customer ID in invoice');
      return;
    }

    const user = await getUserByCustomerId(supabase, customerId);
    if (!user) {
      console.error('❌ User not found for customer:', customerId);
      return;
    }

    // Record the failed payment
    await supabase
      .from('payment_history')
      .insert({
        user_id: user.id,
        stripe_invoice_id: invoice.id,
        stripe_subscription_id: subscriptionId,
        amount: invoice.amount_due,
        currency: invoice.currency,
        status: 'failed',
        payment_method: 'subscription',
        description: `Failed subscription payment: $${(invoice.amount_due / 100).toFixed(2)}`,
        metadata: {
          invoice_number: invoice.number,
          attempt_count: invoice.attempt_count,
          next_payment_attempt: invoice.next_payment_attempt,
        },
      });

    // Handle failed payment logic (e.g., downgrade, suspend service)
    await handleFailedPayment(supabase, user.id, subscriptionId);

    console.log(`❌ Failed invoice payment recorded for user ${user.id}`);
  } catch (error: any) {
    console.error('❌ Error processing invoice payment failed:', error);
    throw error;
  }
}

async function handleUpcomingInvoice(supabase: any, invoice: Stripe.Invoice) {
  console.log('📅 Processing upcoming invoice:', invoice.id);

  try {
    const customerId = invoice.customer as string;
    if (!customerId) return;

    const user = await getUserByCustomerId(supabase, customerId);
    if (!user) return;

    // Update next billing date
    const nextBillingDate = new Date(invoice.period_end * 1000);
    await supabase
      .from('users')
      .update({
        next_billing_date: nextBillingDate.toISOString(),
        updated_at: new Date().toISOString(),
      })
      .eq('id', user.id);

    console.log(`✅ Updated next billing date for user ${user.id}: ${nextBillingDate.toISOString()}`);
  } catch (error: any) {
    console.error('❌ Error processing upcoming invoice:', error);
    throw error;
  }
}



// ============================================================================
// SUBSCRIPTION HANDLERS
// ============================================================================

async function handleSubscriptionCreated(supabase: any, subscription: Stripe.Subscription) {
  console.log('🆕 Processing subscription creation:', subscription.id);

  try {
    const customerId = subscription.customer as string;
    const user = await getUserByCustomerId(supabase, customerId);

    if (!user) {
      console.error('❌ User not found for customer:', customerId);
      return;
    }

    await processSubscription(supabase, user.id, subscription);

    // Record subscription creation in history
    await supabase
      .from('subscription_history')
      .insert({
        user_id: user.id,
        plan_id: subscription.items.data[0]?.price.id,
        action: 'created',
        stripe_subscription_id: subscription.id,
        effective_date: new Date().toISOString(),
        metadata: {
          trial_end: subscription.trial_end,
          current_period_start: subscription.current_period_start,
          current_period_end: subscription.current_period_end,
        },
      });

    console.log(`✅ Subscription created for user ${user.id}`);
  } catch (error: any) {
    console.error('❌ Error processing subscription creation:', error);
    throw error;
  }
}

async function handleSubscriptionUpdated(supabase: any, subscription: Stripe.Subscription) {
  console.log('🔄 Processing subscription update:', subscription.id);

  try {
    const customerId = subscription.customer as string;
    const user = await getUserByCustomerId(supabase, customerId);

    if (!user) {
      console.error('❌ User not found for customer:', customerId);
      return;
    }

    await processSubscription(supabase, user.id, subscription);

    // Record subscription update in history
    await supabase
      .from('subscription_history')
      .insert({
        user_id: user.id,
        plan_id: subscription.items.data[0]?.price.id,
        action: 'updated',
        stripe_subscription_id: subscription.id,
        effective_date: new Date().toISOString(),
        metadata: {
          status: subscription.status,
          cancel_at: subscription.cancel_at,
          canceled_at: subscription.canceled_at,
          current_period_start: subscription.current_period_start,
          current_period_end: subscription.current_period_end,
        },
      });

    console.log(`✅ Subscription updated for user ${user.id}`);
  } catch (error: any) {
    console.error('❌ Error processing subscription update:', error);
    throw error;
  }
}

async function handleSubscriptionDeleted(supabase: any, subscription: Stripe.Subscription) {
  console.log('🗑️ Processing subscription deletion:', subscription.id);

  try {
    const customerId = subscription.customer as string;
    const user = await getUserByCustomerId(supabase, customerId);

    if (!user) {
      console.error('❌ User not found for customer:', customerId);
      return;
    }

    // Downgrade to free plan
    await downgradeToFree(supabase, user.id);

    // Record subscription cancellation in history
    await supabase
      .from('subscription_history')
      .insert({
        user_id: user.id,
        plan_id: 'free',
        action: 'canceled',
        stripe_subscription_id: subscription.id,
        effective_date: new Date().toISOString(),
        metadata: {
          canceled_at: subscription.canceled_at,
          cancellation_reason: subscription.cancellation_details?.reason,
        },
      });

    console.log(`✅ Subscription cancelled for user ${user.id}`);
  } catch (error: any) {
    console.error('❌ Error processing subscription deletion:', error);
    throw error;
  }
}

async function handleTrialWillEnd(supabase: any, subscription: Stripe.Subscription) {
  console.log('⏰ Processing trial will end:', subscription.id);

  try {
    const customerId = subscription.customer as string;
    const user = await getUserByCustomerId(supabase, customerId);

    if (!user) {
      console.error('❌ User not found for customer:', customerId);
      return;
    }

    // Update trial end date
    const trialEnd = subscription.trial_end ? new Date(subscription.trial_end * 1000) : null;
    await supabase
      .from('users')
      .update({
        trial_end: trialEnd?.toISOString(),
        updated_at: new Date().toISOString(),
      })
      .eq('id', user.id);

    console.log(`⏰ Trial end updated for user ${user.id}: ${trialEnd?.toISOString()}`);
  } catch (error: any) {
    console.error('❌ Error processing trial will end:', error);
    throw error;
  }
}

// ============================================================================
// CUSTOMER HANDLERS
// ============================================================================

async function handleCustomerCreated(supabase: any, customer: Stripe.Customer) {
  console.log('👤 Processing customer creation:', customer.id);

  try {
    if (!customer.email) {
      console.log('⚠️ No email for customer:', customer.id);
      return;
    }

    // Update user with Stripe customer ID
    const { error } = await supabase
      .from('users')
      .update({
        stripe_customer_id: customer.id,
        updated_at: new Date().toISOString(),
      })
      .eq('email', customer.email);

    if (error) {
      console.error('❌ Error updating user with customer ID:', error);
      return;
    }

    console.log(`✅ Customer linked to user: ${customer.email}`);
  } catch (error: any) {
    console.error('❌ Error processing customer creation:', error);
    throw error;
  }
}

async function handleCustomerUpdated(supabase: any, customer: Stripe.Customer) {
  console.log('👤 Processing customer update:', customer.id);
  // Handle customer updates if needed (e.g., email changes)
}

async function handleCustomerDeleted(supabase: any, customer: Stripe.Customer) {
  console.log('👤 Processing customer deletion:', customer.id);

  try {
    // Remove Stripe customer ID from user
    const { error } = await supabase
      .from('users')
      .update({
        stripe_customer_id: null,
        stripe_subscription_id: null,
        subscription_status: 'canceled',
        updated_at: new Date().toISOString(),
      })
      .eq('stripe_customer_id', customer.id);

    if (error) {
      console.error('❌ Error removing customer ID from user:', error);
      return;
    }

    console.log(`✅ Customer removed from user: ${customer.id}`);
  } catch (error: any) {
    console.error('❌ Error processing customer deletion:', error);
    throw error;
  }
}

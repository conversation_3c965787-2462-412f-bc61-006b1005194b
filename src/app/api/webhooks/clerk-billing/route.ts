import { Webhook } from 'svix';
import { headers } from 'next/headers';
import { NextResponse } from 'next/server';
import { createServiceRoleSupabaseClient } from '@/utils/supabase/server';
import { processSubscriptionManually } from '@/utils/stripe/webhook-helpers';

const webhookSecret = process.env.CLERK_BILLING_WEBHOOK_SECRET || process.env.CLERK_WEBHOOK_SECRET;

if (!webhookSecret) {
  throw new Error('Please add CLERK_BILLING_WEBHOOK_SECRET from Clerk Dashboard to .env or .env.local');
}

// GET endpoint for testing
export async function GET() {
  console.log('🔍 GET request to Clerk billing webhook endpoint');
  return NextResponse.json({
    message: 'Clerk billing webhook endpoint is working',
    timestamp: new Date().toISOString(),
    hasWebhookSecret: !!webhookSecret,
    environment: process.env.NODE_ENV,
  });
}

export async function POST(req: Request) {
  console.log('🚀 CLERK BILLING WEBHOOK RECEIVED - Starting processing...');
  
  // Get the headers
  const headerPayload = headers();
  const svix_id = headerPayload.get('svix-id');
  const svix_timestamp = headerPayload.get('svix-timestamp');
  const svix_signature = headerPayload.get('svix-signature');

  console.log('📋 Clerk billing webhook details:', {
    hasHeaders: !!(svix_id && svix_timestamp && svix_signature),
    eventId: svix_id,
  });

  // If there are no headers, error out
  if (!svix_id || !svix_timestamp || !svix_signature) {
    console.error('❌ Missing svix headers');
    return new Response('Error occurred -- no svix headers', {
      status: 400,
    });
  }

  // Get the body
  const payload = await req.text();
  const body = JSON.parse(payload);

  console.log('📋 Clerk billing webhook payload:', JSON.stringify(body, null, 2));

  // Create a new Svix instance with your secret.
  const wh = new Webhook(webhookSecret);

  let evt: any;

  // Verify the payload with the headers
  try {
    evt = wh.verify(payload, {
      'svix-id': svix_id,
      'svix-timestamp': svix_timestamp,
      'svix-signature': svix_signature,
    });
    console.log(`✅ Clerk billing webhook verified: ${evt.type}`);
  } catch (err) {
    console.error('❌ Error verifying Clerk billing webhook:', err);
    return new Response('Error occurred', {
      status: 400,
    });
  }

  // Handle the webhook
  const eventType = evt.type;
  console.log(`🔄 Processing Clerk billing webhook: ${eventType}`);

  const supabase = createServiceRoleSupabaseClient();

  try {
    switch (eventType) {
      case 'subscription.created':
        await handleSubscriptionCreated(supabase, evt.data);
        break;
      case 'subscription.updated':
        await handleSubscriptionUpdated(supabase, evt.data);
        break;
      case 'subscription.cancelled':
        await handleSubscriptionCancelled(supabase, evt.data);
        break;
      case 'invoice.payment_succeeded':
        await handleInvoicePaymentSucceeded(supabase, evt.data);
        break;
      case 'invoice.payment_failed':
        await handleInvoicePaymentFailed(supabase, evt.data);
        break;
      default:
        console.log(`🔍 Unhandled Clerk billing event type: ${eventType}`);
        console.log(`📋 Event data:`, JSON.stringify(evt.data, null, 2));
    }

    console.log(`✅ Clerk billing webhook processed successfully: ${eventType}`);
    return NextResponse.json({
      received: true,
      eventType: eventType,
      eventId: evt.id || 'unknown',
      timestamp: new Date().toISOString()
    });

  } catch (error: any) {
    console.error('❌ CRITICAL ERROR processing Clerk billing webhook:', error);
    console.error('❌ Error stack:', error.stack);
    console.error('❌ Event type:', eventType);
    
    return NextResponse.json({
      error: 'Webhook processing failed',
      eventType: eventType || 'unknown',
      eventId: evt?.id || 'unknown',
      message: error.message,
      stack: error.stack,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

async function handleSubscriptionCreated(supabase: any, data: any) {
  console.log('💳 Subscription created:', JSON.stringify(data, null, 2));
  
  try {
    const { user_id, plan, status, subscription_id, customer_id } = data;
    
    if (!user_id) {
      console.error('❌ No user_id in subscription created event');
      return;
    }

    console.log(`🔄 Processing subscription creation for user ${user_id}: ${plan} plan`);

    // Map Clerk plan to our plan IDs
    const planMapping: Record<string, string> = {
      'standard': 'standard',
      'pro': 'pro', 
      'premium': 'premium',
      'Standard': 'standard',
      'Pro': 'pro',
      'Premium': 'premium',
    };

    const planId = planMapping[plan] || 'standard';

    // Create a mock subscription object for our processing function
    const mockSubscription = {
      id: subscription_id || `sub_clerk_${Date.now()}`,
      customer: customer_id || `cus_clerk_${Date.now()}`,
      status: status || 'active',
      current_period_start: Math.floor(Date.now() / 1000),
      current_period_end: Math.floor((Date.now() + 30 * 24 * 60 * 60 * 1000) / 1000),
      items: {
        data: [{
          price: {
            id: `price_clerk_${planId}`
          }
        }]
      }
    };

    // Process the subscription using our existing logic
    await processSubscriptionManually(
      supabase,
      user_id,
      mockSubscription as any,
      planId,
      `price_clerk_${planId}`
    );

    console.log(`✅ Subscription created successfully for user ${user_id}: ${planId} plan`);

  } catch (error) {
    console.error('❌ Error handling subscription created:', error);
    throw error;
  }
}

async function handleSubscriptionUpdated(supabase: any, data: any) {
  console.log('💳 Subscription updated:', JSON.stringify(data, null, 2));
  
  try {
    const { user_id, plan, status, subscription_id } = data;
    
    if (!user_id) {
      console.error('❌ No user_id in subscription updated event');
      return;
    }

    console.log(`🔄 Processing subscription update for user ${user_id}: ${plan} plan, status: ${status}`);

    // If subscription is cancelled, downgrade to free
    if (status === 'cancelled' || status === 'inactive') {
      await handleSubscriptionCancellation(supabase, user_id);
      return;
    }

    // Otherwise, process as a new subscription
    await handleSubscriptionCreated(supabase, data);

  } catch (error) {
    console.error('❌ Error handling subscription updated:', error);
    throw error;
  }
}

async function handleSubscriptionCancelled(supabase: any, data: any) {
  console.log('💳 Subscription cancelled:', JSON.stringify(data, null, 2));
  
  try {
    const { user_id } = data;
    
    if (!user_id) {
      console.error('❌ No user_id in subscription cancelled event');
      return;
    }

    await handleSubscriptionCancellation(supabase, user_id);

  } catch (error) {
    console.error('❌ Error handling subscription cancelled:', error);
    throw error;
  }
}

async function handleSubscriptionCancellation(supabase: any, userId: string) {
  console.log(`🔄 Processing subscription cancellation for user ${userId}`);

  // Downgrade to free plan
  const { error: userUpdateError } = await supabase
    .from('users')
    .update({
      subscription_type: 'Free',
      subscription_status: 'cancelled',
      stripe_subscription_id: null,
      subscription_cancel_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    })
    .eq('id', userId);

  if (userUpdateError) {
    console.error('❌ Error updating user for cancellation:', userUpdateError);
    throw userUpdateError;
  }

  // Record cancellation in history
  await supabase
    .from('subscription_history')
    .insert({
      user_id: userId,
      plan_id: 'free',
      action: 'cancelled',
      effective_date: new Date().toISOString(),
    });

  console.log(`✅ Subscription cancelled successfully for user ${userId}`);
}

async function handleInvoicePaymentSucceeded(supabase: any, data: any) {
  console.log('💰 Invoice payment succeeded:', JSON.stringify(data, null, 2));
  
  try {
    const { user_id, amount_paid, plan } = data;
    
    if (!user_id) {
      console.error('❌ No user_id in invoice payment succeeded event');
      return;
    }

    // Record payment in history
    await supabase
      .from('payment_history')
      .insert({
        user_id: user_id,
        amount: amount_paid || 0,
        currency: 'usd',
        status: 'succeeded',
        description: `Clerk billing payment for ${plan} plan`,
        metadata: data,
      });

    console.log(`✅ Payment recorded for user ${user_id}: $${(amount_paid || 0) / 100}`);

  } catch (error) {
    console.error('❌ Error handling invoice payment succeeded:', error);
    throw error;
  }
}

async function handleInvoicePaymentFailed(supabase: any, data: any) {
  console.log('💰 Invoice payment failed:', JSON.stringify(data, null, 2));
  
  try {
    const { user_id, amount_due, plan } = data;
    
    if (!user_id) {
      console.error('❌ No user_id in invoice payment failed event');
      return;
    }

    // Record failed payment in history
    await supabase
      .from('payment_history')
      .insert({
        user_id: user_id,
        amount: amount_due || 0,
        currency: 'usd',
        status: 'failed',
        description: `Clerk billing payment failed for ${plan} plan`,
        metadata: data,
      });

    // Update user subscription status
    await supabase
      .from('users')
      .update({
        subscription_status: 'past_due',
        updated_at: new Date().toISOString(),
      })
      .eq('id', user_id);

    console.log(`⚠️ Payment failed recorded for user ${user_id}: $${(amount_due || 0) / 100}`);

  } catch (error) {
    console.error('❌ Error handling invoice payment failed:', error);
    throw error;
  }
}

import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { createServiceRoleSupabaseClient } from '@/utils/supabase/server';

export async function GET() {
  try {
    const { userId, sessionClaims } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    console.log(`🔍 Checking payment status for user ${userId}`);

    const supabase = createServiceRoleSupabaseClient();

    // Get current Clerk session data
    const clerkPlan = sessionClaims?.pla as string;
    const clerkPlanName = clerkPlan?.replace('u:', '').replace('_user', '') || 'free';
    
    // Get current database user data
    const { data: dbUser, error: dbError } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single();

    if (dbError) {
      return NextResponse.json({
        success: false,
        error: 'Failed to fetch user from database',
        details: dbError.message
      }, { status: 500 });
    }

    // Get recent payment history (if any)
    const { data: paymentHistory, error: paymentError } = await supabase
      .from('payment_history')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(5);

    // Get recent subscription history
    const { data: subHistory, error: subError } = await supabase
      .from('subscription_history')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(5);

    // Check if there are recent payments that haven't been reflected in Clerk
    const recentPayments = paymentHistory?.filter(p => {
      const paymentTime = new Date(p.created_at).getTime();
      const tenMinutesAgo = Date.now() - 10 * 60 * 1000;
      return paymentTime > tenMinutesAgo;
    }) || [];

    const hasRecentPayments = recentPayments.length > 0;

    return NextResponse.json({
      success: true,
      paymentStatus: {
        clerkPlanUpdated: clerkPlanName !== 'free',
        hasRecentPayments,
        clerkStillShowsFree: clerkPlanName === 'free',
        possibleDelay: clerkPlanName === 'free' && hasRecentPayments,
      },
      clerkData: {
        userId,
        sessionClaims,
        rawPlan: clerkPlan,
        parsedPlan: clerkPlanName,
      },
      databaseData: {
        user: dbUser,
        planFromDb: dbUser?.subscription_type?.toLowerCase() || 'free',
        creditsFromDb: dbUser?.credits || 0,
      },
      recentActivity: {
        paymentHistory: paymentHistory || [],
        subscriptionHistory: subHistory || [],
        recentPayments,
      },
      analysis: {
        clerkPlan: clerkPlanName,
        databasePlan: dbUser?.subscription_type?.toLowerCase() || 'free',
        clerkShowsFree: clerkPlanName === 'free',
        hasRecentPayments,
        possibleIssues: [
          clerkPlanName === 'free' && hasRecentPayments ? 'Clerk billing may not have updated session claims yet' : null,
          clerkPlanName === 'free' && !hasRecentPayments ? 'No recent payments detected' : null,
          clerkPlanName !== 'free' ? 'Clerk shows paid plan - sync should work' : null,
        ].filter(Boolean),
      },
      recommendations: [
        clerkPlanName === 'free' && hasRecentPayments ? '⏳ Wait 5-10 minutes for Clerk to update session claims' : null,
        clerkPlanName === 'free' && hasRecentPayments ? '🔄 Try refreshing the page in a few minutes' : null,
        clerkPlanName === 'free' && !hasRecentPayments ? '💳 Make sure payment was completed successfully' : null,
        clerkPlanName !== 'free' ? '🚀 Run sync - Clerk shows paid plan' : null,
      ].filter(Boolean),
      actions: {
        waitAndRefresh: 'Wait 5-10 minutes then refresh page',
        checkClerkDashboard: 'Check Clerk dashboard for subscription status',
        manualSync: 'POST /api/sync-clerk-subscription (if Clerk shows paid plan)',
        forceUpgrade: 'POST /api/debug/manual-pro-upgrade (emergency only)',
      },
      timestamp: new Date().toISOString(),
    });

  } catch (error: any) {
    console.error('❌ Error checking payment status:', error);
    return NextResponse.json({
      success: false,
      error: error.message,
      stack: error.stack
    }, { status: 500 });
  }
}

import { NextResponse } from 'next/server';
import { createServiceRoleSupabaseClient } from '@/utils/supabase/server';
import { processSubscription } from '@/utils/stripe/webhook-helpers';

export async function POST(request: Request) {
  try {
    const { userId, priceId, planId } = await request.json();

    if (!userId || !priceId || !planId) {
      return NextResponse.json({ 
        error: 'Missing required fields: userId, priceId, planId' 
      }, { status: 400 });
    }

    const supabase = createServiceRoleSupabaseClient();

    console.log(`🧪 Manual payment test for user ${userId}`);
    console.log(`📋 Price ID: ${priceId}, Plan ID: ${planId}`);

    // Create a mock subscription object
    const mockSubscription = {
      id: `sub_test_${Date.now()}`,
      customer: `cus_test_${Date.now()}`,
      status: 'active',
      current_period_start: Math.floor(Date.now() / 1000),
      current_period_end: Math.floor((Date.now() + 30 * 24 * 60 * 60 * 1000) / 1000),
      items: {
        data: [{
          price: {
            id: priceId
          }
        }]
      }
    };

    // Test the subscription processing
    await processSubscription(supabase, userId, mockSubscription as any);

    // Get updated user data
    const { data: updatedUser, error: userError } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single();

    if (userError) {
      console.error('❌ Error fetching updated user:', userError);
      return NextResponse.json({ 
        error: 'Failed to fetch updated user data',
        details: userError.message 
      }, { status: 500 });
    }

    // Get recent credit transactions
    const { data: credits, error: creditsError } = await supabase
      .from('credit_transactions')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(5);

    return NextResponse.json({
      success: true,
      message: 'Manual payment test completed',
      results: {
        user: updatedUser,
        recent_credits: credits || [],
        test_data: {
          priceId,
          planId,
          mockSubscriptionId: mockSubscription.id
        }
      }
    });

  } catch (error: any) {
    console.error('❌ Error in manual payment test:', error);
    return NextResponse.json({
      success: false,
      error: error.message,
      stack: error.stack
    }, { status: 500 });
  }
}

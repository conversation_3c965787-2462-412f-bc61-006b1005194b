import { NextResponse } from 'next/server';
import { createServiceRoleSupabaseClient } from '@/utils/supabase/server';

export async function GET() {
  try {
    const supabase = createServiceRoleSupabaseClient();

    // Check recent payment history
    const { data: payments, error: paymentsError } = await supabase
      .from('payment_history')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(10);

    // Check recent subscription history
    const { data: subscriptions, error: subscriptionsError } = await supabase
      .from('subscription_history')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(10);

    // Check recent credit transactions
    const { data: credits, error: creditsError } = await supabase
      .from('credit_transactions')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(10);

    // Check users with recent updates
    const { data: users, error: usersError } = await supabase
      .from('users')
      .select('id, email, subscription_type, credits, stripe_customer_id, stripe_subscription_id, updated_at')
      .order('updated_at', { ascending: false })
      .limit(5);

    return NextResponse.json({
      success: true,
      webhook_activity: {
        recent_payments: payments || [],
        recent_subscriptions: subscriptions || [],
        recent_credits: credits || [],
        recent_users: users || [],
        errors: {
          payments: paymentsError?.message,
          subscriptions: subscriptionsError?.message,
          credits: creditsError?.message,
          users: usersError?.message,
        }
      }
    });

  } catch (error: any) {
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}

import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { createServiceRoleSupabaseClient } from '@/utils/supabase/server';

export async function POST() {
  try {
    // SECURITY: Only allow in development or with admin access
    const isDevelopment = process.env.NODE_ENV === 'development';
    if (!isDevelopment) {
      return NextResponse.json({ 
        error: 'This endpoint is only available in development' 
      }, { status: 403 });
    }

    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    console.log(`🕐 Manual credit expiration triggered by user ${userId}`);

    const supabase = createServiceRoleSupabaseClient();

    // Get user's current credits before expiration
    const { data: userBefore, error: userBeforeError } = await supabase
      .from('users')
      .select('credits, subscription_type')
      .eq('id', userId)
      .single();

    if (userBeforeError) {
      return NextResponse.json({
        success: false,
        error: 'Failed to get user data',
        details: userBeforeError.message
      }, { status: 500 });
    }

    // Get user's credit transactions before expiration
    const { data: transactionsBefore, error: transBeforeError } = await supabase
      .from('credit_transactions')
      .select('id, amount, expires_at, is_expired, description')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (transBeforeError) {
      console.error('❌ Error fetching transactions before:', transBeforeError);
    }

    console.log('📋 User credits before expiration:', userBefore.credits);
    console.log('📋 Credit transactions before:', transactionsBefore);

    // Call the database function to expire credits
    const { error: expireError } = await supabase.rpc('expire_credits');

    if (expireError) {
      console.error('❌ Error expiring credits:', expireError);
      return NextResponse.json({
        success: false,
        error: 'Failed to expire credits',
        details: expireError.message
      }, { status: 500 });
    }

    console.log('✅ Credit expiration function executed successfully');

    // Get user's credits after expiration
    const { data: userAfter, error: userAfterError } = await supabase
      .from('users')
      .select('credits, subscription_type')
      .eq('id', userId)
      .single();

    if (userAfterError) {
      console.error('❌ Error fetching user after expiration:', userAfterError);
    }

    // Get user's credit transactions after expiration
    const { data: transactionsAfter, error: transAfterError } = await supabase
      .from('credit_transactions')
      .select('id, amount, expires_at, is_expired, description')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (transAfterError) {
      console.error('❌ Error fetching transactions after:', transAfterError);
    }

    console.log('📋 User credits after expiration:', userAfter?.credits);
    console.log('📋 Credit transactions after:', transactionsAfter);

    // Calculate what changed
    const creditsBefore = userBefore.credits || 0;
    const creditsAfter = userAfter?.credits || 0;
    const creditsExpired = creditsBefore - creditsAfter;

    // Count expired transactions
    const expiredTransactions = transactionsAfter?.filter(t => t.is_expired) || [];
    const activeTransactions = transactionsAfter?.filter(t => !t.is_expired) || [];

    return NextResponse.json({
      success: true,
      message: 'Credit expiration completed',
      results: {
        creditsBefore,
        creditsAfter,
        creditsExpired,
        expiredTransactionsCount: expiredTransactions.length,
        activeTransactionsCount: activeTransactions.length,
      },
      details: {
        userBefore,
        userAfter,
        transactionsBefore: transactionsBefore?.slice(0, 10), // Limit for readability
        transactionsAfter: transactionsAfter?.slice(0, 10),
        expiredTransactions: expiredTransactions.slice(0, 5),
        activeTransactions: activeTransactions.slice(0, 5),
      },
      instructions: [
        'Check your dashboard to see updated credits',
        'Expired credits should now be marked as expired',
        'User credits should reflect only non-expired credits',
      ],
      timestamp: new Date().toISOString(),
    });

  } catch (error: any) {
    console.error('❌ Error in manual credit expiration:', error);
    return NextResponse.json({
      success: false,
      error: error.message,
      stack: error.stack
    }, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Manual credit expiration endpoint',
    description: 'Manually triggers the credit expiration process for testing',
    usage: 'POST to trigger credit expiration',
    note: 'This will expire all credits past their expiration date and update user totals',
    security: 'Only available in development mode'
  });
}

import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { createServiceRoleSupabaseClient } from '@/utils/supabase/server';

export async function POST() {
  try {
    // SECURITY: Only allow in development
    const isDevelopment = process.env.NODE_ENV === 'development';
    if (!isDevelopment) {
      return NextResponse.json({ 
        error: 'This endpoint is only available in development' 
      }, { status: 403 });
    }

    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    console.log(`🔧 Fixing Pro plan credit issue for user ${userId}`);

    const supabase = createServiceRoleSupabaseClient();

    // Step 1: Force update database plan to have correct credits
    console.log('🔄 Step 1: Updating database plan credits...');
    
    const { error: updatePlanError } = await supabase
      .from('subscription_plans')
      .update({
        credits_included: 3500,
        updated_at: new Date().toISOString(),
      })
      .eq('id', 'pro');

    if (updatePlanError) {
      console.error('❌ Error updating Pro plan in database:', updatePlanError);
    } else {
      console.log('✅ Pro plan updated in database to 3500 credits');
    }

    // Step 2: Check current user data
    console.log('🔄 Step 2: Checking current user data...');
    
    const { data: currentUser, error: userError } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single();

    if (userError || !currentUser) {
      return NextResponse.json({
        success: false,
        error: 'Failed to fetch user data',
        details: userError?.message
      }, { status: 500 });
    }

    console.log(`📋 Current user plan: ${currentUser.subscription_type}`);
    console.log(`📋 Current user credits: ${currentUser.credits}`);

    // Step 3: If user is on Pro plan, fix their credits
    if (currentUser.subscription_type?.toLowerCase() === 'pro') {
      console.log('🔄 Step 3: User is on Pro plan, fixing credits...');
      
      // Delete any incorrect Pro credit transactions
      const { error: deleteError } = await supabase
        .from('credit_transactions')
        .delete()
        .eq('user_id', userId)
        .eq('plan_id', 'pro')
        .eq('transaction_type', 'subscription');

      if (deleteError) {
        console.error('❌ Error deleting incorrect Pro transactions:', deleteError);
      } else {
        console.log('✅ Deleted incorrect Pro credit transactions');
      }

      // Add correct Pro credits
      const { error: creditError } = await supabase
        .from('credit_transactions')
        .insert({
          user_id: userId,
          amount: 3500,
          description: 'Pro plan: Fixed credits (3500)',
          transaction_type: 'subscription',
          expires_at: null, // Pro credits don't expire
          plan_id: 'pro',
        });

      if (creditError) {
        console.error('❌ Error adding correct Pro credits:', creditError);
      } else {
        console.log('✅ Added correct Pro credits (3500)');
      }

      // Recalculate total credits
      const { data: allCredits, error: totalError } = await supabase
        .from('credit_transactions')
        .select('amount')
        .eq('user_id', userId)
        .eq('is_expired', false);

      if (!totalError && allCredits) {
        let totalCredits = 0;
        for (const credit of allCredits) {
          totalCredits += credit.amount || 0;
        }
        
        console.log(`🔄 Updating user total credits to: ${totalCredits}`);
        
        const { error: updateUserError } = await supabase
          .from('users')
          .update({
            credits: totalCredits,
            updated_at: new Date().toISOString(),
          })
          .eq('id', userId);

        if (updateUserError) {
          console.error('❌ Error updating user credits:', updateUserError);
        } else {
          console.log('✅ Updated user total credits');
        }
      }
    }

    // Step 4: Verify final state
    console.log('🔄 Step 4: Verifying final state...');
    
    const { data: finalUser, error: finalError } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single();

    const { data: proPlan, error: planError } = await supabase
      .from('subscription_plans')
      .select('*')
      .eq('id', 'pro')
      .single();

    const { data: proCredits, error: creditsError } = await supabase
      .from('credit_transactions')
      .select('*')
      .eq('user_id', userId)
      .eq('plan_id', 'pro')
      .eq('transaction_type', 'subscription');

    return NextResponse.json({
      success: true,
      message: 'Pro plan credit issue fix completed',
      results: {
        databasePlan: proPlan,
        userBefore: currentUser,
        userAfter: finalUser,
        proCreditsTransactions: proCredits,
        creditsFixed: finalUser?.credits !== currentUser.credits,
      },
      summary: {
        proPlanCreditsInDatabase: proPlan?.credits_included,
        userPlan: finalUser?.subscription_type,
        userCredits: finalUser?.credits,
        proTransactionsCount: proCredits?.length || 0,
      },
      instructions: [
        'Database Pro plan now has 3500 credits',
        finalUser?.subscription_type?.toLowerCase() === 'pro' ? 
          `Your Pro account now has ${finalUser?.credits} total credits` : 
          'You are not on Pro plan',
        'Test sync again to verify it works correctly',
        'Future Pro upgrades should use correct 3500 credits',
      ],
      nextSteps: [
        'Test upgrading to Pro plan',
        'Verify credits are exactly 3500 (plus any existing credits)',
        'Check that sync uses hardcoded values',
      ],
      timestamp: new Date().toISOString(),
    });

  } catch (error: any) {
    console.error('❌ Error fixing Pro credit issue:', error);
    return NextResponse.json({
      success: false,
      error: error.message,
      stack: error.stack
    }, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Fix Pro plan credit issue endpoint',
    description: 'Fixes the issue where Pro plan gives 1000049 credits instead of 3500',
    correctAmount: 3500,
    incorrectAmount: 1000049,
    whatItDoes: [
      'Updates database Pro plan to have 3500 credits',
      'Fixes current Pro users to have correct credits',
      'Removes incorrect credit transactions',
      'Adds correct credit transactions',
      'Recalculates total credits',
    ],
    usage: 'POST to fix Pro plan credit issue',
    security: 'Only available in development mode',
  });
}

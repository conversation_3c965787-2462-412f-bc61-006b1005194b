import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { createServiceRoleSupabaseClient } from '@/utils/supabase/server';
import { processSubscriptionManually } from '@/utils/stripe/webhook-helpers';

export async function POST(request: Request) {
  try {
    // Get the current user
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    const { planId = 'standard', priceId = 'price_test_123' } = await request.json();

    const supabase = createServiceRoleSupabaseClient();

    console.log(`🧪 Manual subscription trigger for user ${userId} to plan ${planId}`);

    // Create a mock subscription object
    const mockSubscription = {
      id: `sub_test_${Date.now()}`,
      customer: `cus_test_${Date.now()}`,
      status: 'active',
      current_period_start: Math.floor(Date.now() / 1000),
      current_period_end: Math.floor((Date.now() + 30 * 24 * 60 * 60 * 1000) / 1000),
      items: {
        data: [{
          price: {
            id: priceId
          }
        }]
      }
    };

    // Process the subscription manually
    await processSubscriptionManually(
      supabase, 
      userId, 
      mockSubscription as any, 
      planId, 
      priceId
    );

    // Get updated user data
    const { data: updatedUser, error: userError } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single();

    if (userError) {
      console.error('❌ Error fetching updated user:', userError);
      return NextResponse.json({
        success: false,
        error: 'Failed to fetch updated user data'
      }, { status: 500 });
    }

    // Get recent credit transactions
    const { data: credits, error: creditsError } = await supabase
      .from('credit_transactions')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(5);

    // Get recent subscription history
    const { data: subscriptionHistory, error: historyError } = await supabase
      .from('subscription_history')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(3);

    console.log(`✅ Manual subscription trigger completed for user ${userId}`);

    return NextResponse.json({
      success: true,
      message: `Successfully triggered subscription upgrade to ${planId} plan`,
      results: {
        user: updatedUser,
        recent_credits: credits || [],
        subscription_history: subscriptionHistory || [],
        test_data: {
          planId,
          priceId,
          userId,
          mockSubscriptionId: mockSubscription.id,
          timestamp: new Date().toISOString()
        }
      }
    });

  } catch (error: any) {
    console.error('❌ Error in manual subscription trigger:', error);
    return NextResponse.json({
      success: false,
      error: error.message,
      stack: error.stack
    }, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Manual subscription trigger endpoint',
    usage: 'POST with { "planId": "standard" | "pro" | "premium", "priceId": "optional_price_id" }',
    note: 'This will manually trigger subscription processing for the current user'
  });
}

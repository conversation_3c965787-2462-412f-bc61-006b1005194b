import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { createServiceRoleSupabaseClient } from '@/utils/supabase/server';

export async function GET() {
  try {
    const { userId, sessionClaims } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    console.log(`🔍 Diagnosing sync for user ${userId}`);

    const supabase = createServiceRoleSupabaseClient();

    // Get current Clerk session data
    const clerkPlan = sessionClaims?.pla as string;
    const clerkPlanName = clerkPlan?.replace('u:', '').replace('_user', '') || 'free';
    
    // Get current database user data
    const { data: dbUser, error: dbError } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single();

    if (dbError) {
      return NextResponse.json({
        success: false,
        error: 'Failed to fetch user from database',
        details: dbError.message
      }, { status: 500 });
    }

    // Get all subscription plans
    const { data: allPlans, error: plansError } = await supabase
      .from('subscription_plans')
      .select('*')
      .order('id');

    // Get user's credit transactions
    const { data: creditTransactions, error: creditError } = await supabase
      .from('credit_transactions')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(20);

    // Get subscription history
    const { data: subHistory, error: subError } = await supabase
      .from('subscription_history')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(10);

    // Analyze sync status
    const dbPlanName = dbUser?.subscription_type?.toLowerCase() || 'free';
    const isInSync = clerkPlanName === dbPlanName;

    // Calculate expected credits based on Clerk plan
    const planCreditsMap: Record<string, number> = {
      'free': 50,
      'standard': 700,
      'pro': 3500,
      'premium': 14500,
    };

    const expectedCreditsForPlan = planCreditsMap[clerkPlanName] || 50;
    const actualCredits = dbUser?.credits || 0;

    // Check if user has credits for their current Clerk plan
    const planCredits = creditTransactions?.filter(t => t.plan_id === clerkPlanName) || [];
    const hasPlanCredits = planCredits.length > 0;

    // Calculate total from active transactions
    const activeTransactions = creditTransactions?.filter(t => !t.is_expired) || [];
    const totalFromTransactions = activeTransactions.reduce((sum, t) => sum + (t.amount || 0), 0);

    return NextResponse.json({
      success: true,
      diagnosis: {
        syncNeeded: !isInSync || !hasPlanCredits,
        planMismatch: !isInSync,
        missingPlanCredits: !hasPlanCredits,
        creditMismatch: actualCredits !== totalFromTransactions,
      },
      clerkData: {
        userId,
        sessionClaims,
        rawPlan: clerkPlan,
        parsedPlan: clerkPlanName,
      },
      databaseData: {
        user: dbUser,
        planFromDb: dbPlanName,
        creditsFromDb: actualCredits,
        totalFromTransactions,
      },
      planAnalysis: {
        clerkPlan: clerkPlanName,
        databasePlan: dbPlanName,
        plansMatch: isInSync,
        expectedCreditsForClerkPlan: expectedCreditsForPlan,
        actualCredits: actualCredits,
        hasPlanCredits: hasPlanCredits,
        planCreditsCount: planCredits.length,
      },
      availablePlans: allPlans,
      recentActivity: {
        creditTransactions: creditTransactions?.slice(0, 10) || [],
        subscriptionHistory: subHistory || [],
        activeTransactions: activeTransactions.slice(0, 5),
      },
      recommendations: [
        !isInSync ? `🔄 Plan mismatch: Clerk shows "${clerkPlanName}" but database shows "${dbPlanName}"` : null,
        !hasPlanCredits ? `💳 Missing credits for ${clerkPlanName} plan` : null,
        actualCredits !== totalFromTransactions ? `🔢 Credit total mismatch: DB=${actualCredits}, Transactions=${totalFromTransactions}` : null,
        (!isInSync || !hasPlanCredits) ? '🚀 Run sync to fix issues' : null,
      ].filter(Boolean),
      actions: {
        manualSync: 'POST /api/sync-clerk-subscription',
        forceSync: 'Clear session storage and refresh page',
        fixCredits: 'POST /api/debug/fix-my-pro-credits (if Pro plan)',
      },
      timestamp: new Date().toISOString(),
    });

  } catch (error: any) {
    console.error('❌ Error diagnosing sync:', error);
    return NextResponse.json({
      success: false,
      error: error.message,
      stack: error.stack
    }, { status: 500 });
  }
}

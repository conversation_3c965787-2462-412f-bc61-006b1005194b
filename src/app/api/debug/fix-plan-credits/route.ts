import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { createServiceRoleSupabaseClient } from '@/utils/supabase/server';

export async function POST() {
  try {
    // SECURITY: Only allow in development
    const isDevelopment = process.env.NODE_ENV === 'development';
    if (!isDevelopment) {
      return NextResponse.json({
        error: 'This endpoint is only available in development'
      }, { status: 403 });
    }

    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    console.log(`🔧 Fixing plan credits configuration`);

    const supabase = createServiceRoleSupabaseClient();

    // Correct credit amounts (fixed Pro and Premium)
    const correctCredits = {
      free: 50,
      standard: 700,
      pro: 3500,        // Was showing 1000749 - should be 3500
      premium: 14500,   // Was showing 2000748 - should be 14500
    };

    const results = [];

    // Update each plan with correct credits
    for (const [planId, credits] of Object.entries(correctCredits)) {
      console.log(`🔄 Updating ${planId} plan to ${credits} credits`);

      const { data: updatedPlan, error: updateError } = await supabase
        .from('subscription_plans')
        .update({
          credits_included: credits,
          updated_at: new Date().toISOString(),
        })
        .eq('id', planId)
        .select()
        .single();

      if (updateError) {
        console.error(`❌ Error updating ${planId} plan:`, updateError);
        results.push({
          planId,
          success: false,
          error: updateError.message,
        });
      } else {
        console.log(`✅ Updated ${planId} plan successfully`);
        results.push({
          planId,
          success: true,
          oldCredits: updatedPlan?.credits_included || 'unknown',
          newCredits: credits,
          plan: updatedPlan,
        });
      }
    }

    // Get updated plans to verify
    const { data: verifyPlans, error: verifyError } = await supabase
      .from('subscription_plans')
      .select('*')
      .order('id');

    if (verifyError) {
      console.error('❌ Error verifying updated plans:', verifyError);
    }

    const successCount = results.filter(r => r.success).length;
    const failureCount = results.filter(r => !r.success).length;

    return NextResponse.json({
      success: failureCount === 0,
      message: `Updated ${successCount} plans successfully${failureCount > 0 ? `, ${failureCount} failed` : ''}`,
      results,
      correctCredits,
      updatedPlans: verifyPlans,
      summary: {
        totalPlans: results.length,
        successful: successCount,
        failed: failureCount,
      },
      nextSteps: [
        'Check /api/debug/check-plan-credits to verify updates',
        'Existing users may need credit recalculation',
        'New subscriptions will use correct credit amounts',
      ],
      timestamp: new Date().toISOString(),
    });

  } catch (error: any) {
    console.error('❌ Error fixing plan credits:', error);
    return NextResponse.json({
      success: false,
      error: error.message,
      stack: error.stack
    }, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Fix plan credits endpoint',
    description: 'Updates subscription plans with correct credit amounts',
    correctCredits: {
      free: 50,
      standard: 700,
      pro: 3500,
      premium: 14500,
    },
    usage: 'POST to fix all plan credit amounts',
    security: 'Only available in development mode',
  });
}

import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { createServiceRoleSupabaseClient } from '@/utils/supabase/server';

export async function POST() {
  try {
    // SECURITY: Only allow in development
    const isDevelopment = process.env.NODE_ENV === 'development';
    if (!isDevelopment) {
      return NextResponse.json({ 
        error: 'This endpoint is only available in development' 
      }, { status: 403 });
    }

    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    console.log(`🔧 Fixing Pro credits for user ${userId}`);

    const supabase = createServiceRoleSupabaseClient();

    // Get current user data
    const { data: currentUser, error: userError } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single();

    if (userError || !currentUser) {
      return NextResponse.json({
        success: false,
        error: 'Failed to fetch user data',
        details: userError?.message
      }, { status: 500 });
    }

    console.log(`📋 Current user credits: ${currentUser.credits}`);
    console.log(`📋 Current subscription: ${currentUser.subscription_type}`);

    // Get all user's credit transactions
    const { data: allTransactions, error: transError } = await supabase
      .from('credit_transactions')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (transError) {
      console.error('❌ Error fetching transactions:', transError);
    }

    console.log(`📋 Total transactions: ${allTransactions?.length || 0}`);

    // Find Pro plan transactions
    const proTransactions = allTransactions?.filter(t => t.plan_id === 'pro') || [];
    console.log(`📋 Pro transactions: ${proTransactions.length}`);

    // If user has Pro plan, fix their credits
    if (currentUser.subscription_type?.toLowerCase() === 'pro') {
      console.log('🔄 User has Pro plan, fixing credits...');

      // Delete existing Pro credit transactions (they have wrong amounts)
      if (proTransactions.length > 0) {
        console.log(`🗑️ Deleting ${proTransactions.length} incorrect Pro transactions`);
        
        const { error: deleteError } = await supabase
          .from('credit_transactions')
          .delete()
          .eq('user_id', userId)
          .eq('plan_id', 'pro');

        if (deleteError) {
          console.error('❌ Error deleting Pro transactions:', deleteError);
        } else {
          console.log('✅ Deleted incorrect Pro transactions');
        }
      }

      // Add correct Pro credits (3,500)
      console.log('➕ Adding correct Pro credits (3,500)');
      
      const { error: creditError } = await supabase
        .from('credit_transactions')
        .insert({
          user_id: userId,
          amount: 3500,
          description: `Pro plan: Corrected credits (was ${currentUser.credits})`,
          transaction_type: 'subscription',
          expires_at: null, // Pro credits don't expire
          plan_id: 'pro',
        });

      if (creditError) {
        console.error('❌ Error adding correct Pro credits:', creditError);
        return NextResponse.json({
          success: false,
          error: 'Failed to add correct Pro credits',
          details: creditError.message
        }, { status: 500 });
      }

      // Recalculate total credits
      const { data: updatedTransactions, error: recalcError } = await supabase
        .from('credit_transactions')
        .select('amount')
        .eq('user_id', userId)
        .eq('is_expired', false);

      if (!recalcError) {
        const newTotal = updatedTransactions?.reduce((sum: number, t: any) => sum + t.amount, 0) || 0;
        
        console.log(`🔄 Updating user total credits to: ${newTotal}`);
        
        const { error: updateUserError } = await supabase
          .from('users')
          .update({
            credits: newTotal,
            updated_at: new Date().toISOString(),
          })
          .eq('id', userId);

        if (updateUserError) {
          console.error('❌ Error updating user credits:', updateUserError);
        } else {
          console.log('✅ Updated user credits successfully');
        }
      }

      // Get final user data
      const { data: finalUser, error: finalError } = await supabase
        .from('users')
        .select('*')
        .eq('id', userId)
        .single();

      return NextResponse.json({
        success: true,
        message: 'Pro credits fixed successfully',
        changes: {
          previousCredits: currentUser.credits,
          newCredits: finalUser?.credits || 0,
          creditsDifference: (finalUser?.credits || 0) - currentUser.credits,
          proTransactionsDeleted: proTransactions.length,
          correctProCreditsAdded: 3500,
        },
        userBefore: currentUser,
        userAfter: finalUser,
        instructions: [
          'Refresh your dashboard to see corrected credits',
          'You should now have the correct Pro credits amount',
          'Pro plan includes 3,500 credits that don\'t expire',
        ],
        timestamp: new Date().toISOString(),
      });

    } else {
      return NextResponse.json({
        success: false,
        message: 'User does not have Pro plan',
        currentPlan: currentUser.subscription_type,
        suggestion: 'Use /api/debug/manual-pro-upgrade to upgrade to Pro first',
      });
    }

  } catch (error: any) {
    console.error('❌ Error fixing Pro credits:', error);
    return NextResponse.json({
      success: false,
      error: error.message,
      stack: error.stack
    }, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Fix my Pro credits endpoint',
    description: 'Fixes the current user\'s Pro plan credits to the correct amount (3,500)',
    usage: 'POST to fix your Pro credits',
    note: 'Only works if user currently has Pro plan',
    correctAmount: 3500,
    security: 'Only available in development mode',
  });
}

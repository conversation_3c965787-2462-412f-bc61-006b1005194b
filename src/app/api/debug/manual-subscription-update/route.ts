import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { createServiceRoleSupabaseClient } from '@/utils/supabase/server';

export async function POST(request: Request) {
  try {
    // SECURITY: Only allow in development
    const isDevelopment = process.env.NODE_ENV === 'development';
    if (!isDevelopment) {
      return NextResponse.json({ 
        error: 'This endpoint is only available in development' 
      }, { status: 403 });
    }

    // Get the current user
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    const { planId = 'standard' } = await request.json();

    console.log(`🔧 Manual subscription update for user ${userId} to plan ${planId}`);

    const supabase = createServiceRoleSupabaseClient();

    // Get plan details from our database
    const { data: plan, error: planError } = await supabase
      .from('subscription_plans')
      .select('*')
      .eq('id', planId)
      .single();

    if (planError || !plan) {
      return NextResponse.json({
        success: false,
        error: `Plan not found: ${planId}`,
        availablePlans: ['free', 'standard', 'pro', 'premium']
      }, { status: 400 });
    }

    console.log(`📋 Found plan: ${plan.name} with ${plan.credits_included} credits`);

    // Update user subscription details directly in database
    const { error: userUpdateError } = await supabase
      .from('users')
      .update({
        subscription_type: plan.name,
        subscription_status: 'active',
        stripe_customer_id: `cus_manual_${Date.now()}`,
        stripe_subscription_id: `sub_manual_${Date.now()}`,
        stripe_price_id: `price_manual_${planId}`,
        last_payment_date: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .eq('id', userId);

    if (userUpdateError) {
      console.error('❌ Error updating user subscription:', userUpdateError);
      return NextResponse.json({
        success: false,
        error: 'Failed to update user subscription',
        details: userUpdateError.message
      }, { status: 500 });
    }

    // Add credits if it's a paid plan
    let creditsAdded = 0;
    if (plan.credits_included > 0) {
      const expiresAt = plan.credits_expire_days > 0 
        ? new Date(Date.now() + plan.credits_expire_days * 24 * 60 * 60 * 1000).toISOString()
        : null;

      // Insert credit transaction
      const { error: creditError } = await supabase
        .from('credit_transactions')
        .insert({
          user_id: userId,
          amount: plan.credits_included,
          description: `Manual subscription: ${plan.name} plan`,
          transaction_type: 'subscription',
          expires_at: expiresAt,
          plan_id: planId,
        });

      if (creditError) {
        console.error('❌ Error adding credits:', creditError);
        return NextResponse.json({
          success: false,
          error: 'Failed to add credits',
          details: creditError.message
        }, { status: 500 });
      }

      // Update user's total credits
      const { data: totalCredits, error: totalError } = await supabase
        .from('credit_transactions')
        .select('amount')
        .eq('user_id', userId)
        .eq('is_expired', false);

      if (!totalError) {
        const newTotal = totalCredits?.reduce((sum: number, t: any) => sum + t.amount, 0) || 0;
        
        await supabase
          .from('users')
          .update({ 
            credits: newTotal,
            credits_expire_at: expiresAt,
          })
          .eq('id', userId);

        creditsAdded = plan.credits_included;
      }

      console.log(`✅ Added ${plan.credits_included} credits to user ${userId}`);
    }

    // Record subscription history
    const { error: historyError } = await supabase
      .from('subscription_history')
      .insert({
        user_id: userId,
        plan_id: planId,
        action: 'manual_upgrade',
        effective_date: new Date().toISOString(),
        metadata: {
          method: 'manual_update',
          plan: plan.name,
          credits_added: creditsAdded,
        },
      });

    if (historyError) {
      console.error('❌ Error recording subscription history:', historyError);
      // Don't fail here as it's not critical
    }

    // Get updated user data
    const { data: updatedUser, error: userError } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single();

    console.log(`✅ Manual subscription update completed for user ${userId}: ${planId} plan`);

    return NextResponse.json({
      success: true,
      message: `Successfully upgraded to ${plan.name} plan`,
      results: {
        plan: {
          id: planId,
          name: plan.name,
          credits_included: plan.credits_included,
          credits_expire_days: plan.credits_expire_days,
        },
        user: updatedUser,
        credits_added: creditsAdded,
        subscription_status: 'active',
      },
      instructions: [
        'Check your dashboard to see the updated plan and credits',
        'This simulates what should happen when Clerk billing works',
        'If this works, the issue is with Clerk billing integration',
      ],
      timestamp: new Date().toISOString(),
    });

  } catch (error: any) {
    console.error('❌ Error in manual subscription update:', error);
    return NextResponse.json({
      success: false,
      error: error.message,
      stack: error.stack
    }, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Manual subscription update endpoint',
    description: 'Directly updates user subscription in database without Clerk',
    usage: 'POST with { "planId": "standard" | "pro" | "premium" }',
    purpose: 'Test if subscription processing logic works independently of Clerk',
    security: 'Only available in development mode'
  });
}

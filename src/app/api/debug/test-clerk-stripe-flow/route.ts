import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { clerkClient } from '@clerk/nextjs/server';

export async function POST(request: Request) {
  try {
    // SECURITY: Only allow in development
    const isDevelopment = process.env.NODE_ENV === 'development';
    const adminSecret = request.headers.get('x-admin-secret');
    const validAdminSecret = process.env.ADMIN_SECRET_KEY;

    if (!isDevelopment && (!adminSecret || !validAdminSecret || adminSecret !== validAdminSecret)) {
      return NextResponse.json({ 
        error: 'Unauthorized - This endpoint is only available in development or with admin secret' 
      }, { status: 403 });
    }

    // Get the current user
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    const { planId = 'standard' } = await request.json();

    console.log(`🧪 Testing complete Clerk + Stripe billing flow for user ${userId} with plan ${planId}`);

    // Step 1: Simulate Stripe checkout completion by updating Clerk metadata
    // This follows the exact pattern from the documentation
    const stripeMetadata = {
      payment: 'paid',
      status: 'complete',
      plan: planId,
      session_id: `cs_test_${Date.now()}`,
      customer_id: `cus_test_${Date.now()}`,
      subscription_id: `sub_test_${Date.now()}`,
      price_id: `price_test_${planId}`,
      current_period_start: Math.floor(Date.now() / 1000),
      current_period_end: Math.floor((Date.now() + 30 * 24 * 60 * 60 * 1000) / 1000),
    };

    console.log('📋 Updating Clerk user metadata with Stripe data:', stripeMetadata);

    // Update Clerk user metadata (this will trigger user.updated webhook)
    await clerkClient.users.updateUserMetadata(userId, {
      publicMetadata: {
        stripe: stripeMetadata,
      },
    });

    console.log('✅ Clerk user metadata updated successfully');

    // Wait a moment for the webhook to process
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Check if the webhook processed the update
    const webhookLogsResponse = await fetch(`${process.env.NEXT_PUBLIC_SITE_URL || 'https://guardiavision.com'}/api/debug/webhook-logs`);
    const webhookLogs = await webhookLogsResponse.json();

    return NextResponse.json({
      success: true,
      message: `Successfully tested Clerk + Stripe billing flow for ${planId} plan`,
      steps: {
        step1: 'Updated Clerk user metadata with Stripe billing data',
        step2: 'Triggered user.updated webhook automatically',
        step3: 'Webhook should process subscription and add credits',
      },
      testData: {
        userId,
        planId,
        stripeMetadata,
        webhookLogs: webhookLogs.webhook_activity || {},
      },
      nextSteps: [
        'Check your dashboard to see if plan and credits updated',
        'Check webhook logs at /api/debug/webhook-logs',
        'Check Vercel function logs for detailed processing',
      ],
      timestamp: new Date().toISOString(),
    });

  } catch (error: any) {
    console.error('❌ Error testing Clerk + Stripe billing flow:', error);
    return NextResponse.json({
      success: false,
      error: error.message,
      stack: error.stack
    }, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Clerk + Stripe billing flow test endpoint',
    description: 'Tests the complete billing integration following the documentation pattern',
    usage: 'POST with { "planId": "standard" | "pro" | "premium" }',
    flow: [
      '1. Updates Clerk user metadata with Stripe billing data',
      '2. Triggers user.updated webhook automatically',
      '3. Webhook detects Stripe metadata and processes subscription',
      '4. User plan and credits are updated in database',
    ],
    security: 'Only available in development mode',
    documentation: 'Based on Clerk + Stripe integration documentation pattern'
  });
}

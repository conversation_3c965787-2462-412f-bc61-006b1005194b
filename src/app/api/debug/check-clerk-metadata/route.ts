import { NextResponse } from 'next/server';
import { auth, clerkClient } from '@clerk/nextjs/server';

export async function GET() {
  try {
    // Get the current user
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    console.log(`🔍 Checking Clerk metadata for user ${userId}`);

    // Get current user data from Clerk
    const currentUser = await clerkClient.users.getUser(userId);

    // Also get session claims to see what's available in the session
    const { sessionClaims } = await auth();

    return NextResponse.json({
      success: true,
      userId: userId,
      clerkUserData: {
        publicMetadata: currentUser.publicMetadata,
        privateMetadata: currentUser.privateMetadata,
        unsafeMetadata: currentUser.unsafeMetadata,
        emailAddresses: currentUser.emailAddresses,
        firstName: currentUser.firstName,
        lastName: currentUser.lastName,
      },
      sessionClaims: sessionClaims,
      analysis: {
        hasPublicMetadata: Object.keys(currentUser.publicMetadata || {}).length > 0,
        hasPrivateMetadata: Object.keys(currentUser.privateMetadata || {}).length > 0,
        hasUnsafeMetadata: Object.keys(currentUser.unsafeMetadata || {}).length > 0,
        hasStripeData: !!(currentUser.publicMetadata as any)?.stripe,
        hasSubscriptionData: !!(currentUser.publicMetadata as any)?.subscription,
        hasPlanData: !!(currentUser.publicMetadata as any)?.plan,
      },
      instructions: [
        'Look for any billing-related data in the metadata',
        'Check if Clerk has added any subscription information after payment',
        'Compare this with what you see after making a payment through Clerk',
      ],
      timestamp: new Date().toISOString(),
    });

  } catch (error: any) {
    console.error('❌ Error checking Clerk metadata:', error);
    return NextResponse.json({
      success: false,
      error: error.message,
      stack: error.stack
    }, { status: 500 });
  }
}

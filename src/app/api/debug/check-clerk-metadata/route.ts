import { NextResponse } from 'next/server';
import { auth, clerkClient } from '@clerk/nextjs/server';

export async function GET() {
  try {
    // Get the current user
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    console.log(`🔍 Checking Clerk metadata for user ${userId}`);

    // Get session claims to see what's available in the session
    const { sessionClaims } = await auth();

    return NextResponse.json({
      success: true,
      userId: userId,
      sessionClaims: sessionClaims,
      sessionMetadata: {
        publicMetadata: sessionClaims?.publicMetadata,
        privateMetadata: sessionClaims?.privateMetadata,
        unsafeMetadata: sessionClaims?.unsafeMetadata,
      },
      analysis: {
        hasPublicMetadata: Object.keys(sessionClaims?.publicMetadata || {}).length > 0,
        hasPrivateMetadata: Object.keys(sessionClaims?.privateMetadata || {}).length > 0,
        hasUnsafeMetadata: Object.keys(sessionClaims?.unsafeMetadata || {}).length > 0,
        hasStripeData: !!(sessionClaims?.publicMetadata as any)?.stripe,
        hasSubscriptionData: !!(sessionClaims?.publicMetadata as any)?.subscription,
        hasPlanData: !!(sessionClaims?.publicMetadata as any)?.plan,
      },
      instructions: [
        'Look for any billing-related data in the metadata',
        'Check if Clerk has added any subscription information after payment',
        'Compare this with what you see after making a payment through Clerk',
      ],
      timestamp: new Date().toISOString(),
    });

  } catch (error: any) {
    console.error('❌ Error checking Clerk metadata:', error);
    return NextResponse.json({
      success: false,
      error: error.message,
      stack: error.stack
    }, { status: 500 });
  }
}

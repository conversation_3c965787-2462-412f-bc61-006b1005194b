import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { createServiceRoleSupabaseClient } from '@/utils/supabase/server';

export async function POST() {
  try {
    // SECURITY: Only allow in development
    const isDevelopment = process.env.NODE_ENV === 'development';
    if (!isDevelopment) {
      return NextResponse.json({ 
        error: 'This endpoint is only available in development' 
      }, { status: 403 });
    }

    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    console.log(`🔧 Fixing Pro/Premium credits for user ${userId}`);

    const supabase = createServiceRoleSupabaseClient();

    // Get current user data
    const { data: currentUser, error: userError } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single();

    if (userError || !currentUser) {
      return NextResponse.json({
        success: false,
        error: 'Failed to fetch user data',
        details: userError?.message
      }, { status: 500 });
    }

    const userPlan = currentUser.subscription_type?.toLowerCase();
    console.log(`📋 User plan: ${userPlan}`);
    console.log(`📋 Current credits: ${currentUser.credits}`);

    if (!['pro', 'premium'].includes(userPlan)) {
      return NextResponse.json({
        success: false,
        message: 'User is not on Pro or Premium plan',
        currentPlan: userPlan,
        note: 'This fix is only for Pro/Premium users with incorrect credit amounts',
      });
    }

    // Correct credit amounts
    const correctCredits = {
      pro: 3500,
      premium: 14500,
    };

    const correctAmount = correctCredits[userPlan as keyof typeof correctCredits];

    // Get all user's credit transactions
    const { data: allTransactions, error: transError } = await supabase
      .from('credit_transactions')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (transError) {
      console.error('❌ Error fetching transactions:', transError);
    }

    // Find incorrect Pro/Premium transactions
    const incorrectTransactions = allTransactions?.filter(t => 
      t.plan_id === userPlan && 
      t.transaction_type === 'subscription' &&
      t.amount !== correctAmount
    ) || [];

    console.log(`📋 Found ${incorrectTransactions.length} incorrect ${userPlan} transactions`);

    if (incorrectTransactions.length === 0) {
      return NextResponse.json({
        success: true,
        message: `User already has correct ${userPlan} credits`,
        currentCredits: currentUser.credits,
        correctAmount: correctAmount,
        noFixNeeded: true,
      });
    }

    // Delete incorrect transactions
    if (incorrectTransactions.length > 0) {
      console.log(`🗑️ Deleting ${incorrectTransactions.length} incorrect ${userPlan} transactions`);
      
      const { error: deleteError } = await supabase
        .from('credit_transactions')
        .delete()
        .eq('user_id', userId)
        .eq('plan_id', userPlan)
        .eq('transaction_type', 'subscription');

      if (deleteError) {
        console.error('❌ Error deleting incorrect transactions:', deleteError);
        return NextResponse.json({
          success: false,
          error: 'Failed to delete incorrect transactions',
          details: deleteError.message
        }, { status: 500 });
      }

      console.log('✅ Deleted incorrect transactions');
    }

    // Add correct credits
    console.log(`➕ Adding correct ${userPlan} credits: ${correctAmount}`);
    
    const { error: creditError } = await supabase
      .from('credit_transactions')
      .insert({
        user_id: userId,
        amount: correctAmount,
        description: `${userPlan.charAt(0).toUpperCase() + userPlan.slice(1)} plan: Corrected credits`,
        transaction_type: 'subscription',
        expires_at: null, // Pro/Premium credits don't expire
        plan_id: userPlan,
      });

    if (creditError) {
      console.error('❌ Error adding correct credits:', creditError);
      return NextResponse.json({
        success: false,
        error: 'Failed to add correct credits',
        details: creditError.message
      }, { status: 500 });
    }

    // Recalculate total credits
    const { data: updatedTransactions, error: recalcError } = await supabase
      .from('credit_transactions')
      .select('amount')
      .eq('user_id', userId)
      .eq('is_expired', false);

    if (!recalcError) {
      let newTotal = 0;
      for (const transaction of updatedTransactions || []) {
        newTotal += transaction.amount || 0;
      }
      
      console.log(`🔄 Updating user total credits to: ${newTotal}`);
      
      const { error: updateUserError } = await supabase
        .from('users')
        .update({
          credits: newTotal,
          updated_at: new Date().toISOString(),
        })
        .eq('id', userId);

      if (updateUserError) {
        console.error('❌ Error updating user credits:', updateUserError);
      } else {
        console.log('✅ Updated user credits successfully');
      }
    }

    // Get final user data
    const { data: finalUser, error: finalError } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single();

    return NextResponse.json({
      success: true,
      message: `${userPlan.charAt(0).toUpperCase() + userPlan.slice(1)} credits fixed successfully`,
      changes: {
        plan: userPlan,
        previousCredits: currentUser.credits,
        newCredits: finalUser?.credits || 0,
        creditsDifference: (finalUser?.credits || 0) - currentUser.credits,
        incorrectTransactionsDeleted: incorrectTransactions.length,
        correctCreditsAdded: correctAmount,
      },
      userBefore: currentUser,
      userAfter: finalUser,
      instructions: [
        'Refresh your dashboard to see corrected credits',
        `You should now have the correct ${userPlan} credits amount`,
        `${userPlan === 'pro' ? '3,500' : '14,500'} credits that don't expire`,
      ],
      timestamp: new Date().toISOString(),
    });

  } catch (error: any) {
    console.error('❌ Error fixing Pro/Premium credits:', error);
    return NextResponse.json({
      success: false,
      error: error.message,
      stack: error.stack
    }, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Fix Pro/Premium credits endpoint',
    description: 'Fixes incorrect credit amounts for Pro (3,500) and Premium (14,500) plans',
    usage: 'POST to fix your Pro/Premium credits',
    correctAmounts: {
      pro: 3500,
      premium: 14500,
    },
    note: 'Only works if user currently has Pro or Premium plan',
    security: 'Only available in development mode',
  });
}

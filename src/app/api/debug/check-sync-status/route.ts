import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { createServiceRoleSupabaseClient } from '@/utils/supabase/server';

export async function GET() {
  try {
    const { userId, sessionClaims } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    console.log(`🔍 Checking sync status for user ${userId}`);

    const supabase = createServiceRoleSupabaseClient();

    // Get current Clerk session data
    const clerkPlan = sessionClaims?.pla as string;
    
    // Get current database user data
    const { data: dbUser, error: dbError } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single();

    if (dbError) {
      return NextResponse.json({
        success: false,
        error: 'Failed to fetch user from database',
        details: dbError.message
      }, { status: 500 });
    }

    // Get credit transactions
    const { data: creditTransactions, error: creditError } = await supabase
      .from('credit_transactions')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(10);

    if (creditError) {
      console.error('❌ Error fetching credit transactions:', creditError);
    }

    // Get subscription history
    const { data: subHistory, error: subError } = await supabase
      .from('subscription_history')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(5);

    if (subError) {
      console.error('❌ Error fetching subscription history:', subError);
    }

    // Analyze sync status
    const clerkPlanName = clerkPlan?.replace('u:', '').replace('_user', '') || 'free';
    const dbPlanName = dbUser?.subscription_type?.toLowerCase() || 'free';
    const isInSync = clerkPlanName === dbPlanName;

    // Calculate expected credits based on plan
    const planCreditsMap: Record<string, number> = {
      'free': 50,
      'standard': 750, // 50 initial + 700 from plan
      'pro': 50 + 999999, // Unlimited
      'premium': 50 + 999999, // Unlimited
    };

    const expectedCredits = planCreditsMap[clerkPlanName] || 50;
    const actualCredits = dbUser?.credits || 0;
    const creditsMatch = actualCredits >= (clerkPlanName === 'free' ? 0 : 700); // Allow some flexibility

    return NextResponse.json({
      success: true,
      syncStatus: {
        isInSync,
        creditsMatch,
        needsSync: !isInSync || !creditsMatch,
      },
      clerkData: {
        userId,
        sessionClaims,
        planFromClaims: clerkPlan,
        parsedPlan: clerkPlanName,
      },
      databaseData: {
        user: dbUser,
        planFromDb: dbPlanName,
        creditsFromDb: actualCredits,
      },
      analysis: {
        planComparison: {
          clerk: clerkPlanName,
          database: dbPlanName,
          match: isInSync,
        },
        creditComparison: {
          expected: expectedCredits,
          actual: actualCredits,
          match: creditsMatch,
        },
      },
      recentActivity: {
        creditTransactions: creditTransactions?.slice(0, 5) || [],
        subscriptionHistory: subHistory || [],
      },
      recommendations: [
        !isInSync ? `Plan mismatch: Clerk shows "${clerkPlanName}" but database shows "${dbPlanName}"` : null,
        !creditsMatch ? `Credit mismatch: Expected ~${expectedCredits} but found ${actualCredits}` : null,
        (!isInSync || !creditsMatch) ? 'Run manual sync: POST /api/sync-clerk-subscription' : null,
      ].filter(Boolean),
      timestamp: new Date().toISOString(),
    });

  } catch (error: any) {
    console.error('❌ Error checking sync status:', error);
    return NextResponse.json({
      success: false,
      error: error.message,
      stack: error.stack
    }, { status: 500 });
  }
}

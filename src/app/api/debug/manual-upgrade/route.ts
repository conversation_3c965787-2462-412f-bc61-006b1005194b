import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { createServiceRoleSupabaseClient } from '@/utils/supabase/server';

export async function POST(request: Request) {
  try {
    // Get the current user
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    const { planId = 'standard' } = await request.json();

    const supabase = createServiceRoleSupabaseClient();

    console.log(`🧪 Manual upgrade test for user ${userId} to plan ${planId}`);

    // Test the database function directly
    const { error: upgradeError } = await supabase.rpc('update_user_subscription', {
      p_user_id: userId,
      p_plan_id: planId,
      p_stripe_customer_id: `cus_test_${Date.now()}`,
      p_stripe_subscription_id: `sub_test_${Date.now()}`,
      p_stripe_price_id: 'price_test_123',
      p_subscription_status: 'active',
      p_period_start: new Date().toISOString(),
      p_period_end: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
    });

    if (upgradeError) {
      console.error('❌ Error in manual upgrade:', upgradeError);
      return NextResponse.json({
        success: false,
        error: upgradeError.message,
        details: upgradeError
      }, { status: 500 });
    }

    // Get updated user data
    const { data: updatedUser, error: userError } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single();

    if (userError) {
      console.error('❌ Error fetching updated user:', userError);
      return NextResponse.json({
        success: false,
        error: 'Failed to fetch updated user data'
      }, { status: 500 });
    }

    // Get recent credit transactions
    const { data: credits, error: creditsError } = await supabase
      .from('credit_transactions')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(5);

    // Get recent subscription history
    const { data: subscriptionHistory, error: historyError } = await supabase
      .from('subscription_history')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(3);

    console.log(`✅ Manual upgrade completed for user ${userId}`);

    return NextResponse.json({
      success: true,
      message: `Successfully upgraded user to ${planId} plan`,
      results: {
        user: updatedUser,
        recent_credits: credits || [],
        subscription_history: subscriptionHistory || [],
        test_data: {
          planId,
          userId,
          timestamp: new Date().toISOString()
        }
      }
    });

  } catch (error: any) {
    console.error('❌ Error in manual upgrade test:', error);
    return NextResponse.json({
      success: false,
      error: error.message,
      stack: error.stack
    }, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Manual upgrade test endpoint',
    usage: 'POST with { "planId": "standard" | "pro" | "premium" }',
    note: 'This will manually upgrade the current user for testing purposes'
  });
}

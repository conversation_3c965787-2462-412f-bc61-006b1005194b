import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { clerkClient } from '@clerk/nextjs/server';

export async function POST(request: Request) {
  try {
    // SECURITY: Only allow in development
    const isDevelopment = process.env.NODE_ENV === 'development';
    if (!isDevelopment) {
      return NextResponse.json({ 
        error: 'This endpoint is only available in development' 
      }, { status: 403 });
    }

    // Get the current user
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    const { planId = 'standard' } = await request.json();

    console.log(`🧪 Testing direct Clerk metadata update for user ${userId} with plan ${planId}`);

    // Test different metadata formats that <PERSON> might use
    const testFormats = [
      // Format 1: Following the documentation exactly
      {
        name: 'Documentation Format',
        metadata: {
          publicMetadata: {
            stripe: {
              payment: 'paid',
              plan: planId,
              status: 'complete',
            }
          }
        }
      },
      // Format 2: Direct plan in public metadata
      {
        name: 'Direct Plan Format',
        metadata: {
          publicMetadata: {
            plan: planId,
            subscription_status: 'active',
            payment_status: 'paid',
          }
        }
      },
      // Format 3: Subscription object format
      {
        name: 'Subscription Object Format',
        metadata: {
          publicMetadata: {
            subscription: {
              plan: planId,
              status: 'active',
              payment: 'paid',
              created_at: new Date().toISOString(),
            }
          }
        }
      }
    ];

    const results = [];

    for (const format of testFormats) {
      try {
        console.log(`📋 Testing ${format.name}:`, JSON.stringify(format.metadata, null, 2));

        // Update Clerk user metadata
        await clerkClient.users.updateUserMetadata(userId, format.metadata);

        console.log(`✅ ${format.name} metadata updated successfully`);

        // Wait for webhook processing
        await new Promise(resolve => setTimeout(resolve, 1000));

        results.push({
          format: format.name,
          success: true,
          metadata: format.metadata,
        });

      } catch (error: any) {
        console.error(`❌ Error with ${format.name}:`, error);
        results.push({
          format: format.name,
          success: false,
          error: error.message,
        });
      }
    }

    // Wait a bit more for all webhooks to process
    await new Promise(resolve => setTimeout(resolve, 3000));

    // Check webhook logs
    const webhookLogsResponse = await fetch(`${process.env.NEXT_PUBLIC_SITE_URL || 'https://guardiavision.com'}/api/debug/webhook-logs`);
    const webhookLogs = await webhookLogsResponse.json();

    // Get current user data
    const currentUser = await clerkClient.users.getUser(userId);

    return NextResponse.json({
      success: true,
      message: `Tested multiple Clerk metadata formats for ${planId} plan`,
      results,
      currentUserMetadata: {
        publicMetadata: currentUser.publicMetadata,
        privateMetadata: currentUser.privateMetadata,
        unsafeMetadata: currentUser.unsafeMetadata,
      },
      webhookActivity: webhookLogs.webhook_activity || {},
      instructions: [
        'Check your dashboard to see if any format worked',
        'Check Vercel function logs for Clerk webhook processing',
        'Look for "BILLING DATA DETECTED" or "STRIPE BILLING DATA DETECTED" messages',
      ],
      timestamp: new Date().toISOString(),
    });

  } catch (error: any) {
    console.error('❌ Error testing Clerk metadata formats:', error);
    return NextResponse.json({
      success: false,
      error: error.message,
      stack: error.stack
    }, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Direct Clerk metadata testing endpoint',
    description: 'Tests different metadata formats to see which one triggers subscription processing',
    usage: 'POST with { "planId": "standard" | "pro" | "premium" }',
    purpose: 'Find the exact metadata format that Clerk billing uses',
    security: 'Only available in development mode'
  });
}

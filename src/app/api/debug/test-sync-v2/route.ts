import { NextResponse } from 'next/server';

export async function GET() {
  return NextResponse.json({
    message: 'Test Sync V2 Endpoint',
    description: 'Quick test for the new sync function without variable conflicts',
    
    status: 'Ready to test V2 sync function',
    
    testInstructions: [
      '1. Run the manual test below in browser console',
      '2. Check console logs for detailed sync process',
      '3. Verify dashboard updates with Standard plan',
      '4. Check that credits increase to 750 (50 + 700)',
    ],

    manualTest: `
// Test the V2 sync function directly
fetch('/api/sync-clerk-subscription-v2', {
  method: 'POST'
})
.then(response => response.json())
.then(data => {
  console.log('=== SYNC V2 RESULT ===');
  console.log(data);
  
  if (data.success) {
    console.log('✅ Sync successful!');
    console.log('📋 Plan:', data.results.planName);
    console.log('💳 Credits added:', data.results.creditsAdded);
    console.log('💰 Total credits:', data.results.totalCredits);
    
    alert('✅ Sync successful! Refreshing dashboard...');
    window.location.reload();
  } else {
    console.error('❌ Sync failed:', data.error);
    alert('❌ Sync failed: ' + data.error);
  }
})
.catch(error => {
  console.error('❌ Network error:', error);
  alert('❌ Network error: ' + error.message);
});
    `,

    expectedResult: {
      success: true,
      message: 'Successfully synced to Standard plan',
      results: {
        clerkPlan: 'u:standard',
        mappedPlan: 'standard',
        planName: 'Standard',
        creditsAdded: 700,
        totalCredits: 750,
      },
    },

    expectedLogs: [
      '🔄 Syncing Clerk subscription V2 for user user_xxx',
      '📋 Raw plan from session: u:standard',
      '🔄 Mapping Clerk plan "standard" to our plan "standard"',
      '📋 Current plan: free, Target plan: standard',
      '🔄 Upgrading user from free to standard',
      '✅ Added 700 credits for standard plan',
      '✅ Updated total credits to 750',
      '✅ Clerk subscription synced successfully V2',
    ],

    troubleshooting: {
      stillFails: 'Use manual upgrade: POST /api/debug/manual-upgrade-after-payment',
      noCredits: 'Check if credits were already added for standard plan',
      wrongPlan: 'Verify Clerk session shows u:standard in claims',
    },

    quickActions: {
      testV2Sync: 'Copy and run the manualTest JavaScript above',
      checkStatus: 'Visit /api/debug/check-payment-status',
      manualUpgrade: 'Use /api/debug/manual-upgrade-after-payment if needed',
    },

    timestamp: new Date().toISOString(),
  });
}

export async function POST() {
  // Redirect to the actual V2 sync endpoint
  try {
    const syncResponse = await fetch(`${process.env.NEXT_PUBLIC_SITE_URL || 'https://guardiavision.com'}/api/sync-clerk-subscription-v2`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const result = await syncResponse.json();
    
    return NextResponse.json({
      success: true,
      message: 'V2 sync test completed',
      syncResult: result,
      instructions: result.success ? 
        ['Sync successful! Check your dashboard for updates.'] :
        ['Sync failed. Check the error details and try manual upgrade if needed.'],
    });

  } catch (error: any) {
    return NextResponse.json({
      success: false,
      error: 'Failed to test V2 sync',
      details: error.message,
    }, { status: 500 });
  }
}

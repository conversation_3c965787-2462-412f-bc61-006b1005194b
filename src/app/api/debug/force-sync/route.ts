import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';

export async function POST() {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    console.log(`🔄 Force syncing subscription for user ${userId}`);

    // Call the sync endpoint
    const syncResponse = await fetch(`${process.env.NEXT_PUBLIC_SITE_URL || 'https://guardiavision.com'}/api/sync-clerk-subscription`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // Forward the authorization if needed
      },
    });

    const syncResult = await syncResponse.json();

    if (syncResult.success) {
      console.log('✅ Force sync completed successfully');
      
      return NextResponse.json({
        success: true,
        message: 'Subscription sync completed successfully',
        syncResult,
        instructions: [
          'Check your dashboard to see updated plan and credits',
          'Refresh the page if changes are not visible immediately',
          'Check /api/debug/check-sync-status to verify sync status',
        ],
        timestamp: new Date().toISOString(),
      });
    } else {
      console.error('❌ Force sync failed:', syncResult.error);
      
      return NextResponse.json({
        success: false,
        error: 'Sync failed',
        details: syncResult,
        troubleshooting: [
          'Check if you have an active subscription in Clerk',
          'Verify your session claims contain plan information',
          'Check Vercel function logs for detailed error messages',
        ],
      }, { status: 500 });
    }

  } catch (error: any) {
    console.error('❌ Error in force sync:', error);
    return NextResponse.json({
      success: false,
      error: error.message,
      stack: error.stack
    }, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Force subscription sync endpoint',
    description: 'Manually triggers the Clerk subscription sync process',
    usage: 'POST to force sync subscription from Clerk to database',
    note: 'This will update plan and credits based on current Clerk session claims',
  });
}

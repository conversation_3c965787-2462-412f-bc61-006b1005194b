import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { createServiceRoleSupabaseClient } from '@/utils/supabase/server';

export async function GET() {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    console.log(`🔍 Checking database plan configurations`);

    const supabase = createServiceRoleSupabaseClient();

    // Get all subscription plans from database
    const { data: plans, error: plansError } = await supabase
      .from('subscription_plans')
      .select('*')
      .order('id');

    if (plansError) {
      return NextResponse.json({
        success: false,
        error: 'Failed to fetch subscription plans',
        details: plansError.message
      }, { status: 500 });
    }

    // Expected vs actual comparison
    const expectedCredits = {
      free: 50,
      standard: 700,
      pro: 3500,
      premium: 14500,
    };

    const planAnalysis = plans?.map(plan => {
      const expected = expectedCredits[plan.id as keyof typeof expectedCredits] || 0;
      const actual = plan.credits_included || 0;
      const isCorrect = actual === expected;

      return {
        planId: plan.id,
        planName: plan.name,
        expectedCredits: expected,
        actualCredits: actual,
        isCorrect,
        needsUpdate: !isCorrect,
        difference: actual - expected,
        fullPlanData: plan,
      };
    }) || [];

    const incorrectPlans = planAnalysis.filter(p => !p.isCorrect);

    return NextResponse.json({
      success: true,
      databaseStatus: {
        totalPlans: planAnalysis.length,
        correctPlans: planAnalysis.filter(p => p.isCorrect).length,
        incorrectPlans: incorrectPlans.length,
        allCorrect: incorrectPlans.length === 0,
      },
      planAnalysis,
      incorrectPlans,
      expectedCredits,
      recommendations: [
        ...incorrectPlans.map(p => 
          `🔧 ${p.planName}: Database shows ${p.actualCredits}, should be ${p.expectedCredits}`
        ),
        incorrectPlans.length > 0 ? '🚀 Run /api/debug/force-fix-database-plans to correct' : null,
      ].filter(Boolean),
      possibleSources: [
        'Database subscription_plans table has wrong values',
        'Clerk billing configuration has wrong credit amounts',
        'Stripe product metadata has wrong credit values',
        'Sync function is reading from wrong source',
      ],
      nextSteps: [
        '1. Fix database plans first',
        '2. Check Clerk billing configuration',
        '3. Check Stripe product metadata',
        '4. Override with hardcoded values if needed',
      ],
      timestamp: new Date().toISOString(),
    });

  } catch (error: any) {
    console.error('❌ Error checking database plans:', error);
    return NextResponse.json({
      success: false,
      error: error.message,
      stack: error.stack
    }, { status: 500 });
  }
}

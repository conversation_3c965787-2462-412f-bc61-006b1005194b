import { NextResponse } from 'next/server';

export async function GET() {
  return NextResponse.json({
    message: 'Credit System Test Page',
    description: 'Test both credit expiration and credit purchase features',
    
    features: {
      creditExpiration: {
        endpoint: '/api/debug/expire-credits-now',
        description: 'Manually expire credits that have passed their expiration date',
        usage: 'POST to trigger credit expiration',
        note: 'This will update user credits to reflect only non-expired credits'
      },
      
      creditPurchase: {
        endpoint: '/api/purchase-credits',
        description: 'Purchase additional credits through Stripe',
        usage: 'POST with packageId to create checkout session',
        packages: {
          small: { credits: 500, price: '$5.00' },
          medium: { credits: 1200, price: '$10.00' },
          large: { credits: 3000, price: '$20.00' },
          xlarge: { credits: 7500, price: '$40.00' }
        }
      }
    },

    testInstructions: [
      '1. First, test credit expiration:',
      '   - Change credit expiration dates to past dates in database',
      '   - Call POST /api/debug/expire-credits-now',
      '   - Check that credits are reduced to only non-expired ones',
      '',
      '2. Then, test credit purchase:',
      '   - Call POST /api/purchase-credits with packageId',
      '   - Complete Stripe checkout',
      '   - Verify credits are added to account',
      '',
      '3. Integration test:',
      '   - Purchase credits',
      '   - Set expiration date to past',
      '   - Run expiration',
      '   - Verify only non-expired credits remain'
    ],

    javascriptTests: {
      expireCredits: `
// Test credit expiration
fetch('/api/debug/expire-credits-now', {
  method: 'POST'
})
.then(response => response.json())
.then(data => console.log('Credit expiration result:', data));
      `,
      
      purchaseCredits: `
// Test credit purchase (medium package)
fetch('/api/purchase-credits', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ packageId: 'medium' })
})
.then(response => response.json())
.then(data => {
  console.log('Purchase result:', data);
  if (data.checkoutUrl) {
    window.location.href = data.checkoutUrl;
  }
});
      `
    },

    timestamp: new Date().toISOString()
  });
}

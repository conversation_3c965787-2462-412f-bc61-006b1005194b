import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { createServiceRoleSupabaseClient } from '@/utils/supabase/server';

export async function POST() {
  try {
    const { userId, sessionClaims } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    console.log(`🚀 Manual Pro upgrade for user ${userId}`);

    const supabase = createServiceRoleSupabaseClient();

    // Check current Clerk plan
    const clerkPlan = sessionClaims?.pla as string;
    const clerkPlanName = clerkPlan?.replace('u:', '').replace('_user', '') || 'free';

    console.log(`📋 Clerk plan: ${clerkPlan} -> ${clerkPlanName}`);

    // Get current user data
    const { data: currentUser, error: currentUserError } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single();

    if (currentUserError) {
      return NextResponse.json({
        success: false,
        error: 'Failed to fetch current user data',
        details: currentUserError.message
      }, { status: 500 });
    }

    // Get Pro plan details
    const { data: proPlan, error: proPlanError } = await supabase
      .from('subscription_plans')
      .select('*')
      .eq('id', 'pro')
      .single();

    if (proPlanError || !proPlan) {
      return NextResponse.json({
        success: false,
        error: 'Pro plan not found in database',
        details: proPlanError?.message
      }, { status: 500 });
    }

    console.log(`📋 Pro plan details:`, proPlan);

    // Update user to Pro plan
    const { error: userUpdateError } = await supabase
      .from('users')
      .update({
        subscription_type: 'Pro',
        subscription_status: 'active',
        last_payment_date: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .eq('id', userId);

    if (userUpdateError) {
      return NextResponse.json({
        success: false,
        error: 'Failed to update user to Pro plan',
        details: userUpdateError.message
      }, { status: 500 });
    }

    // Add Pro credits (unlimited = 999999)
    let creditsAdded = 0;
    if (proPlan.credits_included > 0) {
      // Check if user already has Pro credits
      const { data: existingProCredits } = await supabase
        .from('credit_transactions')
        .select('id')
        .eq('user_id', userId)
        .eq('plan_id', 'pro')
        .eq('transaction_type', 'subscription')
        .limit(1);

      if (!existingProCredits || existingProCredits.length === 0) {
        // Add unlimited credits (no expiration for Pro)
        const { error: creditError } = await supabase
          .from('credit_transactions')
          .insert({
            user_id: userId,
            amount: proPlan.credits_included, // 999999
            description: `Manual Pro upgrade: Unlimited credits`,
            transaction_type: 'subscription',
            expires_at: null, // Pro credits don't expire
            plan_id: 'pro',
          });

        if (creditError) {
          console.error('❌ Error adding Pro credits:', creditError);
        } else {
          // Update user's total credits to unlimited
          await supabase
            .from('users')
            .update({ 
              credits: proPlan.credits_included, // 999999
              credits_expire_at: null, // No expiration
            })
            .eq('id', userId);

          creditsAdded = proPlan.credits_included;
          console.log(`✅ Added ${creditsAdded} unlimited credits`);
        }
      } else {
        console.log('⚠️ User already has Pro credits, updating total only');
        
        // Just update the total to unlimited
        await supabase
          .from('users')
          .update({ 
            credits: proPlan.credits_included, // 999999
            credits_expire_at: null,
          })
          .eq('id', userId);
      }
    }

    // Record subscription history
    await supabase
      .from('subscription_history')
      .insert({
        user_id: userId,
        plan_id: 'pro',
        action: 'manual_upgrade',
        effective_date: new Date().toISOString(),
        metadata: {
          clerk_plan: clerkPlan,
          upgrade_method: 'manual_pro_upgrade',
          credits_added: creditsAdded,
          previous_plan: currentUser.subscription_type,
        },
      });

    // Get updated user data
    const { data: updatedUser, error: userError } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single();

    console.log(`✅ Manual Pro upgrade completed for user ${userId}`);

    return NextResponse.json({
      success: true,
      message: 'Successfully upgraded to Pro plan',
      results: {
        previousPlan: currentUser.subscription_type,
        newPlan: 'Pro',
        creditsAdded: creditsAdded,
        totalCredits: updatedUser?.credits || 0,
        clerkPlan: clerkPlanName,
        user: updatedUser,
      },
      instructions: [
        'Refresh your dashboard to see the Pro plan',
        'You now have unlimited credits (999,999)',
        'Pro plan includes fastest processing and priority support',
      ],
      timestamp: new Date().toISOString(),
    });

  } catch (error: any) {
    console.error('❌ Error in manual Pro upgrade:', error);
    return NextResponse.json({
      success: false,
      error: error.message,
      stack: error.stack
    }, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Manual Pro upgrade endpoint',
    description: 'Manually upgrades user to Pro plan with unlimited credits',
    usage: 'POST to upgrade current user to Pro plan',
    note: 'This bypasses the sync system and directly updates the database',
  });
}

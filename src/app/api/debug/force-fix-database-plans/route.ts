import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { createServiceRoleSupabaseClient } from '@/utils/supabase/server';

export async function POST() {
  try {
    // SECURITY: Only allow in development
    const isDevelopment = process.env.NODE_ENV === 'development';
    if (!isDevelopment) {
      return NextResponse.json({ 
        error: 'This endpoint is only available in development' 
      }, { status: 403 });
    }

    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    console.log(`🔧 Force fixing database plan credits`);

    const supabase = createServiceRoleSupabaseClient();

    // HARDCODED correct credit amounts - override everything
    const CORRECT_CREDITS = {
      free: 50,
      standard: 700,
      pro: 3500,        // NOT 1000749
      premium: 14500,   // NOT 2000749
    };

    const results = [];

    // Force update each plan with correct credits
    for (const [planId, correctCredits] of Object.entries(CORRECT_CREDITS)) {
      console.log(`🔄 Force updating ${planId} plan to ${correctCredits} credits`);

      const { data: updatedPlan, error: updateError } = await supabase
        .from('subscription_plans')
        .update({
          credits_included: correctCredits,
          updated_at: new Date().toISOString(),
        })
        .eq('id', planId)
        .select()
        .single();

      if (updateError) {
        console.error(`❌ Error force updating ${planId} plan:`, updateError);
        results.push({
          planId,
          success: false,
          error: updateError.message,
          targetCredits: correctCredits,
        });
      } else {
        console.log(`✅ Force updated ${planId} plan successfully`);
        results.push({
          planId,
          success: true,
          oldCredits: 'unknown',
          newCredits: correctCredits,
          plan: updatedPlan,
        });
      }
    }

    // Verify all plans are now correct
    const { data: verifyPlans, error: verifyError } = await supabase
      .from('subscription_plans')
      .select('*')
      .order('id');

    if (verifyError) {
      console.error('❌ Error verifying updated plans:', verifyError);
    }

    const verification = verifyPlans?.map(plan => ({
      planId: plan.id,
      credits: plan.credits_included,
      expected: CORRECT_CREDITS[plan.id as keyof typeof CORRECT_CREDITS],
      isCorrect: plan.credits_included === CORRECT_CREDITS[plan.id as keyof typeof CORRECT_CREDITS],
    })) || [];

    const allCorrect = verification.every(v => v.isCorrect);
    const successCount = results.filter(r => r.success).length;

    return NextResponse.json({
      success: allCorrect,
      message: allCorrect ? 
        'All plans force updated successfully' : 
        'Some plans failed to update',
      results,
      verification,
      summary: {
        totalPlans: results.length,
        successful: successCount,
        failed: results.length - successCount,
        allCorrect,
      },
      CORRECT_CREDITS,
      updatedPlans: verifyPlans,
      instructions: [
        allCorrect ? '✅ Database plans are now correct' : '❌ Some plans still incorrect',
        'Test sync again to see if credits are now correct',
        'If still wrong, the issue is in Clerk/Stripe configuration',
      ],
      nextSteps: allCorrect ? [
        'Test plan upgrade to see if credits are now correct',
        'If still wrong, check Clerk billing configuration',
        'May need to override sync logic with hardcoded values',
      ] : [
        'Check database permissions',
        'Verify plan IDs exist in database',
        'Check for database constraints',
      ],
      timestamp: new Date().toISOString(),
    });

  } catch (error: any) {
    console.error('❌ Error force fixing database plans:', error);
    return NextResponse.json({
      success: false,
      error: error.message,
      stack: error.stack
    }, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Force fix database plans endpoint',
    description: 'Forces database to have correct credit amounts, overriding any existing values',
    correctCredits: {
      free: 50,
      standard: 700,
      pro: 3500,
      premium: 14500,
    },
    incorrectValues: {
      pro: 1000749,
      premium: 2000749,
    },
    usage: 'POST to force update all plan credits in database',
    security: 'Only available in development mode',
    note: 'This will override any existing values with hardcoded correct amounts',
  });
}

import { NextResponse } from 'next/server';
import { createServiceRoleSupabaseClient } from '@/utils/supabase/server';

export async function GET() {
  try {
    const supabase = createServiceRoleSupabaseClient();

    // Check if new tables exist by trying to query them
    const tableChecks = {
      subscription_plans: false,
      payment_history: false,
      subscription_history: false,
    };

    for (const tableName of Object.keys(tableChecks)) {
      try {
        const { error } = await supabase.from(tableName).select('*').limit(1);
        tableChecks[tableName as keyof typeof tableChecks] = !error;
      } catch (e) {
        tableChecks[tableName as keyof typeof tableChecks] = false;
      }
    }

    // Check if new columns exist in users table by trying to select them
    const columnChecks = {
      subscription_type: false,
      stripe_customer_id: false,
      stripe_subscription_id: false,
      credits_expire_at: false,
    };

    try {
      const { error } = await supabase
        .from('users')
        .select('subscription_type, stripe_customer_id, stripe_subscription_id, credits_expire_at')
        .limit(1);

      if (!error) {
        Object.keys(columnChecks).forEach(col => {
          columnChecks[col as keyof typeof columnChecks] = true;
        });
      }
    } catch (e) {
      // Columns don't exist
    }

    // Check if functions exist by trying to call them
    const functionChecks = {
      update_user_subscription: false,
      add_credits_with_expiration: false,
      expire_credits: false,
    };

    // Test expire_credits function (safest to test)
    try {
      const { error } = await supabase.rpc('expire_credits');
      functionChecks.expire_credits = !error;
    } catch (e: any) {
      // Check if function exists by looking at the error message
      functionChecks.expire_credits = !e.message?.includes('function') && !e.message?.includes('does not exist');
    }

    // Test add_credits_with_expiration function with dummy data
    try {
      // This will fail if function doesn't exist, but won't actually add credits due to invalid user_id
      await supabase.rpc('add_credits_with_expiration', {
        p_user_id: 'test_user_id_that_does_not_exist',
        p_amount: 0,
        p_description: 'test',
      });
      functionChecks.add_credits_with_expiration = true;
    } catch (e: any) {
      // If error is about user not existing, function exists
      // If error is about function not existing, function doesn't exist
      functionChecks.add_credits_with_expiration = e.message?.includes('violates foreign key constraint') ||
                                                   e.message?.includes('insert or update on table');
    }

    // Test update_user_subscription function
    try {
      await supabase.rpc('update_user_subscription', {
        p_user_id: 'test_user_id_that_does_not_exist',
        p_plan_id: 'free',
      });
      functionChecks.update_user_subscription = true;
    } catch (e: any) {
      functionChecks.update_user_subscription = e.message?.includes('Plan not found') ||
                                               e.message?.includes('violates foreign key constraint');
    }

    return NextResponse.json({
      success: true,
      database_status: {
        tables: tableChecks,
        columns: columnChecks,
        functions: functionChecks,
        migration_applied: Object.values(tableChecks).every(Boolean) &&
                          Object.values(columnChecks).every(Boolean) &&
                          Object.values(functionChecks).every(Boolean)
      }
    });

  } catch (error: any) {
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}

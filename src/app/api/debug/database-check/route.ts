import { NextResponse } from 'next/server';
import { createServiceRoleSupabaseClient } from '@/utils/supabase/server';

export async function GET() {
  try {
    const supabase = createServiceRoleSupabaseClient();

    // Check if new tables exist
    const { data: tables, error: tablesError } = await supabase
      .rpc('exec_sql', { 
        sql: `
          SELECT table_name 
          FROM information_schema.tables 
          WHERE table_schema = 'public' 
          AND table_name IN ('subscription_plans', 'payment_history', 'subscription_history');
        `
      });

    // Check if new columns exist in users table
    const { data: columns, error: columnsError } = await supabase
      .rpc('exec_sql', { 
        sql: `
          SELECT column_name 
          FROM information_schema.columns 
          WHERE table_schema = 'public' 
          AND table_name = 'users' 
          AND column_name IN ('subscription_type', 'stripe_customer_id', 'stripe_subscription_id', 'credits_expire_at');
        `
      });

    // Check if functions exist
    const { data: functions, error: functionsError } = await supabase
      .rpc('exec_sql', { 
        sql: `
          SELECT routine_name 
          FROM information_schema.routines 
          WHERE routine_schema = 'public' 
          AND routine_name IN ('update_user_subscription', 'add_credits_with_expiration', 'expire_credits');
        `
      });

    return NextResponse.json({
      success: true,
      database_status: {
        tables: tables || [],
        columns: columns || [],
        functions: functions || [],
        errors: {
          tables: tablesError?.message,
          columns: columnsError?.message,
          functions: functionsError?.message,
        }
      }
    });

  } catch (error: any) {
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}

import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';

export async function POST(request: Request) {
  try {
    // SECURITY: Only allow in development
    const isDevelopment = process.env.NODE_ENV === 'development';
    const adminSecret = request.headers.get('x-admin-secret');
    const validAdminSecret = process.env.ADMIN_SECRET_KEY;

    if (!isDevelopment && (!adminSecret || !validAdminSecret || adminSecret !== validAdminSecret)) {
      return NextResponse.json({ 
        error: 'Unauthorized - This endpoint is only available in development or with admin secret' 
      }, { status: 403 });
    }

    // Get the current user
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    const { planId = 'standard' } = await request.json();

    console.log(`🧪 Simulating Clerk billing webhook for user ${userId} with plan ${planId}`);

    // Create a mock Clerk webhook payload
    const mockClerkWebhook = {
      type: 'user.updated',
      data: {
        id: userId,
        email_addresses: [{ email_address: '<EMAIL>' }],
        first_name: 'Test',
        last_name: 'User',
        public_metadata: {
          subscription: {
            plan: planId,
            status: 'active',
            subscription_id: `sub_clerk_${Date.now()}`,
            customer_id: `cus_clerk_${Date.now()}`,
            price_id: `price_clerk_${planId}`,
            created_at: new Date().toISOString(),
          }
        }
      }
    };

    // Send the webhook to our Clerk webhook endpoint
    const webhookResponse = await fetch(`${process.env.NEXT_PUBLIC_SITE_URL || 'https://guardiavision.com'}/api/webhooks/clerk`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'svix-id': `msg_test_${Date.now()}`,
        'svix-timestamp': Math.floor(Date.now() / 1000).toString(),
        'svix-signature': 'v1,test_signature_for_development',
      },
      body: JSON.stringify(mockClerkWebhook),
    });

    const webhookResult = await webhookResponse.text();

    console.log(`📋 Webhook response status: ${webhookResponse.status}`);
    console.log(`📋 Webhook response: ${webhookResult}`);

    if (!webhookResponse.ok) {
      return NextResponse.json({
        success: false,
        error: 'Webhook processing failed',
        webhookStatus: webhookResponse.status,
        webhookResponse: webhookResult,
      }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      message: `Successfully simulated Clerk billing for ${planId} plan`,
      mockWebhook: mockClerkWebhook,
      webhookStatus: webhookResponse.status,
      webhookResponse: webhookResult,
      timestamp: new Date().toISOString(),
    });

  } catch (error: any) {
    console.error('❌ Error simulating Clerk billing:', error);
    return NextResponse.json({
      success: false,
      error: error.message,
      stack: error.stack
    }, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Clerk billing simulation endpoint',
    usage: 'POST with { "planId": "standard" | "pro" | "premium" }',
    note: 'This simulates how Clerk billing would trigger subscription updates',
    security: 'Only available in development mode'
  });
}

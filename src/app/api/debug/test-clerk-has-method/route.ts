import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';

export async function GET() {
  try {
    // Get the current user and check subscription status
    const { userId, has } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    console.log(`🔍 Checking Clerk subscription status for user ${userId}`);

    // Test different plan checks using Clerk's has() method
    const subscriptionChecks = {
      hasStandardPlan: has({ plan: 'standard' }),
      hasProPlan: has({ plan: 'pro' }),
      hasPremiumPlan: has({ plan: 'premium' }),
      hasAnyPlan: has({ plan: 'standard' }) || has({ plan: 'pro' }) || has({ plan: 'premium' }),
    };

    // Also check for different possible feature names
    const featureChecks = {
      hasSubscription: has({ feature: 'subscription' }),
      hasPaidAccess: has({ feature: 'paid_access' }),
      hasPremiumFeatures: has({ feature: 'premium_features' }),
    };

    return NextResponse.json({
      success: true,
      userId: userId,
      subscriptionChecks,
      featureChecks,
      instructions: [
        'These checks use Clerk\'s built-in has() method',
        'If any return true, Clerk recognizes your subscription',
        'This might be an alternative to webhook-based processing',
      ],
      note: 'If has() method works, we can use it instead of webhooks for subscription checking',
      timestamp: new Date().toISOString(),
    });

  } catch (error: any) {
    console.error('❌ Error checking Clerk subscription status:', error);
    return NextResponse.json({
      success: false,
      error: error.message,
      stack: error.stack
    }, { status: 500 });
  }
}

import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { createServiceRoleSupabaseClient } from '@/utils/supabase/server';

export async function GET() {
  try {
    const { userId, sessionClaims } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    console.log(`🔍 Checking Pro upgrade status for user ${userId}`);

    const supabase = createServiceRoleSupabaseClient();

    // Get current Clerk session data
    const clerkPlan = sessionClaims?.pla as string;
    
    // Get current database user data
    const { data: dbUser, error: dbError } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single();

    if (dbError) {
      return NextResponse.json({
        success: false,
        error: 'Failed to fetch user from database',
        details: dbError.message
      }, { status: 500 });
    }

    // Get recent credit transactions
    const { data: recentCredits, error: creditError } = await supabase
      .from('credit_transactions')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(10);

    // Get recent subscription history
    const { data: subHistory, error: subError } = await supabase
      .from('subscription_history')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(10);

    // Parse Clerk plan
    const clerkPlanName = clerkPlan?.replace('u:', '').replace('_user', '') || 'free';
    const dbPlanName = dbUser?.subscription_type?.toLowerCase() || 'free';

    // Check if this is a Pro plan
    const isClerkPro = clerkPlanName === 'pro';
    const isDbPro = dbPlanName === 'pro';

    // Get Pro plan details
    const { data: proPlan, error: proPlanError } = await supabase
      .from('subscription_plans')
      .select('*')
      .eq('id', 'pro')
      .single();

    return NextResponse.json({
      success: true,
      upgradeAnalysis: {
        clerkShowsPro: isClerkPro,
        databaseShowsPro: isDbPro,
        needsSync: isClerkPro && !isDbPro,
        planMismatch: clerkPlanName !== dbPlanName,
      },
      currentStatus: {
        clerkPlan: clerkPlanName,
        databasePlan: dbPlanName,
        currentCredits: dbUser?.credits || 0,
        subscriptionStatus: dbUser?.subscription_status,
      },
      clerkData: {
        userId,
        sessionClaims,
        rawPlan: clerkPlan,
        parsedPlan: clerkPlanName,
      },
      databaseData: {
        user: dbUser,
        proPlanDetails: proPlan,
      },
      recentActivity: {
        creditTransactions: recentCredits?.slice(0, 5) || [],
        subscriptionHistory: subHistory?.slice(0, 5) || [],
      },
      recommendations: [
        isClerkPro && !isDbPro ? '🔄 Run sync to upgrade database to Pro plan' : null,
        isClerkPro && dbUser?.credits < 999999 ? '💎 Add unlimited Pro credits' : null,
        clerkPlanName !== dbPlanName ? `📋 Plan mismatch: Clerk="${clerkPlanName}", DB="${dbPlanName}"` : null,
      ].filter(Boolean),
      actions: {
        forceSync: 'POST /api/sync-clerk-subscription',
        manualUpgrade: 'POST /api/debug/manual-pro-upgrade',
      },
      timestamp: new Date().toISOString(),
    });

  } catch (error: any) {
    console.error('❌ Error checking Pro upgrade:', error);
    return NextResponse.json({
      success: false,
      error: error.message,
      stack: error.stack
    }, { status: 500 });
  }
}

import { NextResponse } from 'next/server';
import Stripe from 'stripe';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2024-06-20',
});

export async function GET() {
  try {
    // Get all prices from Stripe
    const prices = await stripe.prices.list({
      active: true,
      expand: ['data.product'],
    });

    // Get current price mapping from our code
    const currentMapping = {
      'price_1RSIGpR6OeqomohOPQNu7awg': 'standard',
      'price_1RSIHaR6OeqomohOzOEwFOgZ': 'pro',
      'price_1RSIICR6OeqomohOLsmbhNj8': 'premium',
    };

    // Format prices for easy viewing
    const formattedPrices = prices.data.map(price => ({
      id: price.id,
      product_name: (price.product as Stripe.Product)?.name || 'Unknown',
      amount: price.unit_amount,
      currency: price.currency,
      interval: price.recurring?.interval || 'one_time',
      active: price.active,
      created: new Date(price.created * 1000).toISOString(),
    }));

    // Check which prices are mapped and which aren't
    const mappedPrices = formattedPrices.filter(price => 
      Object.keys(currentMapping).includes(price.id)
    );

    const unmappedPrices = formattedPrices.filter(price => 
      !Object.keys(currentMapping).includes(price.id)
    );

    // Generate suggested mapping code
    const suggestedMapping = formattedPrices.map(price => {
      const planName = price.product_name.toLowerCase().includes('standard') ? 'standard' :
                      price.product_name.toLowerCase().includes('pro') ? 'pro' :
                      price.product_name.toLowerCase().includes('premium') ? 'premium' :
                      'unknown';
      
      return `'${price.id}': '${planName}', // ${price.product_name} - $${(price.amount || 0) / 100}/${price.interval}`;
    }).join('\n    ');

    return NextResponse.json({
      success: true,
      stripe_data: {
        total_prices: formattedPrices.length,
        all_prices: formattedPrices,
        mapped_prices: mappedPrices,
        unmapped_prices: unmappedPrices,
        current_mapping: currentMapping,
        suggested_mapping_code: `const priceMapping: Record<string, string> = {\n    ${suggestedMapping}\n};`
      }
    });

  } catch (error: any) {
    console.error('Error fetching Stripe prices:', error);
    return NextResponse.json({
      success: false,
      error: error.message,
      suggestion: 'Make sure STRIPE_SECRET_KEY is set correctly in your environment variables'
    }, { status: 500 });
  }
}

import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { createServiceRoleSupabaseClient } from '@/utils/supabase/server';

export async function GET() {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    console.log(`🔍 Checking plan credits configuration`);

    const supabase = createServiceRoleSupabaseClient();

    // Get all subscription plans
    const { data: plans, error: plansError } = await supabase
      .from('subscription_plans')
      .select('*')
      .order('id');

    if (plansError) {
      return NextResponse.json({
        success: false,
        error: 'Failed to fetch subscription plans',
        details: plansError.message
      }, { status: 500 });
    }

    // Get current user data
    const { data: currentUser, error: userError } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single();

    if (userError) {
      return NextResponse.json({
        success: false,
        error: 'Failed to fetch user data',
        details: userError.message
      }, { status: 500 });
    }

    // Get user's credit transactions
    const { data: creditTransactions, error: creditError } = await supabase
      .from('credit_transactions')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (creditError) {
      console.error('❌ Error fetching credit transactions:', creditError);
    }

    // Expected credit amounts
    const expectedCredits = {
      free: 50,
      standard: 700,
      pro: 3500,
      premium: 14500,
    };

    // Analyze each plan
    const planAnalysis = plans?.map(plan => {
      const expected = expectedCredits[plan.id as keyof typeof expectedCredits] || 0;
      const actual = plan.credits_included || 0;
      const isCorrect = actual === expected;

      return {
        planId: plan.id,
        planName: plan.name,
        expectedCredits: expected,
        actualCredits: actual,
        isCorrect,
        needsUpdate: !isCorrect,
        difference: actual - expected,
        fullPlanData: plan,
      };
    }) || [];

    // Calculate user's total credits from transactions
    const activeCredits = creditTransactions?.filter(t => !t.is_expired) || [];
    const totalFromTransactions = activeCredits.reduce((sum, t) => sum + (t.amount || 0), 0);

    return NextResponse.json({
      success: true,
      analysis: {
        plansNeedingUpdate: planAnalysis.filter(p => !p.isCorrect),
        allPlansCorrect: planAnalysis.every(p => p.isCorrect),
        totalIssues: planAnalysis.filter(p => !p.isCorrect).length,
      },
      planDetails: planAnalysis,
      expectedCredits,
      currentUser: {
        id: userId,
        subscription_type: currentUser?.subscription_type,
        credits: currentUser?.credits,
        totalFromTransactions,
        creditsMismatch: currentUser?.credits !== totalFromTransactions,
      },
      recentTransactions: creditTransactions?.slice(0, 10) || [],
      recommendations: [
        ...planAnalysis.filter(p => !p.isCorrect).map(p => 
          `🔧 Update ${p.planName} plan: ${p.actualCredits} → ${p.expectedCredits} credits`
        ),
        currentUser?.credits !== totalFromTransactions ? 
          `🔄 Recalculate user credits: DB shows ${currentUser?.credits}, transactions total ${totalFromTransactions}` : null,
      ].filter(Boolean),
      fixActions: {
        updatePlanCredits: 'POST /api/debug/fix-plan-credits',
        recalculateUserCredits: 'POST /api/debug/recalculate-credits',
      },
      timestamp: new Date().toISOString(),
    });

  } catch (error: any) {
    console.error('❌ Error checking plan credits:', error);
    return NextResponse.json({
      success: false,
      error: error.message,
      stack: error.stack
    }, { status: 500 });
  }
}

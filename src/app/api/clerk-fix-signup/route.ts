import { NextResponse } from 'next/server';
import { clerkClient } from '@clerk/nextjs/server';

export async function GET() {
  try {
    // Get environment variables
    const envVars = {
      NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY: process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY?.substring(0, 10) + '...',
      CLERK_SECRET_KEY: process.env.CLERK_SECRET_KEY ? process.env.CLERK_SECRET_KEY.substring(0, 10) + '...' : 'missing',
      CLERK_ENCRYPTION_KEY: process.env.CLERK_ENCRYPTION_KEY ? 'present' : 'missing',
      NODE_ENV: process.env.NODE_ENV,
    };

    // Check if we're using test or live keys
    const isTestKey = process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY?.startsWith('pk_test_');
    
    // Get the current Clerk instance settings
    try {
      // Create a test user to check if signup works
      const testEmail = `test-${Date.now()}@example.com`;
      const testPassword = 'Password123!';
      
      const user = await clerkClient.users.createUser({
        emailAddress: [testEmail],
        password: testPassword,
        firstName: 'Test',
        lastName: 'User',
      });
      
      // If we get here, user creation works
      return NextResponse.json({
        success: true,
        message: 'Clerk signup is working correctly',
        userId: user.id,
        isTestKey,
        envVars,
      });
    } catch (createError: any) {
      // Check if this is a domain restriction error
      const isDomainError = createError.message?.includes('domain') || 
                           createError.errors?.some((e: any) => e.message?.includes('domain'));
      
      return NextResponse.json({
        success: false,
        message: 'Clerk signup is not working',
        error: createError.message || 'Unknown error',
        errorDetails: createError.errors || [],
        isTestKey,
        isDomainError,
        recommendation: isDomainError ? 
          'Your Clerk instance has domain restrictions. Use test keys for local development or add localhost to your allowed domains.' : 
          'Check your Clerk configuration and API keys.',
        envVars,
      }, { status: 500 });
    }
  } catch (error: any) {
    console.error('Clerk fix check failed:', error);

    return NextResponse.json({
      success: false,
      error: error.message || 'Unknown error',
      stack: error.stack,
    }, { status: 500 });
  }
}

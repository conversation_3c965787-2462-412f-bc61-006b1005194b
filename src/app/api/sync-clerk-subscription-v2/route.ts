import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { createServiceRoleSupabaseClient } from '@/utils/supabase/server';

export async function POST() {
  try {
    // Get the current user and session claims
    const { userId, sessionClaims } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    console.log(`🔄 Syncing Clerk subscription V2 for user ${userId}`);
    console.log(`📋 Session claims:`, JSON.stringify(sessionClaims, null, 2));

    const supabase = createServiceRoleSupabaseClient();

    // Extract plan from Clerk's session claims
    const clerkPlan = sessionClaims?.pla as string; // e.g., "u:standard", "u:free_user", "u:pro"
    
    if (!clerkPlan) {
      return NextResponse.json({
        success: false,
        error: 'No plan information found in session claims'
      }, { status: 400 });
    }

    console.log(`📋 Raw plan from session: ${clerkPlan}`);

    // Parse the plan (remove "u:" prefix and "_user" suffix)
    const planName = clerkPlan.replace('u:', '').replace('_user', '');
    
    // Map Clerk plan names to our plan IDs
    const planMapping: Record<string, string> = {
      'free': 'free',
      'standard': 'standard',
      'pro': 'pro',
      'premium': 'premium',
    };

    const planId = planMapping[planName] || 'free';
    
    console.log(`🔄 Mapping Clerk plan "${planName}" to our plan "${planId}"`);

    // Get current user data from our database
    const { data: currentUser, error: currentUserError } = await supabase
      .from('users')
      .select('subscription_type, credits, last_payment_date')
      .eq('id', userId)
      .single();

    if (currentUserError) {
      console.error('❌ Error fetching current user:', currentUserError);
      return NextResponse.json({
        success: false,
        error: 'Failed to fetch current user data'
      }, { status: 500 });
    }

    // Check if user already has this plan
    const currentPlanId = currentUser?.subscription_type?.toLowerCase() || 'free';
    const targetPlanName = planId === 'free' ? 'Free' : planId.charAt(0).toUpperCase() + planId.slice(1);

    console.log(`📋 Current plan: ${currentPlanId}, Target plan: ${planId}`);

    // Check if user already has credits for this plan
    const { data: existingPlanCredits } = await supabase
      .from('credit_transactions')
      .select('id')
      .eq('user_id', userId)
      .eq('plan_id', planId)
      .eq('transaction_type', 'subscription')
      .limit(1);

    const hasExistingPlanCredits = existingPlanCredits && existingPlanCredits.length > 0;

    if (currentPlanId === planId && hasExistingPlanCredits) {
      console.log(`✅ User already has ${planId} plan with credits, no sync needed`);
      return NextResponse.json({
        success: true,
        message: `User already has ${targetPlanName} plan`,
        currentPlan: targetPlanName,
        noChangeNeeded: true,
      });
    }

    console.log(`🔄 Upgrading user from ${currentPlanId} to ${planId}`);

    // Get plan details from our database
    const { data: plan, error: planError } = await supabase
      .from('subscription_plans')
      .select('*')
      .eq('id', planId)
      .single();

    if (planError || !plan) {
      console.error('❌ Plan not found:', planId);
      return NextResponse.json({
        success: false,
        error: `Plan not found: ${planId}`
      }, { status: 400 });
    }

    // Update user subscription details
    const { error: userUpdateError } = await supabase
      .from('users')
      .update({
        subscription_type: plan.name,
        subscription_status: 'active',
        last_payment_date: planId !== 'free' ? new Date().toISOString() : currentUser.last_payment_date,
        updated_at: new Date().toISOString(),
      })
      .eq('id', userId);

    if (userUpdateError) {
      console.error('❌ Error updating user subscription:', userUpdateError);
      return NextResponse.json({
        success: false,
        error: 'Failed to update user subscription'
      }, { status: 500 });
    }

    // Add credits if it's a paid plan and user doesn't already have them
    let creditsAdded = 0;
    if (plan.credits_included > 0 && planId !== 'free' && !hasExistingPlanCredits) {
      const expiresAt = plan.credits_expire_days > 0 
        ? new Date(Date.now() + plan.credits_expire_days * 24 * 60 * 60 * 1000).toISOString()
        : null;

      // Insert credit transaction
      const { error: creditError } = await supabase
        .from('credit_transactions')
        .insert({
          user_id: userId,
          amount: plan.credits_included,
          description: `Clerk subscription sync V2: ${plan.name} plan`,
          transaction_type: 'subscription',
          expires_at: expiresAt,
          plan_id: planId,
        });

      if (creditError) {
        console.error('❌ Error adding credits:', creditError);
      } else {
        creditsAdded = plan.credits_included;
        console.log(`✅ Added ${creditsAdded} credits for ${planId} plan`);
      }
    }

    // Recalculate total credits
    const { data: allActiveCredits, error: totalError } = await supabase
      .from('credit_transactions')
      .select('amount')
      .eq('user_id', userId)
      .eq('is_expired', false);

    if (!totalError && allActiveCredits) {
      let totalCredits = 0;
      for (const creditTransaction of allActiveCredits) {
        totalCredits += creditTransaction.amount || 0;
      }
      
      await supabase
        .from('users')
        .update({ 
          credits: totalCredits,
          credits_expire_at: plan.credits_expire_days > 0 ? 
            new Date(Date.now() + plan.credits_expire_days * 24 * 60 * 60 * 1000).toISOString() : null,
        })
        .eq('id', userId);

      console.log(`✅ Updated total credits to ${totalCredits}`);
    }

    // Record subscription history
    await supabase
      .from('subscription_history')
      .insert({
        user_id: userId,
        plan_id: planId,
        action: 'clerk_sync_v2',
        effective_date: new Date().toISOString(),
        metadata: {
          clerk_plan: clerkPlan,
          sync_method: 'session_claims_v2',
          credits_added: creditsAdded,
        },
      });

    // Get updated user data
    const { data: updatedUser, error: userError } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single();

    console.log(`✅ Clerk subscription synced successfully V2 for user ${userId}: ${planId} plan`);

    return NextResponse.json({
      success: true,
      message: `Successfully synced to ${plan.name} plan`,
      results: {
        clerkPlan: clerkPlan,
        mappedPlan: planId,
        planName: plan.name,
        creditsAdded: creditsAdded,
        totalCredits: updatedUser?.credits || 0,
        user: updatedUser,
      },
      timestamp: new Date().toISOString(),
    });

  } catch (error: any) {
    console.error('❌ Error syncing Clerk subscription V2:', error);
    return NextResponse.json({
      success: false,
      error: error.message,
      stack: error.stack
    }, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Clerk subscription sync V2 endpoint',
    description: 'Simplified sync without variable conflicts',
    usage: 'POST to sync current user subscription',
    improvements: [
      'No reduce() functions that could cause variable conflicts',
      'Simple for loop for credit calculation',
      'Better error handling',
      'More detailed logging',
    ],
  });
}

import { NextResponse } from 'next/server';
import { stripe } from '@/utils/stripe/server';

export async function GET() {
  try {
    // List all products with their prices
    const products = await stripe.products.list({
      active: true,
      expand: ['data.default_price'],
    });

    // Get all prices for each product
    const productsWithPrices = await Promise.all(
      products.data.map(async (product) => {
        const prices = await stripe.prices.list({
          product: product.id,
          active: true,
        });

        return {
          productId: product.id,
          productName: product.name,
          prices: prices.data.map(price => ({
            priceId: price.id,
            amount: price.unit_amount ? price.unit_amount / 100 : 0,
            currency: price.currency,
            interval: price.recurring?.interval || 'one_time',
            intervalCount: price.recurring?.interval_count || 1,
          })),
        };
      })
    );

    return NextResponse.json({
      products: productsWithPrices,
      envFormat: generateEnvFormat(productsWithPrices),
    });
  } catch (error) {
    console.error('Error listing prices:', error);
    return NextResponse.json(
      { error: 'Failed to list prices' },
      { status: 500 }
    );
  }
}

function generateEnvFormat(products: any[]) {
  let envString = '\n# Stripe Price IDs\n';
  
  products.forEach(product => {
    const name = product.productName.toUpperCase().replace(/\s+/g, '_');
    
    product.prices.forEach((price: any) => {
      const interval = price.interval === 'month' ? 'MONTHLY' : 'YEARLY';
      envString += `STRIPE_PRICE_ID_${name}_${interval}=${price.priceId}\n`;
    });
  });
  
  return envString;
}

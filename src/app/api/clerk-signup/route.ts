import { NextRequest, NextResponse } from 'next/server';
import { clerkClient } from '@clerk/nextjs/server';

export async function POST(req: NextRequest) {
  try {
    const { email, password, firstName, lastName } = await req.json();

    // Log the attempt (without sensitive data)
    console.log('Attempting to create user with email:', email);

    // Create the user
    const user = await clerkClient.users.createUser({
      emailAddress: [email],
      password,
      firstName,
      lastName,
    });

    return NextResponse.json({
      success: true,
      message: 'User created successfully',
      userId: user.id,
    });
  } catch (error: any) {
    console.error('Error creating user:', error);

    // Check for already verified or existing email errors
    const errorMessage = error.message || '';
    const errorCode = error.errors?.[0]?.code || '';
    const isEmailAlreadyUsed =
      errorMessage.includes('already exists') ||
      errorMessage.includes('verified') ||
      errorCode === 'form_identifier_exists';

    // Set appropriate status code and message
    const statusCode = isEmailAlreadyUsed ? 409 : 500; // 409 Conflict for already used email
    const userMessage = isEmailAlreadyUsed
      ? 'This email address is already registered. Please try signing in instead.'
      : error.message || 'Unknown error';

    return NextResponse.json({
      success: false,
      error: userMessage,
      details: error.errors || [],
      code: errorCode,
    }, { status: statusCode });
  }
}

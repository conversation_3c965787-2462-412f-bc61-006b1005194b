/* Add these animation styles to globals.css */
.scroll-animate {
  opacity: 0;
  transform: translateY(12px);
  transition: opacity 2.2s cubic-bezier(0.19, 1, 0.22, 1),
              transform 2.2s cubic-bezier(0.19, 1, 0.22, 1);
  will-change: opacity, transform;
}

.animate-in {
  opacity: 1;
  transform: translateY(0);
}

.delay-100 { transition-delay: 0.2s; }
.delay-200 { transition-delay: 0.4s; }
.delay-300 { transition-delay: 0.6s; }
.delay-400 { transition-delay: 0.8s; }
.delay-500 { transition-delay: 1s; }
.delay-600 { transition-delay: 1.2s; }
.delay-700 { transition-delay: 1.4s; }
.delay-800 { transition-delay: 1.6s; }

.vision-text {
  background: linear-gradient(90deg, #10b981, #3b82f6, #10b981);
  background-size: 200% auto;
  color: transparent;
  -webkit-background-clip: text;
  background-clip: text;
  animation: gradient 3s linear infinite;
}

@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes shimmer {
  0% {
    background-position: 0% 50%;
  }
  100% {
    background-position: 100% 50%;
  }
}

@keyframes shine {
  0% {
    transform: translateX(-100%);
  }
  60%, 100% {
    transform: translateX(100%);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

@keyframes float-button {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5px);
  }
}

.upgrade-button {
  transform-style: preserve-3d;
  perspective: 1000px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.upgrade-button:hover {
  transform: scale(1.05) translateY(-4px) rotateX(5deg);
  box-shadow: 0 15px 25px -5px rgba(124, 58, 237, 0.5);
}

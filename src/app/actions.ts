'use server';

import { createServerActionSupabaseClient, createServiceRoleSupabaseClient } from '@/utils/supabase/server';

/**
 * Server action to create a user in Supabase when they sign up with Clerk
 */
export async function createUserInSupabase(
  userId: string,
  email: string,
  username: string,
  firstName: string,
  lastName: string,
  imageUrl: string,
  subscriptionType: string = 'Free' // Add default parameter
) {
  try {
    console.log('Server action: Creating user in Supabase with ID:', userId);

    // Create Supabase client with service role for admin operations
    const supabase = createServiceRoleSupabaseClient();

    // Check if user already exists in Supabase
    const { data: existingUser, error: checkError } = await supabase
      .from('users')
      .select('id')
      .eq('id', userId)
      .maybeSingle();

    if (checkError) {
      console.error('Error checking for existing user:', checkError);
      return { success: false, error: `Error checking for existing user: ${checkError.message}` };
    }

    if (existingUser) {
      console.log('User already exists in Supabase:', existingUser);
      return { success: true, message: 'User already exists in Supabase' };
    }

    // Insert user into Supabase
    const { error: insertError } = await supabase
      .from('users')
      .insert({
        id: userId,
        email,
        username,
        first_name: firstName,
        last_name: lastName,
        avatar_url: imageUrl,
        credits: 50,
        subscription_type: subscriptionType, // Use the parameter
      });

    if (insertError) {
      console.error('Error inserting user into Supabase:', insertError);
      return { success: false, error: `Error inserting user: ${insertError.message}` };
    }

    console.log('User created successfully in Supabase');
    return { success: true, message: 'User created successfully in Supabase' };
  } catch (error: any) {
    console.error('Unexpected error in createUserInSupabase:', error);
    return { success: false, error: `Unexpected error: ${error.message}` };
  }
}

/**
 * Server action to get user data from Supabase
 */
export async function getUserFromSupabase(userId: string) {
  try {
    const supabase = await createServerActionSupabaseClient();

    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .maybeSingle();

    if (error) {
      return { success: false, error: error.message };
    }

    return { success: true, data };
  } catch (error: any) {
    return { success: false, error: error.message };
  }
}

/**
 * Test action to verify server actions are working
 */
export async function testAction() {
  return { success: true, message: 'Server action is working' };
}

'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';

export default function ServerSignUp() {
  const router = useRouter();
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState<string | null>(null);

  // Check if email already exists
  const checkEmailExists = async (email: string): Promise<boolean> => {
    try {
      const response = await fetch('/api/check-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      if (!response.ok) {
        // If the API call fails, we'll proceed with normal signup
        console.warn('Failed to check email existence, proceeding with normal signup');
        return false;
      }

      const data = await response.json();
      return data.exists === true;
    } catch (err) {
      console.error('Error checking email existence:', err);
      // If there's an error, we'll proceed with normal signup
      return false;
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      setLoading(true);
      setError('');
      setSuccess(null);

      // Validate password
      if (password.length < 8) {
        setError('Password must be at least 8 characters long');
        return;
      }

      // First check if the email already exists
      const emailExists = await checkEmailExists(email);
      if (emailExists) {
        setError('This email address is already registered. Please try signing in instead.');
        setLoading(false);
        return;
      }

      // Log attempt
      console.log('Attempting server-side sign-up with:', { email, firstName, lastName });

      // Call the server-side API
      const response = await fetch('/api/direct-signup', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          firstName,
          lastName,
          email,
          password,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to sign up');
      }

      // Handle success
      console.log('Sign-up successful:', data);
      setSuccess(data.message || 'Sign-up successful! Please check your email for verification instructions.');

      // Clear form
      setFirstName('');
      setLastName('');
      setEmail('');
      setPassword('');

    } catch (err: any) {
      console.error('Sign-up error:', err);
      setError(err.message || 'An unknown error occurred');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-900 p-4">
      <div className="w-full max-w-md bg-gray-800 p-8 rounded-lg shadow-lg">
        <h1 className="text-2xl font-bold text-white mb-2">Server-Side Sign Up</h1>
        <p className="text-gray-400 mb-6">This page uses a server-side API to create your account</p>

        {error && (
          <div className="mb-4 p-3 bg-red-500/20 border border-red-500 rounded text-red-300">
            {error}
          </div>
        )}

        {success && (
          <div className="mb-4 p-3 bg-green-500/20 border border-green-500 rounded text-green-300">
            {success}
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-gray-300 mb-1">First Name</label>
              <input
                type="text"
                value={firstName}
                onChange={(e) => setFirstName(e.target.value)}
                className="w-full p-2 bg-gray-700 border border-gray-600 rounded text-white"
                required
              />
            </div>

            <div>
              <label className="block text-gray-300 mb-1">Last Name</label>
              <input
                type="text"
                value={lastName}
                onChange={(e) => setLastName(e.target.value)}
                className="w-full p-2 bg-gray-700 border border-gray-600 rounded text-white"
                required
              />
            </div>
          </div>

          <div>
            <label className="block text-gray-300 mb-1">Email</label>
            <input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="w-full p-2 bg-gray-700 border border-gray-600 rounded text-white"
              required
            />
          </div>

          <div>
            <label className="block text-gray-300 mb-1">Password</label>
            <input
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="w-full p-2 bg-gray-700 border border-gray-600 rounded text-white"
              required
            />
            <p className="text-xs text-gray-400 mt-1">Must be at least 8 characters</p>
          </div>

          <button
            type="submit"
            disabled={loading}
            className="w-full py-2 bg-blue-600 hover:bg-blue-700 text-white rounded font-medium"
          >
            {loading ? 'Signing up...' : 'Sign Up'}
          </button>

          <div className="text-center">
            <button
              type="button"
              onClick={() => router.push('/sign-in')}
              className="text-blue-400 hover:text-blue-300 text-sm"
            >
              Already have an account? Sign in
            </button>
          </div>
        </form>

        <div className="mt-6 text-center text-sm text-gray-400">
          <p>This page uses a server-side API to create your account, bypassing client-side Clerk issues.</p>
        </div>
      </div>
    </div>
  );
}

'use client';

import { useState } from 'react';

export default function TestInvitation() {
  const [email, setEmail] = useState('');
  const [result, setResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const sendInvitation = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    setResult(null);

    try {
      if (!email || !email.includes('@')) {
        throw new Error('Please enter a valid email address');
      }

      // Call our API endpoint
      const response = await fetch('/api/test-invitation', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email: email.trim() }),
      });
      
      let data;
      try {
        data = await response.json();
      } catch (jsonError) {
        console.error('Error parsing JSON response:', jsonError);
        throw new Error('Invalid response from server. Please try again.');
      }

      if (!response.ok || !data.success) {
        throw new Error(data.error || 'Failed to send invitation');
      }

      setResult(data);
    } catch (err: any) {
      console.error('Error sending invitation:', err);
      setError(err.message || 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <h1 className="text-3xl font-bold mb-6">Test Clerk Invitation</h1>
      
      <div className="bg-white p-6 rounded-lg shadow-md mb-8">
        <h2 className="text-xl font-semibold mb-4">Send Test Invitation</h2>
        <p className="mb-4 text-gray-600">This will create and send an invitation email using Clerk's API.</p>
        
        <form onSubmit={sendInvitation} className="space-y-4">
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
              Email Address
            </label>
            <input
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="block w-full rounded-md border border-gray-300 px-4 py-2 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
              placeholder="<EMAIL>"
              required
            />
          </div>
          
          <button
            type="submit"
            disabled={loading}
            className="rounded-md bg-blue-600 px-4 py-2 text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          >
            {loading ? 'Sending...' : 'Send Invitation'}
          </button>
        </form>
        
        {error && (
          <div className="mt-4 bg-red-50 border-l-4 border-red-400 p-3">
            <p className="text-sm text-red-700">{error}</p>
          </div>
        )}
      </div>
      
      {result && (
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-xl font-semibold mb-4">Invitation Result</h2>
          
          <div className="bg-green-50 border-l-4 border-green-400 p-4 mb-4">
            <p className="text-sm text-green-700">{result.message}</p>
          </div>
          
          <div>
            <h3 className="text-lg font-medium mb-2">Invitation Details:</h3>
            <pre className="bg-gray-50 p-4 rounded overflow-auto max-h-96">
              {JSON.stringify(result.invitation, null, 2)}
            </pre>
          </div>
          
          <div className="mt-6 bg-gray-50 p-4 rounded-lg border border-gray-200">
            <h3 className="text-lg font-medium mb-2">Troubleshooting Tips:</h3>
            <ul className="list-disc pl-5 space-y-1">
              <li>Check the recipient's spam/junk folder</li>
              <li>Try with a different email address (Gmail, Outlook, etc.)</li>
              <li>Check your Clerk Dashboard for any email configuration issues</li>
              <li>Verify your domain's DNS settings if using a custom domain</li>
              <li>Check the Clerk logs in the dashboard for any error messages</li>
            </ul>
          </div>
        </div>
      )}
    </div>
  );
}

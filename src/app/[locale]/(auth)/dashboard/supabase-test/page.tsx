import { getTranslations } from 'next-intl/server';

import { SupabaseTestClient } from './supabase-test-client';

export async function generateMetadata(props: { params: { locale: string } }) {
  const t = await getTranslations({
    locale: props.params.locale,
    namespace: 'Dashboard',
  });

  return {
    title: `${t('meta_title')} - Supabase Test`,
    description: t('meta_description'),
  };
}

export default function SupabaseTestPage() {
  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="mb-6 text-3xl font-bold text-white">Supabase Integration Test</h1>
      <p className="mb-8 text-gray-300">
        This page tests the integration between Clerk and Supabase. It allows you to create and view tasks
        to verify that the Row Level Security (RLS) policies are working correctly.
      </p>
      
      <SupabaseTestClient />
    </div>
  );
}

export const dynamic = 'force-dynamic';

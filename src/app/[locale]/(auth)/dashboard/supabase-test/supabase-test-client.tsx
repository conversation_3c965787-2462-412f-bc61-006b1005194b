'use client';

import { useState, useEffect } from 'react';
import { useUser } from '@clerk/nextjs';
import { useSupabaseClient } from '@/utils/supabase/client';
import { getUserFromSupabase } from '@/app/actions';

interface Task {
  id: number;
  name: string;
  user_id: string;
  created_at: string;
}

interface UserData {
  id: string;
  email: string;
  username: string | null;
  first_name: string | null;
  last_name: string | null;
  avatar_url: string | null;
  credits: number;
  created_at: string;
  updated_at: string;
}

export function SupabaseTestClient() {
  const { user, isLoaded } = useUser();
  const supabase = useSupabaseClient();
  const [tasks, setTasks] = useState<Task[]>([]);
  const [newTaskName, setNewTaskName] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [userData, setUserData] = useState<UserData | null>(null);

  // Load tasks when the component mounts
  useEffect(() => {
    if (!isLoaded || !user) return;

    async function loadTasks() {
      setLoading(true);
      setError(null);

      try {
        // Fetch tasks from Supabase
        const { data, error } = await supabase
          .from('tasks')
          .select('*')
          .order('created_at', { ascending: false });

        if (error) {
          console.error('Error fetching tasks:', error);
          setError(`Error fetching tasks: ${error.message}`);
          setLoading(false);
          return;
        }

        setTasks(data || []);
        
        // Fetch user data from Supabase using server action
        const userResult = await getUserFromSupabase(user.id);
        if (userResult.success && userResult.data) {
          setUserData(userResult.data as UserData);
        }
      } catch (err: any) {
        console.error('Unexpected error:', err);
        setError(`Unexpected error: ${err.message}`);
      } finally {
        setLoading(false);
      }
    }

    loadTasks();
  }, [isLoaded, user, supabase]);

  // Create a new task
  const createTask = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newTaskName.trim()) return;

    setLoading(true);
    setError(null);

    try {
      // Insert task into Supabase
      const { error } = await supabase
        .from('tasks')
        .insert({ name: newTaskName });

      if (error) {
        console.error('Error creating task:', error);
        setError(`Error creating task: ${error.message}`);
        setLoading(false);
        return;
      }

      // Refresh tasks
      const { data, error: fetchError } = await supabase
        .from('tasks')
        .select('*')
        .order('created_at', { ascending: false });

      if (fetchError) {
        console.error('Error fetching tasks after creation:', fetchError);
        setError(`Error refreshing tasks: ${fetchError.message}`);
        setLoading(false);
        return;
      }

      setTasks(data || []);
      setNewTaskName('');
    } catch (err: any) {
      console.error('Unexpected error:', err);
      setError(`Unexpected error: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  if (!isLoaded) {
    return <div className="text-white">Loading user data...</div>;
  }

  if (!user) {
    return <div className="text-white">Please sign in to use this feature.</div>;
  }

  return (
    <div className="space-y-8">
      {/* User Information */}
      <div className="rounded-lg bg-navy-light p-6">
        <h2 className="mb-4 text-xl font-semibold text-white">User Information</h2>
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <div>
            <p className="text-gray-400">Clerk User ID:</p>
            <p className="text-white">{user.id}</p>
          </div>
          <div>
            <p className="text-gray-400">Email:</p>
            <p className="text-white">{user.primaryEmailAddress?.emailAddress}</p>
          </div>
          <div>
            <p className="text-gray-400">Username:</p>
            <p className="text-white">{user.username || 'Not set'}</p>
          </div>
          <div>
            <p className="text-gray-400">Name:</p>
            <p className="text-white">{user.firstName} {user.lastName}</p>
          </div>
        </div>
      </div>

      {/* Supabase User Data */}
      <div className="rounded-lg bg-navy-light p-6">
        <h2 className="mb-4 text-xl font-semibold text-white">Supabase User Data</h2>
        {userData ? (
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <div>
              <p className="text-gray-400">Supabase User ID:</p>
              <p className="text-white">{userData.id}</p>
            </div>
            <div>
              <p className="text-gray-400">Email:</p>
              <p className="text-white">{userData.email}</p>
            </div>
            <div>
              <p className="text-gray-400">Username:</p>
              <p className="text-white">{userData.username || 'Not set'}</p>
            </div>
            <div>
              <p className="text-gray-400">Name:</p>
              <p className="text-white">{userData.first_name} {userData.last_name}</p>
            </div>
            <div>
              <p className="text-gray-400">Credits:</p>
              <p className="text-white">{userData.credits}</p>
            </div>
            <div>
              <p className="text-gray-400">Created At:</p>
              <p className="text-white">{new Date(userData.created_at).toLocaleString()}</p>
            </div>
          </div>
        ) : (
          <p className="text-white">No Supabase user data found. This could mean the user hasn't been synced to Supabase yet.</p>
        )}
      </div>

      {/* Create Task Form */}
      <div className="rounded-lg bg-navy-light p-6">
        <h2 className="mb-4 text-xl font-semibold text-white">Create a Task</h2>
        <form onSubmit={createTask} className="flex flex-col space-y-4">
          <div>
            <label htmlFor="taskName" className="mb-2 block text-sm font-medium text-gray-300">
              Task Name
            </label>
            <input
              type="text"
              id="taskName"
              value={newTaskName}
              onChange={(e) => setNewTaskName(e.target.value)}
              className="w-full rounded-lg border border-gray-600 bg-navy-dark p-2.5 text-white placeholder-gray-400 focus:border-blue-500 focus:ring-blue-500"
              placeholder="Enter task name"
              required
            />
          </div>
          <button
            type="submit"
            disabled={loading}
            className="rounded-lg bg-green-600 px-5 py-2.5 text-center text-sm font-medium text-white hover:bg-green-700 focus:outline-none focus:ring-4 focus:ring-green-800 disabled:opacity-50"
          >
            {loading ? 'Creating...' : 'Create Task'}
          </button>
        </form>
      </div>

      {/* Error Message */}
      {error && (
        <div className="rounded-lg bg-red-900 p-4 text-white">
          <p className="font-bold">Error:</p>
          <p>{error}</p>
        </div>
      )}

      {/* Tasks List */}
      <div className="rounded-lg bg-navy-light p-6">
        <h2 className="mb-4 text-xl font-semibold text-white">Your Tasks</h2>
        {loading ? (
          <p className="text-gray-300">Loading tasks...</p>
        ) : tasks.length > 0 ? (
          <ul className="space-y-2">
            {tasks.map((task) => (
              <li key={task.id} className="rounded-lg bg-navy-dark p-4">
                <p className="text-white">{task.name}</p>
                <p className="text-xs text-gray-400">Created: {new Date(task.created_at).toLocaleString()}</p>
              </li>
            ))}
          </ul>
        ) : (
          <p className="text-gray-300">No tasks found. Create your first task above!</p>
        )}
      </div>
    </div>
  );
}

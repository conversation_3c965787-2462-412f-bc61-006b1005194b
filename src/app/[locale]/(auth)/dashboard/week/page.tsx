"use client";

import { useState, useEffect } from 'react';
import { useUser } from '@clerk/nextjs';
import { useSupabaseClient } from '@/utils/supabase/client';
import { Clock, ChevronDown } from 'lucide-react';
import Image from 'next/image';

interface MediaItem {
  id: number;
  user_id: string;
  original_url: string;
  processed_url: string | null;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  credits_used: number;
  created_at: string;
  metadata: any;
  duration_seconds?: number;
}

export default function WeekPage() {
  const { user } = useUser();
  const supabase = useSupabaseClient();
  const [mediaItems, setMediaItems] = useState<MediaItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [sortOrder, setSortOrder] = useState<'latest' | 'oldest'>('latest');

  // Fetch media from the last week
  useEffect(() => {
    if (!user) return;
    
    async function fetchWeekMedia() {
      setLoading(true);
      
      try {
        // Calculate timestamp for 7 days ago
        const oneWeekAgo = new Date();
        oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
        const timestamp = oneWeekAgo.toISOString();
        
        // Fetch recent images
        const { data: images, error: imagesError } = await supabase
          .from('processed_images')
          .select('*')
          .eq('user_id', user.id)
          .gte('created_at', timestamp)
          .order('created_at', { ascending: sortOrder === 'oldest' });
        
        // Fetch recent videos
        const { data: videos, error: videosError } = await supabase
          .from('processed_videos')
          .select('*')
          .eq('user_id', user.id)
          .gte('created_at', timestamp)
          .order('created_at', { ascending: sortOrder === 'oldest' });
        
        // Combine media
        let combinedMedia: MediaItem[] = [
          ...(images || []),
          ...(videos || [])
        ];
        
        // Sort by created_at
        combinedMedia.sort((a, b) => {
          const dateA = new Date(a.created_at).getTime();
          const dateB = new Date(b.created_at).getTime();
          return sortOrder === 'latest' ? dateB - dateA : dateA - dateB;
        });
        
        setMediaItems(combinedMedia);
      } catch (err) {
        console.error('Error fetching week media:', err);
      } finally {
        setLoading(false);
      }
    }
    
    fetchWeekMedia();
  }, [user, sortOrder, supabase]);

  // Format time elapsed
  const formatTimeElapsed = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    
    if (diffDays > 0) {
      return `${diffDays} day${diffDays !== 1 ? 's' : ''} ago`;
    } else {
      const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
      if (diffHours > 0) {
        return `${diffHours} hour${diffHours !== 1 ? 's' : ''} ago`;
      } else {
        const diffMinutes = Math.floor(diffMs / (1000 * 60));
        return `${diffMinutes} minute${diffMinutes !== 1 ? 's' : ''} ago`;
      }
    }
  };

  // Format video duration
  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  if (!user) {
    return null;
  }

  return (
    <div className="flex h-full flex-col">
      <h1 className="mb-4 text-xl font-bold text-white">A Week Ago</h1>
      
      {/* Sort dropdown */}
      <div className="mb-4 flex items-center justify-end">
        <button className="flex items-center space-x-1 text-sm text-white">
          <span>{sortOrder === 'latest' ? 'Latest' : 'Oldest'}</span>
          <ChevronDown className="h-4 w-4" />
        </button>
      </div>
      
      {/* Media grid */}
      {loading ? (
        <div className="flex h-40 items-center justify-center">
          <div className="text-center text-gray-400">Loading...</div>
        </div>
      ) : mediaItems.length === 0 ? (
        <div className="flex h-40 items-center justify-center">
          <div className="text-center text-gray-400">No media from the last week</div>
        </div>
      ) : (
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
          {mediaItems.map((item) => {
            const isVideo = 'duration_seconds' in item;
            const fileName = item.metadata?.originalName || 'Unnamed file';
            
            return (
              <div key={`${isVideo ? 'video' : 'image'}-${item.id}`} className="overflow-hidden rounded-lg">
                {/* Thumbnail */}
                <div className="relative aspect-video bg-gray-800">
                  {item.processed_url ? (
                    <Image
                      src={item.processed_url}
                      alt={fileName}
                      fill
                      className="object-cover"
                    />
                  ) : (
                    <div className="absolute inset-0 flex items-center justify-center">
                      <Clock className="h-8 w-8 text-gray-500" />
                    </div>
                  )}
                  
                  {/* Video duration badge */}
                  {isVideo && item.duration_seconds && (
                    <div className="absolute bottom-2 right-2 rounded bg-black/70 px-1.5 py-0.5 text-xs text-white">
                      {formatDuration(item.duration_seconds)}
                    </div>
                  )}
                  
                  {/* Status badge */}
                  <div className={`absolute top-2 left-2 rounded px-1.5 py-0.5 text-xs text-white ${
                    item.status === 'completed' 
                      ? 'bg-green-500/90' 
                      : item.status === 'failed'
                        ? 'bg-red-500/90'
                        : 'bg-yellow-500/90'
                  }`}>
                    {item.status.charAt(0).toUpperCase() + item.status.slice(1)}
                  </div>
                </div>
                
                {/* File info */}
                <div className="mt-2">
                  <p className="truncate text-sm font-medium text-white">{fileName}</p>
                  <p className="text-xs text-gray-400">{formatTimeElapsed(item.created_at)}</p>
                </div>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
}

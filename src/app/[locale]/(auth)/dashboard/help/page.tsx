"use client";

import { Mail, MessageSquare, FileText, ExternalLink } from 'lucide-react';
import Link from 'next/link';

export default function HelpPage() {
  return (
    <div className="mx-auto max-w-3xl">
      <h1 className="mb-6 text-2xl font-bold text-white">Help Center</h1>
      
      <div className="mb-8 rounded-lg border border-navy-light bg-navy p-6">
        <h2 className="mb-4 text-xl font-semibold text-white">Frequently Asked Questions</h2>
        
        <div className="space-y-4">
          <div className="rounded-md bg-navy-light p-4">
            <h3 className="mb-2 text-lg font-medium text-white">How does Guardiavision work?</h3>
            <p className="text-gray-300">
              Guardiavision uses advanced AI technology to automatically detect and blur faces, license plates, and other sensitive information in your photos and videos, helping you protect privacy.
            </p>
          </div>
          
          <div className="rounded-md bg-navy-light p-4">
            <h3 className="mb-2 text-lg font-medium text-white">What file formats are supported?</h3>
            <p className="text-gray-300">
              We support most common image formats (JPG, PNG, GIF) and video formats (MP4, MOV). Files must be under 50MB for images and 500MB for videos.
            </p>
          </div>
          
          <div className="rounded-md bg-navy-light p-4">
            <h3 className="mb-2 text-lg font-medium text-white">How are credits calculated?</h3>
            <p className="text-gray-300">
              Each image processed costs 1 credit. Videos cost 1 credit per 6 seconds of footage. Free accounts start with 50 credits, and you can purchase more through our subscription plans.
            </p>
          </div>
          
          <div className="rounded-md bg-navy-light p-4">
            <h3 className="mb-2 text-lg font-medium text-white">Is my data secure?</h3>
            <p className="text-gray-300">
              Yes, we take data security seriously. Your files are processed securely and are not shared with third parties. We automatically delete original files after 30 days unless you choose to keep them.
            </p>
          </div>
        </div>
      </div>
      
      <div className="grid gap-6 md:grid-cols-3">
        <div className="rounded-lg border border-navy-light bg-navy p-6 text-center">
          <Mail className="mx-auto mb-4 h-10 w-10 text-orange-500" />
          <h3 className="mb-2 text-lg font-medium text-white">Email Support</h3>
          <p className="mb-4 text-sm text-gray-300">
            Get help from our support team via email
          </p>
          <Link 
            href="mailto:<EMAIL>" 
            className="inline-flex items-center text-sm font-medium text-orange-500 hover:text-orange-400"
          >
            Contact Support
            <ExternalLink className="ml-1 h-4 w-4" />
          </Link>
        </div>
        
        <div className="rounded-lg border border-navy-light bg-navy p-6 text-center">
          <MessageSquare className="mx-auto mb-4 h-10 w-10 text-orange-500" />
          <h3 className="mb-2 text-lg font-medium text-white">Live Chat</h3>
          <p className="mb-4 text-sm text-gray-300">
            Chat with our support team in real-time
          </p>
          <button 
            className="inline-flex items-center text-sm font-medium text-orange-500 hover:text-orange-400"
          >
            Start Chat
            <ExternalLink className="ml-1 h-4 w-4" />
          </button>
        </div>
        
        <div className="rounded-lg border border-navy-light bg-navy p-6 text-center">
          <FileText className="mx-auto mb-4 h-10 w-10 text-orange-500" />
          <h3 className="mb-2 text-lg font-medium text-white">Documentation</h3>
          <p className="mb-4 text-sm text-gray-300">
            Browse our detailed documentation
          </p>
          <Link 
            href="/docs" 
            className="inline-flex items-center text-sm font-medium text-orange-500 hover:text-orange-400"
          >
            View Docs
            <ExternalLink className="ml-1 h-4 w-4" />
          </Link>
        </div>
      </div>
    </div>
  );
}

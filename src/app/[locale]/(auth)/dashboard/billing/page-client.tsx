"use client";

// Commented out unused imports
// import { useTranslations } from 'next-intl';
import { Check } from 'lucide-react';
// import Link from 'next/link';
// import { useParams } from 'next/navigation';

import { buttonVariants } from '@/components/ui/buttonVariants';

export function BillingPageClient() {
  // Commented out unused variables
  // const t = useTranslations('Billing');
  // const params = useParams();
  // const locale = params.locale as string || 'en';

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="rounded-lg border border-navy-light bg-navy p-6">
        <h1 className="text-2xl font-bold text-white">Subscription Plans</h1>
        <p className="mt-2 text-gray-300">
        </p>
      </div>

      {/* Plans */}
      <div className="grid gap-8 md:grid-cols-3">
        {/* Free Plan */}
        <div className="rounded-lg border border-navy-light bg-navy p-8 shadow-lg">
          <h3 className="text-xl font-bold text-white">Free</h3>
          <p className="mt-4 text-gray-300">Perfect for testing our features</p>
          <div className="mt-6 flex items-baseline">
            <span className="text-5xl font-extrabold text-white">$0</span>
            <span className="ml-1 text-xl font-medium text-gray-300">/month</span>
          </div>
          <ul className="mt-8 space-y-4">
            <li className="flex items-center">
              <div className="flex-shrink-0 rounded-full bg-green-400 p-1">
                <Check className="h-4 w-4 text-navy" />
              </div>
              <span className="ml-3 text-gray-300">Process up to 50 images</span>
            </li>
            <li className="flex items-center">
              <div className="flex-shrink-0 rounded-full bg-green-400 p-1">
                <Check className="h-4 w-4 text-navy" />
              </div>
              <span className="ml-3 text-gray-300">Basic blur options</span>
            </li>
            <li className="flex items-center">
              <div className="flex-shrink-0 rounded-full bg-green-400 p-1">
                <Check className="h-4 w-4 text-navy" />
              </div>
              <span className="ml-3 text-gray-300">Standard processing speed</span>
            </li>
            <li className="flex items-center">
              <div className="flex-shrink-0 rounded-full bg-green-400 p-1">
                <Check className="h-4 w-4 text-navy" />
              </div>
              <span className="ml-3 text-gray-300">Email support</span>
            </li>
          </ul>
          <div className="mt-8">
            <button className={buttonVariants({ className: "w-full justify-center bg-green-400 text-navy hover:bg-green-500" })}>
              Current Plan
            </button>
          </div>
        </div>

        {/* Premium Plan */}
        <div className="relative rounded-lg border-2 border-green-400 bg-navy p-8 shadow-lg">
          <div className="absolute -top-5 left-0 right-0 mx-auto w-32 rounded-full bg-green-400 py-1 text-center text-sm font-semibold text-navy">
            Popular
          </div>
          <h3 className="text-xl font-bold text-white">Premium</h3>
          <p className="mt-4 text-gray-300">For content creators and small teams</p>
          <div className="mt-6 flex items-baseline">
            <span className="text-5xl font-extrabold text-white">$29</span>
            <span className="ml-1 text-xl font-medium text-gray-300">/month</span>
          </div>
          <ul className="mt-8 space-y-4">
            <li className="flex items-center">
              <div className="flex-shrink-0 rounded-full bg-green-400 p-1">
                <Check className="h-4 w-4 text-navy" />
              </div>
              <span className="ml-3 text-gray-300">Process up to 1,000 images/month</span>
            </li>
            <li className="flex items-center">
              <div className="flex-shrink-0 rounded-full bg-green-400 p-1">
                <Check className="h-4 w-4 text-navy" />
              </div>
              <span className="ml-3 text-gray-300">Process up to 10 hours of video/month</span>
            </li>
            <li className="flex items-center">
              <div className="flex-shrink-0 rounded-full bg-green-400 p-1">
                <Check className="h-4 w-4 text-navy" />
              </div>
              <span className="ml-3 text-gray-300">Advanced blur customization</span>
            </li>
            <li className="flex items-center">
              <div className="flex-shrink-0 rounded-full bg-green-400 p-1">
                <Check className="h-4 w-4 text-navy" />
              </div>
              <span className="ml-3 text-gray-300">Faster processing speed</span>
            </li>
            <li className="flex items-center">
              <div className="flex-shrink-0 rounded-full bg-green-400 p-1">
                <Check className="h-4 w-4 text-navy" />
              </div>
              <span className="ml-3 text-gray-300">API access with 1,000 calls/month</span>
            </li>
            <li className="flex items-center">
              <div className="flex-shrink-0 rounded-full bg-green-400 p-1">
                <Check className="h-4 w-4 text-navy" />
              </div>
              <span className="ml-3 text-gray-300">Priority email support</span>
            </li>
          </ul>
          <div className="mt-8">
            <button className={buttonVariants({ className: "w-full justify-center bg-green-400 text-navy hover:bg-green-500" })}>
              Upgrade
            </button>
          </div>
        </div>

        {/* Enterprise Plan */}
        <div className="rounded-lg border border-navy-light bg-navy p-8 shadow-lg">
          <h3 className="text-xl font-bold text-white">Enterprise</h3>
          <p className="mt-4 text-gray-300">For organizations with advanced needs</p>
          <div className="mt-6 flex items-baseline">
            <span className="text-5xl font-extrabold text-white">Custom</span>
          </div>
          <ul className="mt-8 space-y-4">
            <li className="flex items-center">
              <div className="flex-shrink-0 rounded-full bg-green-400 p-1">
                <Check className="h-4 w-4 text-navy" />
              </div>
              <span className="ml-3 text-gray-300">Unlimited processing</span>
            </li>
            <li className="flex items-center">
              <div className="flex-shrink-0 rounded-full bg-green-400 p-1">
                <Check className="h-4 w-4 text-navy" />
              </div>
              <span className="ml-3 text-gray-300">Full customization options</span>
            </li>
            <li className="flex items-center">
              <div className="flex-shrink-0 rounded-full bg-green-400 p-1">
                <Check className="h-4 w-4 text-navy" />
              </div>
              <span className="ml-3 text-gray-300">Fastest processing speed</span>
            </li>
            <li className="flex items-center">
              <div className="flex-shrink-0 rounded-full bg-green-400 p-1">
                <Check className="h-4 w-4 text-navy" />
              </div>
              <span className="ml-3 text-gray-300">Unlimited API access</span>
            </li>
            <li className="flex items-center">
              <div className="flex-shrink-0 rounded-full bg-green-400 p-1">
                <Check className="h-4 w-4 text-navy" />
              </div>
              <span className="ml-3 text-gray-300">Dedicated account manager</span>
            </li>
            <li className="flex items-center">
              <div className="flex-shrink-0 rounded-full bg-green-400 p-1">
                <Check className="h-4 w-4 text-navy" />
              </div>
              <span className="ml-3 text-gray-300">On-premise deployment option</span>
            </li>
          </ul>
          <div className="mt-8">
            <button className={buttonVariants({ className: "w-full justify-center border border-blue-400 bg-transparent text-blue-400 hover:bg-blue-400 hover:text-navy" })}>
              Contact Sales
            </button>
          </div>
        </div>
      </div>

      {/* FAQ */}
      <div className="rounded-lg border border-navy-light bg-navy p-6">
        <h2 className="text-xl font-bold text-white">Frequently Asked Questions</h2>
        <div className="mt-6 space-y-6">
          <div>
            <h3 className="text-lg font-medium text-white">Can I change plans at any time?</h3>
            <p className="mt-2 text-gray-300">
              Yes, you can upgrade, downgrade, or cancel your subscription at any time. Changes to your subscription will take effect immediately.
            </p>
          </div>
          <div>
            <h3 className="text-lg font-medium text-white">How does the 14-day free trial work?</h3>
            <p className="mt-2 text-gray-300">
              When you sign up for any paid plan, you'll get a 14-day free trial. You won't be charged until the trial period ends, and you can cancel at any time during the trial.
            </p>
          </div>
          <div>
            <h3 className="text-lg font-medium text-white">What payment methods do you accept?</h3>
            <p className="mt-2 text-gray-300">
              We accept all major credit cards, including Visa, Mastercard, American Express, and Discover. We also support payment via PayPal.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

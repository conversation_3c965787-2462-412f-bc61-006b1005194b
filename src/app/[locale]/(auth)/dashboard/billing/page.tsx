// Commented out unused import
// import { getTranslations } from 'next-intl/server';

import { NewBillingPageClient } from './new-page-client';

export async function generateMetadata(_props: { params: { locale: string } }) {
  // Commented out unused variable
  // const t = await getTranslations({
  //   locale: props.params.locale,
  //   namespace: 'Billing',
  // });

  return {
    title: 'Subscription Plans - Guardiavision',
    description: 'Choose the subscription plan that best fits your needs.',
  };
}

const BillingPage = () => {
  return <NewBillingPageClient />;
};

export default BillingPage;

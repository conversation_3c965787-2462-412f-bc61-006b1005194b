'use client';

import { useEffect, useState } from 'react';
import { useUserData } from '@/contexts/UserDataContext';
import { PlanManager } from '@/components/subscription/PlanManager';
import { CreditPurchase } from '@/components/credits/CreditPurchase';
import { Settings, CreditCard, History, Info } from 'lucide-react';

export function PlanManagementClient() {
  const { userData, refreshUserData } = useUserData();
  const [activeTab, setActiveTab] = useState<'plans' | 'credits' | 'history'>('plans');
  const [subscriptionHistory, setSubscriptionHistory] = useState([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (activeTab === 'history') {
      fetchSubscriptionHistory();
    }
  }, [activeTab]);

  const fetchSubscriptionHistory = async () => {
    setLoading(true);
    try {
      // Mock subscription history - in real app, fetch from API
      const mockHistory = [
        {
          id: 1,
          action: 'plan_upgrade',
          from_plan: 'standard',
          to_plan: 'pro',
          effective_date: new Date().toISOString(),
          metadata: { credits_added: 3500 },
        },
        {
          id: 2,
          action: 'clerk_sync',
          plan_id: 'standard',
          effective_date: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
          metadata: { credits_added: 700 },
        },
      ];
      setSubscriptionHistory(mockHistory);
    } catch (error) {
      console.error('Error fetching subscription history:', error);
    } finally {
      setLoading(false);
    }
  };

  const handlePlanChange = () => {
    refreshUserData();
  };

  const tabs = [
    { id: 'plans', label: 'Manage Plans', icon: Settings },
    { id: 'credits', label: 'Buy Credits', icon: CreditCard },
    { id: 'history', label: 'History', icon: History },
  ];

  return (
    <div className="min-h-screen bg-navy text-white p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">Plan Management</h1>
          <p className="text-gray-400">
            Manage your subscription, purchase credits, and view your billing history
          </p>
        </div>

        {/* Current Status */}
        <div className="bg-gray-900 rounded-lg p-6 mb-8 border border-gray-700">
          <h2 className="text-xl font-semibold mb-4 flex items-center">
            <Info className="h-5 w-5 mr-2" />
            Current Status
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-400">
                {userData?.subscription_type || 'Free'}
              </div>
              <div className="text-sm text-gray-400">Current Plan</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-400">
                {userData?.credits?.toLocaleString() || '0'}
              </div>
              <div className="text-sm text-gray-400">Available Credits</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-400">
                {userData?.subscription_status || 'Active'}
              </div>
              <div className="text-sm text-gray-400">Status</div>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="mb-8">
          <div className="border-b border-gray-700">
            <nav className="-mb-px flex space-x-8">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id as any)}
                    className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center ${
                      activeTab === tab.id
                        ? 'border-purple-500 text-purple-400'
                        : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'
                    }`}
                  >
                    <Icon className="h-4 w-4 mr-2" />
                    {tab.label}
                  </button>
                );
              })}
            </nav>
          </div>
        </div>

        {/* Tab Content */}
        <div className="mb-8">
          {activeTab === 'plans' && (
            <div>
              <PlanManager
                currentPlan={userData?.subscription_type || 'Free'}
                currentCredits={userData?.credits || 0}
                onPlanChange={handlePlanChange}
              />
            </div>
          )}

          {activeTab === 'credits' && (
            <div>
              <CreditPurchase
                currentCredits={userData?.credits || 0}
              />
            </div>
          )}

          {activeTab === 'history' && (
            <div className="bg-gray-900 rounded-lg p-6 border border-gray-700">
              <h2 className="text-xl font-semibold mb-4">Subscription History</h2>
              
              {loading ? (
                <div className="animate-pulse space-y-4">
                  {[1, 2, 3].map(i => (
                    <div key={i} className="h-16 bg-gray-700 rounded"></div>
                  ))}
                </div>
              ) : subscriptionHistory.length > 0 ? (
                <div className="space-y-4">
                  {subscriptionHistory.map((item: any) => (
                    <div
                      key={item.id}
                      className="bg-gray-800 rounded-lg p-4 border border-gray-600"
                    >
                      <div className="flex justify-between items-start">
                        <div>
                          <h3 className="font-semibold text-white">
                            {item.action.replace('_', ' ').replace(/\b\w/g, (l: string) => l.toUpperCase())}
                          </h3>
                          <p className="text-sm text-gray-400">
                            {item.metadata?.from_plan && item.metadata?.to_plan
                              ? `${item.metadata.from_plan} → ${item.metadata.to_plan}`
                              : `Plan: ${item.plan_id}`}
                          </p>
                          {item.metadata?.credits_added && (
                            <p className="text-sm text-green-400">
                              +{item.metadata.credits_added} credits added
                            </p>
                          )}
                        </div>
                        <div className="text-right">
                          <div className="text-sm text-gray-400">
                            {new Date(item.effective_date).toLocaleDateString()}
                          </div>
                          <div className="text-xs text-gray-500">
                            {new Date(item.effective_date).toLocaleTimeString()}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <History className="h-12 w-12 text-gray-600 mx-auto mb-4" />
                  <p className="text-gray-400">No subscription history yet</p>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Help Section */}
        <div className="bg-gray-900 rounded-lg p-6 border border-gray-700">
          <h2 className="text-lg font-semibold mb-4">Need Help?</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <h3 className="font-semibold text-white mb-2">Plan Changes</h3>
              <ul className="text-gray-400 space-y-1">
                <li>• Upgrades require payment</li>
                <li>• Downgrades are immediate</li>
                <li>• Credits are always preserved</li>
                <li>• Changes take effect immediately</li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold text-white mb-2">Credits</h3>
              <ul className="text-gray-400 space-y-1">
                <li>• Purchase additional credits anytime</li>
                <li>• Credits expire based on plan rules</li>
                <li>• Unused credits carry over</li>
                <li>• Pro/Premium credits don't expire</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

import { getTranslations } from 'next-intl/server';
import { UploadsClient } from './uploads-client';

export async function generateMetadata(props: { params: { locale: string } }) {
  const t = await getTranslations({
    locale: props.params.locale,
    namespace: 'Dashboard',
  });

  return {
    title: `${t('meta_title')} - Uploads`,
    description: t('meta_description'),
  };
}

export default function UploadsPage() {
  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="mb-6 text-3xl font-bold text-white">Your Uploads</h1>
      <p className="mb-8 text-gray-300">
        Upload photos and videos to blur faces, license plates, and other sensitive information.
      </p>
      
      <UploadsClient />
    </div>
  );
}

export const dynamic = 'force-dynamic';

'use client';

import { useState, useEffect } from 'react';
import { useUser } from '@clerk/nextjs';
import { FileUploader } from '@/components/uploads/FileUploader';
import { MediaGallery } from '@/components/uploads/MediaGallery';
import { useSupabaseClient } from '@/utils/supabase/client';
import { Loader2, AlertCircle } from 'lucide-react';

export function UploadsClient() {
  const { user, isLoaded } = useUser();
  const supabase = useSupabaseClient();
  const [credits, setCredits] = useState<number | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshGallery, setRefreshGallery] = useState(0);

  // Fetch user credits
  useEffect(() => {
    if (!isLoaded || !user) return;

    async function fetchUserCredits() {
      try {
        const { data, error } = await supabase
          .from('users')
          .select('credits')
          .eq('id', user.id)
          .single();
        
        if (error) {
          throw error;
        }
        
        setCredits(data.credits);
      } catch (err: any) {
        console.error('Error fetching user credits:', err);
        setError(`Failed to load user credits: ${err.message}`);
      } finally {
        setLoading(false);
      }
    }

    fetchUserCredits();
  }, [user, isLoaded, supabase]);

  // Handle upload completion
  const handleUploadComplete = () => {
    // Refresh the gallery
    setRefreshGallery(prev => prev + 1);
    
    // Refresh user credits
    if (user) {
      supabase
        .from('users')
        .select('credits')
        .eq('id', user.id)
        .single()
        .then(({ data, error }) => {
          if (!error && data) {
            setCredits(data.credits);
          }
        });
    }
  };

  // Setup storage buckets
  useEffect(() => {
    async function setupStorage() {
      try {
        const response = await fetch('/api/setup-storage');
        if (!response.ok) {
          const data = await response.json();
          console.error('Error setting up storage:', data.error);
        }
      } catch (err) {
        console.error('Failed to set up storage:', err);
      }
    }

    setupStorage();
  }, []);

  if (!isLoaded) {
    return (
      <div className="flex justify-center items-center py-12">
        <Loader2 className="h-8 w-8 text-blue-500 animate-spin" />
        <span className="ml-2 text-gray-300">Loading...</span>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-400">Please sign in to access your uploads</p>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Credits display */}
      <div className="bg-navy-light rounded-lg p-4 flex justify-between items-center">
        <div>
          <h2 className="text-lg font-medium text-white">Your Credits</h2>
          {loading ? (
            <p className="text-gray-400">Loading credits...</p>
          ) : (
            <p className="text-2xl font-bold text-white">{credits}</p>
          )}
        </div>
        <button className="bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-md transition-colors">
          Upgrade
        </button>
      </div>
      
      {/* Error message */}
      {error && (
        <div className="bg-red-900/50 text-white p-3 rounded-md flex items-center space-x-2">
          <AlertCircle className="h-5 w-5" />
          <span>{error}</span>
        </div>
      )}
      
      {/* File uploader */}
      <div className="bg-navy-light rounded-lg p-6">
        <h2 className="text-xl font-bold text-white mb-4">Upload Media</h2>
        <FileUploader onUploadComplete={handleUploadComplete} />
      </div>
      
      {/* Media gallery */}
      <div className="bg-navy-light rounded-lg p-6">
        <h2 className="text-xl font-bold text-white mb-4">Your Media</h2>
        <MediaGallery key={refreshGallery} />
      </div>
    </div>
  );
}

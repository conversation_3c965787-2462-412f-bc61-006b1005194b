"use client";

import { useState, useEffect } from 'react';
import Link from 'next/link';
import {
  LayoutGrid,
  FileEdit,
  CheckCircle,
  Clock,
  Calendar,
  CalendarDays,
  HelpCircle
} from 'lucide-react';
import { usePathname } from 'next/navigation';
import { useUser, useClerk } from '@clerk/nextjs';
import { useSupabaseClient } from '@/utils/supabase/client';
import { UserDataProvider } from '@/contexts/UserDataContext';
import { RefreshButton } from '@/components/dashboard/RefreshButton';

export function MinimalDashboardLayoutClient(props: { children: React.ReactNode }) {
  const pathname = usePathname();
  const { user, isLoaded: isUserLoaded } = useUser();
  const { signOut } = useClerk();
  const supabase = useSupabaseClient();
  const [credits, setCredits] = useState<number | null>(null);
  const [subscriptionType, setSubscriptionType] = useState<string>('Free');
  const [isLoading, setIsLoading] = useState(true);
  const [lastRefreshed, setLastRefreshed] = useState<Date | null>(null);

  // Function to refresh user data
  const refreshUserData = async () => {
    // Skip if user is not loaded or undefined
    if (!isUserLoaded || !user) {
      console.log('User not available, skipping refresh');
      return;
    }

    setIsLoading(true);
    console.log('Refreshing user data for:', user.id);

    try {
      // Fetch user data from Supabase
      const { data, error } = await supabase
        .from('users')
        .select('credits, subscription_type')
        .eq('id', user.id)
        .single();

      if (error) {
        console.error('Error fetching user data:', error);
        // Set default values on error
        setCredits(50);
        setSubscriptionType('Free');
      } else if (data) {
        console.log('User data fetched successfully:', data);
        setCredits(data.credits);
        setSubscriptionType(data.subscription_type || 'Free');
      }

      // Update last refreshed timestamp
      setLastRefreshed(new Date());
    } catch (err) {
      console.error('Unexpected error fetching user data:', err);
      // Set default values on error
      setCredits(50);
      setSubscriptionType('Free');
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch user data from Supabase on initial load
  useEffect(() => {
    // Skip if user is not loaded yet
    if (!isUserLoaded) {
      console.log('User not loaded yet, waiting...');
      return;
    }

    // Skip if user is undefined (not logged in)
    if (!user) {
      console.log('No user logged in, skipping data fetch');
      setIsLoading(false);
      return;
    }

    // Fetch data when user is loaded
    refreshUserData();
  }, [user, isUserLoaded]);

  // Navigation items for the sidebar
  const navItems = [
    {
      label: 'All',
      href: '/dashboard',
      icon: <LayoutGrid className="h-5 w-5" />,
      exact: true
    },
    {
      label: 'Draft',
      href: '/dashboard/draft',
      icon: <FileEdit className="h-5 w-5" />
    },
    {
      label: 'Completed',
      href: '/dashboard/completed',
      icon: <CheckCircle className="h-5 w-5" />
    }
  ];

  // Time filter items
  const timeFilters = [
    {
      label: '24 hours ago',
      href: '/dashboard/recent',
      icon: <Clock className="h-5 w-5" />
    },
    {
      label: 'A week ago',
      href: '/dashboard/week',
      icon: <Calendar className="h-5 w-5" />
    },
    {
      label: 'A month ago',
      href: '/dashboard/month',
      icon: <CalendarDays className="h-5 w-5" />
    }
  ];

  // Check if a nav item is active
  const isActive = (href: string, exact = false) => {
    if (!pathname) return false;

    if (exact) {
      return pathname === href || pathname === href + '/';
    }
    return pathname.startsWith(href);
  };

  return (
    <div className="flex min-h-screen bg-black text-white">
      {/* Top navbar */}
      <nav className="fixed top-0 z-50 w-full border-b border-gray-800 bg-black/90 backdrop-blur-md">
        <div className="px-4">
          <div className="flex h-16 items-center justify-between">
            <div className="flex items-center ml-1">
              <Link href="/dashboard" className="flex items-center">
                <div className="flex items-center">
                  <span className="text-xl font-bold text-white">Guardia<span className="vision-text">Vision</span></span>
                </div>
              </Link>

              <style jsx global>{`
                .vision-text {
                  background: linear-gradient(90deg, #10b981, #3b82f6, #10b981);
                  background-size: 200% auto;
                  color: transparent;
                  -webkit-background-clip: text;
                  background-clip: text;
                  animation: gradient 3s linear infinite;
                }

                @keyframes gradient {
                  0% {
                    background-position: 0% 50%;
                  }
                  50% {
                    background-position: 100% 50%;
                  }
                  100% {
                    background-position: 0% 50%;
                  }
                }
              `}</style>
            </div>

            <div className="flex items-center">
              {/* Custom user dropdown */}
              <div className="relative">
                <button
                  onClick={() => {
                    const menu = document.getElementById('user-dropdown-menu');
                    if (menu) menu.classList.toggle('hidden');
                  }}
                  className="flex items-center space-x-2"
                >
                  <div className="flex flex-col items-start">
                    <div className="flex items-center">
                      <span className="text-lg font-medium text-white">{user?.username || user?.firstName || 'UnknownHero'}</span>
                      <span className="ml-2 rounded-full border border-gray-700 bg-gray-800 px-2 py-0.5 text-xs text-white">{subscriptionType || 'Free'}</span>
                    </div>
                    <span className="text-sm text-gray-400">{user?.emailAddresses[0]?.emailAddress || '<EMAIL>'}</span>
                  </div>
                </button>

                {/* Dropdown menu */}
                <div
                  id="user-dropdown-menu"
                  className="absolute right-0 top-full z-50 mt-1 hidden w-48 divide-y divide-gray-700 rounded-md border border-gray-700 bg-black shadow-lg"
                >
                  <div className="py-1">
                    <Link
                      href="/dashboard"
                      className="block px-4 py-2 text-sm text-white hover:bg-gray-800"
                    >
                      Workspace
                    </Link>
                    <Link
                      href="/dashboard/user-profile"
                      className="block px-4 py-2 text-sm text-white hover:bg-gray-800"
                    >
                      Account settings
                    </Link>
                  </div>
                  <div className="py-1">
                    <button
                      onClick={() => {
                        if (window.confirm('Are you sure you want to log out?')) {
                          signOut();
                        }
                      }}
                      className="block w-full px-4 py-2 text-left text-sm text-red-500 hover:bg-gray-800"
                    >
                      Log out
                    </button>
                  </div>
                </div>

                {/* Close dropdown when clicking outside */}
                <script dangerouslySetInnerHTML={{
                  __html: `
                    document.addEventListener('click', function(e) {
                      const menu = document.getElementById('user-dropdown-menu');
                      const button = e.target.closest('button');

                      if (menu && !menu.contains(e.target) && !button) {
                        menu.classList.add('hidden');
                      }
                    });
                  `
                }} />
              </div>
            </div>
          </div>
        </div>
      </nav>

      {/* Left sidebar */}
      <div className="fixed left-0 top-16 bottom-0 w-[205px] border-r border-gray-800 bg-black">
        <div className="flex h-full flex-col p-4">
          {/* User info */}
          <div className="mb-6 px-1">
            <div className="flex items-center">
              <span className="text-xl font-medium text-white">{user?.username || user?.firstName || 'UnknownHero'}</span>
            </div>

            {/* Plan and credits info */}
            <div className="mt-4 space-y-3">
              {/* Plan display */}
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className="mr-2 rounded-full bg-purple-500/20 p-1.5">
                    <svg className="h-4 w-4 text-purple-400" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" fill="currentColor" strokeWidth="1" />
                      <path d="M2 17L12 22L22 17" stroke="currentColor" strokeWidth="1" />
                      <path d="M2 12L12 17L22 12" stroke="currentColor" strokeWidth="1" />
                    </svg>
                  </div>
                  <span className="text-sm text-gray-400">Current Plan</span>
                </div>
                {isLoading ? (
                  <span className="rounded bg-gray-800 px-2 py-0.5 text-sm text-white">Loading...</span>
                ) : (
                  <span className="rounded bg-purple-500/20 px-2.5 py-0.5 text-sm font-medium text-purple-300">{subscriptionType || 'Free'}</span>
                )}
              </div>

              {/* Credits display with refresh button */}
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className="mr-2 rounded-full bg-purple-500/20 p-1.5">
                    <svg className="h-4 w-4 text-purple-400" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M12 15V18M12 15C13.6569 15 15 13.6569 15 12C15 10.3431 13.6569 9 12 9C10.3431 9 9 10.3431 9 12C9 13.6569 10.3431 15 12 15ZM20 12C20 16.4183 16.4183 20 12 20C7.58172 20 4 16.4183 4 12C4 7.58172 7.58172 4 12 4C16.4183 4 20 7.58172 20 12Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                  </div>
                  <span className="text-sm text-gray-400">Credits</span>
                </div>
                <div className="flex items-center">
                  <span className="text-sm font-bold text-white">{credits !== null ? credits : 50}</span>
                  <div className="ml-2">
                    <svg className="h-4 w-4 text-purple-400" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M6 3L2 9L12 21L22 9L18 3H6Z" fill="currentColor" />
                      <path d="M12 9L2 9" stroke="rgba(255,255,255,0.3)" strokeWidth="0.5" />
                      <path d="M12 9L22 9" stroke="rgba(255,255,255,0.3)" strokeWidth="0.5" />
                      <path d="M12 9L12 21" stroke="rgba(255,255,255,0.3)" strokeWidth="0.5" />
                      <path d="M12 9L6 3" stroke="rgba(255,255,255,0.3)" strokeWidth="0.5" />
                      <path d="M12 9L18 3" stroke="rgba(255,255,255,0.3)" strokeWidth="0.5" />
                    </svg>
                  </div>
                  <button
                    onClick={refreshUserData}
                    disabled={isLoading}
                    className="ml-2 rounded-full bg-gradient-to-r from-purple-500/20 to-indigo-500/20 p-1.5 text-purple-300 transition-all duration-300 hover:from-purple-500/30 hover:to-indigo-500/30 hover:text-purple-200 hover:shadow-sm hover:shadow-purple-500/20"
                    title="Sync credits"
                  >
                    <svg
                      className={`h-3.5 w-3.5 transition-transform duration-700 ${isLoading ? 'animate-spin' : 'group-hover:rotate-180'}`}
                      viewBox="0 0 24 24"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M21.1679 8C19.6247 4.46819 16.1006 2 11.9999 2C6.81459 2 2.55104 5.94668 2.04932 11"
                        stroke="currentColor"
                        strokeWidth="1.5"
                        strokeLinecap="round"
                      />
                      <path
                        d="M17 8H21.4C21.7314 8 22 7.73137 22 7.4V3"
                        stroke="currentColor"
                        strokeWidth="1.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                      <path
                        d="M2.88146 16C4.42458 19.5318 7.94874 22 12.0494 22C17.2347 22 21.4983 18.0533 22 13"
                        stroke="currentColor"
                        strokeWidth="1.5"
                        strokeLinecap="round"
                      />
                      <path
                        d="M7.04932 16H2.64932C2.31795 16 2.04932 16.2686 2.04932 16.6V21"
                        stroke="currentColor"
                        strokeWidth="1.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                  </button>
                </div>
              </div>
            </div>

            <Link
              href="/dashboard/billing"
              className="upgrade-button group relative mt-6 flex w-full items-center justify-center overflow-hidden rounded-md bg-gradient-to-r from-purple-500 to-violet-600 py-3 text-base font-medium text-white transition-all duration-300 hover:from-purple-600 hover:to-violet-700 hover:shadow-lg hover:shadow-purple-500/30"
              style={{
                backgroundSize: '200% 100%',
                animation: 'gradient-shift 3s ease infinite, float-button 3s ease-in-out infinite'
              }}
            >
              {/* Animated background effect */}
              <span className="absolute inset-0 z-0 bg-gradient-to-r from-purple-400 to-violet-500 opacity-0 blur-xl transition-opacity duration-500 group-hover:opacity-70"></span>

              {/* Animated border glow */}
              <span className="absolute inset-0 -z-10 rounded-md opacity-0 group-hover:opacity-100 transition-opacity duration-500"
                    style={{
                      background: 'linear-gradient(90deg, #a855f7, #8b5cf6, #a855f7)',
                      backgroundSize: '200% 100%',
                      animation: 'shimmer 2s linear infinite'
                    }}></span>

              {/* Animated shine effect */}
              <span className="absolute inset-0 -z-10 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-1000 bg-gradient-to-r from-transparent via-white/20 to-transparent"></span>

              {/* Particles effect (small diamonds floating) */}
              <span className="absolute top-1/4 left-1/4 h-1.5 w-1.5 rounded-full bg-purple-200 opacity-0 group-hover:opacity-100"
                    style={{ animation: 'float 3s ease-in-out infinite' }}></span>
              <span className="absolute top-1/3 right-1/4 h-2 w-2 rounded-full bg-violet-200 opacity-0 group-hover:opacity-100"
                    style={{ animation: 'float 2.3s ease-in-out infinite 0.2s' }}></span>
              <span className="absolute bottom-1/3 left-1/3 h-1 w-1 rounded-full bg-fuchsia-200 opacity-0 group-hover:opacity-100"
                    style={{ animation: 'float 2.7s ease-in-out infinite 0.5s' }}></span>
              <span className="absolute bottom-1/4 right-1/3 h-1.5 w-1.5 rounded-full bg-pink-200 opacity-0 group-hover:opacity-100"
                    style={{ animation: 'float 2.5s ease-in-out infinite 0.7s' }}></span>

              {/* Diamond icon with internal edges and glow */}
              <div className="diamond-icon relative z-10 mr-2 transition-transform duration-500 group-hover:scale-125 group-hover:rotate-12 group-hover:animate-pulse">
                <svg className="h-5 w-5" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  {/* Glow effect */}
                  <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
                    <feGaussianBlur stdDeviation="2" result="blur" />
                    <feComposite in="SourceGraphic" in2="blur" operator="over" />
                  </filter>

                  {/* Main diamond shape */}
                  <path
                    d="M6 3L2 9L12 21L22 9L18 3H6Z"
                    fill="white"
                    className="transition-all duration-500 group-hover:fill-purple-100"
                    style={{ filter: 'url(#glow)' }}
                  />

                  {/* Internal edges with animation */}
                  <path
                    d="M12 9L2 9"
                    stroke="rgba(255,255,255,0.3)"
                    strokeWidth="0.5"
                    className="transition-all duration-500 group-hover:stroke-purple-300 group-hover:stroke-[1.5]"
                  />
                  <path
                    d="M12 9L22 9"
                    stroke="rgba(255,255,255,0.3)"
                    strokeWidth="0.5"
                    className="transition-all duration-500 group-hover:stroke-purple-300 group-hover:stroke-[1.5]"
                  />
                  <path
                    d="M12 9L12 21"
                    stroke="rgba(255,255,255,0.3)"
                    strokeWidth="0.5"
                    className="transition-all duration-500 group-hover:stroke-purple-300 group-hover:stroke-[1.5]"
                  />
                  <path
                    d="M12 9L6 3"
                    stroke="rgba(255,255,255,0.3)"
                    strokeWidth="0.5"
                    className="transition-all duration-500 group-hover:stroke-purple-300 group-hover:stroke-[1.5]"
                  />
                  <path
                    d="M12 9L18 3"
                    stroke="rgba(255,255,255,0.3)"
                    strokeWidth="0.5"
                    className="transition-all duration-500 group-hover:stroke-purple-300 group-hover:stroke-[1.5]"
                  />
                </svg>
              </div>

              {/* Text with animation */}
              <span className="relative z-10 transition-all duration-300 group-hover:tracking-wider group-hover:font-bold">
                Upgrade
              </span>
            </Link>


          </div>

          {/* Navigation */}
          <div className="mb-6">
            <div className="mb-2 text-xs font-semibold uppercase text-gray-500">Navigation</div>
            <ul className="space-y-1">
              {navItems.map((item) => (
                <li key={item.href}>
                  <Link
                    href={item.href}
                    className={`flex items-center space-x-2 rounded-md px-3 py-2 text-sm ${
                      isActive(item.href, item.exact)
                        ? 'bg-gray-800 text-white'
                        : 'text-gray-400 hover:bg-gray-800/50 hover:text-white'
                    }`}
                  >
                    {item.icon}
                    <span>{item.label}</span>
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Uploaded */}
          <div className="mb-6">
            <div className="mb-2 text-xs font-semibold uppercase text-gray-500">Uploaded</div>
            <ul className="space-y-1">
              {timeFilters.map((item) => (
                <li key={item.href}>
                  <Link
                    href={item.href}
                    className={`flex items-center space-x-2 rounded-md px-3 py-2 text-sm ${
                      isActive(item.href)
                        ? 'bg-gray-800 text-white'
                        : 'text-gray-400 hover:bg-gray-800/50 hover:text-white'
                    }`}
                  >
                    {item.icon}
                    <span>{item.label}</span>
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Help button at bottom */}
          <div className="mt-auto">
            <Link
              href="/dashboard/help"
              className="flex items-center space-x-2 rounded-md px-3 py-2 text-sm text-gray-400 hover:bg-gray-800/50 hover:text-white"
            >
              <HelpCircle className="h-5 w-5" />
              <span>Help</span>
            </Link>
          </div>
        </div>
      </div>

      {/* Main content */}
      <div className="ml-[200px] mt-16 min-h-[calc(100vh-64px)] w-[calc(100%-200px)]">
        <div className="p-4">
          {props.children}
        </div>
      </div>
    </div>
  );
}

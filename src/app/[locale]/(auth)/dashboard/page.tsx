"use client";

import { useEffect } from 'react';
import { MediaDashboard } from '@/components/dashboard/MediaDashboard';
import { useUser } from '@clerk/nextjs';
import { useSupabaseClient } from '@/utils/supabase/client';
import { useUserData } from '@/contexts/UserDataContext';
import { PlanProtect } from '@/components/billing/PlanProtection';

const DashboardIndexPage = () => {
  const { user } = useUser();
  const supabase = useSupabaseClient();

  // Try to use the context, but handle the case where it might not be available
  let userData = { credits: null, subscriptionType: null, isLoading: true };
  try {
    userData = useUserData();
  } catch (error) {
    console.log('UserData context not available, using default values');
  }

  // Force a fresh database query on page load
  useEffect(() => {
    if (!user) return;

    const fetchCredits = async () => {
      const timestamp = new Date().getTime();
      console.log(`PAGE LOAD QUERY (${timestamp}): Fetching credits for user ${user.id}`);

      try {
        const { data, error } = await supabase
          .from('users')
          .select('credits, subscription_type')
          .eq('id', user.id)
          .single()
          .headers({ 'Cache-Control': 'no-cache', 'Pragma': 'no-cache', 'X-Cache-Bust': timestamp.toString() });

        if (!error && data) {
          console.log(`PAGE LOAD QUERY: Found credits: ${data.credits}`);
          console.log('%c PAGE LOAD CREDITS: ' + data.credits, 'background: #222; color: #bada55; font-size: 20px');
        } else {
          console.error('PAGE LOAD QUERY: Error or no data:', error);
        }
      } catch (err) {
        console.error('PAGE LOAD QUERY: Error fetching credits:', err);
      }
    };

    fetchCredits();
  }, [user, supabase]);

  return <MediaDashboard key={Date.now()} />;
};

export default DashboardIndexPage;

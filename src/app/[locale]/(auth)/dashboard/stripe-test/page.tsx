'use client';

import { useState } from 'react';
import { StripeTestButton } from '@/components/stripe/StripeTestButton';

export default function StripeTestPage() {
  const [isYearly, setIsYearly] = useState(false);

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-white mb-8">Stripe Integration Test</h1>

        <div className="bg-navy-light rounded-lg p-6 mb-8">
          <h2 className="text-xl font-semibold text-white mb-4">Test Instructions</h2>
          <div className="text-gray-300 space-y-2">
            <p>1. Make sure you've added your Stripe test keys to .env.local</p>
            <p>2. Create products in your Stripe Dashboard (Test mode)</p>
            <p>3. Add the price IDs to your environment variables</p>
            <p>4. Use test card numbers like: 4242 4242 4242 4242</p>
          </div>
        </div>

        <div className="bg-navy-light rounded-lg p-6 mb-8">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-white">Billing Period</h2>
            <div className="flex items-center space-x-4">
              <span className={`text-sm ${!isYearly ? 'text-white' : 'text-gray-400'}`}>Monthly</span>
              <button
                onClick={() => setIsYearly(!isYearly)}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                  isYearly ? 'bg-green-400' : 'bg-gray-600'
                }`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    isYearly ? 'translate-x-6' : 'translate-x-1'
                  }`}
                />
              </button>
              <span className={`text-sm ${isYearly ? 'text-white' : 'text-gray-400'}`}>Yearly</span>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Standard Plan */}
          <div className="bg-navy-light rounded-lg p-6">
            <h3 className="text-lg font-semibold text-white mb-2">Standard Plan</h3>
            <p className="text-gray-300 mb-4">700 credits per month</p>
            <p className="text-2xl font-bold text-white mb-6">
              €5/month
            </p>
            <StripeTestButton
              planType="STANDARD"
              isYearly={false}
              priceId="price_1RSIGpR6OeqomohOPQNu7awg"
              planName="Standard"
              price={5}
            />
          </div>

          {/* Pro Plan */}
          <div className="bg-navy-light rounded-lg p-6 border-2 border-green-400">
            <div className="text-center mb-4">
              <span className="bg-green-400 text-navy px-3 py-1 rounded-full text-sm font-semibold">
                Most Popular
              </span>
            </div>
            <h3 className="text-lg font-semibold text-white mb-2">Pro Plan</h3>
            <p className="text-gray-300 mb-4">3,500 credits per month</p>
            <p className="text-2xl font-bold text-white mb-6">
              €26/month
            </p>
            <StripeTestButton
              planType="PRO"
              isYearly={false}
              priceId="price_1RSIHaR6OeqomohOzOEwFOgZ"
              planName="Pro"
              price={26}
            />
          </div>

          {/* Premium Plan */}
          <div className="bg-navy-light rounded-lg p-6">
            <h3 className="text-lg font-semibold text-white mb-2">Premium Plan</h3>
            <p className="text-gray-300 mb-4">14,500 credits per month</p>
            <p className="text-2xl font-bold text-white mb-6">
              €78/month
            </p>
            <StripeTestButton
              planType="PREMIUM"
              isYearly={false}
              priceId={undefined}
              planName="Premium"
              price={78}
            />
          </div>
        </div>

        <div className="mt-8 bg-yellow-900/20 border border-yellow-600 rounded-lg p-4">
          <h3 className="text-yellow-400 font-semibold mb-2">⚠️ Test Mode</h3>
          <p className="text-yellow-200 text-sm">
            This is running in Stripe test mode. Use test card numbers like 4242 4242 4242 4242.
            No real charges will be made.
          </p>
        </div>
      </div>
    </div>
  );
}

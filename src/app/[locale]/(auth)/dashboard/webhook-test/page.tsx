import { getTranslations } from 'next-intl/server';

import { WebhookTestClient } from './webhook-test-client';

export async function generateMetadata(props: { params: { locale: string } }) {
  const t = await getTranslations({
    locale: props.params.locale,
    namespace: 'Dashboard',
  });

  return {
    title: `${t('meta_title')} - Webhook Test`,
    description: t('meta_description'),
  };
}

export default function WebhookTestPage() {
  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="mb-6 text-3xl font-bold text-white">Clerk Webhook Test</h1>
      <p className="mb-8 text-gray-300">
        This page helps you verify that your Clerk webhook is properly configured.
      </p>
      
      <WebhookTestClient />
    </div>
  );
}

export const dynamic = 'force-dynamic';

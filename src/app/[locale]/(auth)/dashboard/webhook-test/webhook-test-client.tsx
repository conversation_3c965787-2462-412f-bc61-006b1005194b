'use client';

import { useState, useEffect } from 'react';

export function WebhookTestClient() {
  const [webhookSecret, setWebhookSecret] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [webhookEndpoint, setWebhookEndpoint] = useState<string>('');

  useEffect(() => {
    async function checkWebhookSecret() {
      try {
        const response = await fetch('/api/webhooks/clerk/test');
        const data = await response.json();
        
        setWebhookSecret(data.webhookSecretStatus);
        setWebhookEndpoint(data.webhookEndpoint);
        setLoading(false);
      } catch (err: any) {
        console.error('Error checking webhook secret:', err);
        setError(err.message || 'An error occurred');
        setLoading(false);
      }
    }

    checkWebhookSecret();
  }, []);

  return (
    <div className="space-y-8">
      <div className="rounded-lg bg-navy-light p-6">
        <h2 className="mb-4 text-xl font-semibold text-white">Webhook Configuration</h2>
        
        {loading ? (
          <p className="text-gray-300">Checking webhook configuration...</p>
        ) : error ? (
          <div className="rounded-lg bg-red-900 p-4 text-white">
            <p className="font-bold">Error:</p>
            <p>{error}</p>
          </div>
        ) : (
          <div className="space-y-4">
            <div>
              <p className="text-gray-400">Webhook Secret Status:</p>
              <p className={`font-medium ${webhookSecret === 'configured' ? 'text-green-500' : 'text-red-500'}`}>
                {webhookSecret === 'configured' ? 'Configured ✓' : 'Not Configured ✗'}
              </p>
            </div>
            
            <div>
              <p className="text-gray-400">Webhook Endpoint:</p>
              <p className="text-white break-all">{webhookEndpoint}</p>
            </div>
            
            <div className="mt-6 space-y-4">
              <h3 className="text-lg font-medium text-white">How to Configure Clerk Webhooks</h3>
              
              <ol className="list-decimal pl-5 space-y-2 text-gray-300">
                <li>Go to the <a href="https://dashboard.clerk.com" target="_blank" rel="noopener noreferrer" className="text-blue-400 hover:underline">Clerk Dashboard</a></li>
                <li>Select your application</li>
                <li>Navigate to <span className="text-white">Webhooks</span> in the sidebar</li>
                <li>Click <span className="text-white">Add Endpoint</span></li>
                <li>Enter the webhook URL: <code className="bg-navy-dark px-2 py-1 rounded text-white">{webhookEndpoint}</code></li>
                <li>Select the events you want to listen for (at minimum: <code className="bg-navy-dark px-2 py-1 rounded text-white">user.created</code>, <code className="bg-navy-dark px-2 py-1 rounded text-white">user.updated</code>, and <code className="bg-navy-dark px-2 py-1 rounded text-white">user.deleted</code>)</li>
                <li>Copy the <span className="text-white">Signing Secret</span> and add it to your <code className="bg-navy-dark px-2 py-1 rounded text-white">.env.local</code> file as <code className="bg-navy-dark px-2 py-1 rounded text-white">CLERK_WEBHOOK_SECRET</code></li>
                <li>Restart your application</li>
              </ol>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

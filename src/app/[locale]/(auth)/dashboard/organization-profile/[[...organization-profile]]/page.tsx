"use client";

import { OrganizationProfile } from '@clerk/nextjs';
import { useTranslations } from 'next-intl';

import { getI18nPath } from '@/utils/Helpers';
import { clerkAppearance } from '@/utils/clerk-appearance';

const OrganizationProfilePage = (props: { params: { locale: string } }) => {
  const t = useTranslations('OrganizationProfile');

  return (
    <div className="space-y-8">
      <div className="rounded-lg border border-navy-light bg-navy p-6">
        <h1 className="text-2xl font-bold text-white">{t('title_bar')}</h1>
        <p className="mt-2 text-gray-300">
          {t('title_bar_description')}
        </p>
      </div>

      <div className="rounded-lg border border-navy-light bg-navy p-6">
        <OrganizationProfile
          routing="path"
          path={getI18nPath(
            '/dashboard/organization-profile',
            props.params.locale,
          )}
          afterLeaveOrganizationUrl="/onboarding/organization-selection"
          appearance={clerkAppearance}
        />
      </div>
    </div>
  );
};

export default OrganizationProfilePage;

'use client';

import { enUS } from '@clerk/localizations';
import { Clerk<PERSON>rovider } from '@clerk/nextjs';

import { AppConfig } from '@/utils/AppConfig';
import { CreateUserInSupabase } from '@/components/auth/CreateUserInSupabase';
import { UserDataProvider } from '@/contexts/UserDataContext';
import './auth.css';

export default function AuthLayout(props: {
  children: React.ReactNode;
  params: { locale: string };
}) {
  const clerkLocale = enUS;
  let signInUrl = '/sign-in';
  let signUpUrl = '/sign-up';
  let dashboardUrl = '/dashboard';
  let afterSignOutUrl = '/';

  if (props.params.locale !== AppConfig.defaultLocale) {
    signInUrl = `/${props.params.locale}${signInUrl}`;
    signUpUrl = `/${props.params.locale}${signUpUrl}`;
    dashboardUrl = `/${props.params.locale}${dashboardUrl}`;
    afterSignOutUrl = `/${props.params.locale}${afterSignOutUrl}`;
  }

  return (
    <ClerkProvider
      localization={clerkLocale}
      signInUrl={signInUrl}
      signUpUrl={signUpUrl}
      afterSignInUrl={dashboardUrl}
      afterSignUpUrl={dashboardUrl}
      afterSignOutUrl={afterSignOutUrl}
    >
      <UserDataProvider>
        <CreateUserInSupabase />
        {props.children}
      </UserDataProvider>
    </ClerkProvider>
  );
}

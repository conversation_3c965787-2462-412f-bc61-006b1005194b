'use client';

import { CustomResetPassword } from '@/components/auth/CustomResetPassword';

export function ResetPasswordClient({ params }: { params: { locale: string } }) {
  // Debug log to check if the component is rendering
  console.log('Rendering Reset Password component');

  return (
    <div className="w-full max-w-md">
      <div className="mb-4 text-center text-red-500">
        {!process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY && 'Clerk publishable key is missing!'}
      </div>
      <CustomResetPassword 
        signInUrl={`/${params.locale}/sign-in`}
      />
    </div>
  );
}

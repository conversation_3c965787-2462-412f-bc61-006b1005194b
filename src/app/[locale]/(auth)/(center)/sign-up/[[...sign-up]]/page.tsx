import { getTranslations } from 'next-intl/server';

import { SignUpWrapper } from '@/components/auth/SignUpWrapper';
import { getI18nPath } from '@/utils/Helpers';

export async function generateMetadata(props: { params: { locale: string } }) {
  const t = await getTranslations({
    locale: props.params.locale,
    namespace: 'SignUp',
  });

  return {
    title: t('meta_title'),
    description: t('meta_description'),
  };
}

const SignUpPage = (props: { params: { locale: string } }) => {
  const dashboardPath = getI18nPath('/dashboard', props.params.locale);

  return (
    <div className="w-full">
      <div className="mb-8 text-center">
        <img
          src="/photo-de-profil-linkedin.png"
          alt="Guardiavision Logo"
          className="mx-auto h-16 w-auto"
        />
        <h1 className="mt-4 text-2xl font-bold text-white">Guardiavision</h1>
        <p className="mt-2 text-[#94a3b8]">Secure your visual content with AI-powered privacy protection</p>
      </div>

      <div className="relative overflow-hidden rounded-xl border border-[#1e3a6f] bg-[#0a192f]/80 p-1 shadow-xl backdrop-blur-sm">
        <div className="absolute inset-0 bg-gradient-to-r from-[#22C55E]/10 to-[#3b82f6]/10"></div>
        <SignUpWrapper
          path={getI18nPath('/sign-up', props.params.locale)}
          redirectUrl={dashboardPath}
          afterSignUpUrl={dashboardPath}
          signInUrl={getI18nPath('/sign-in', props.params.locale)}
        />
      </div>

      <div className="mt-6 text-center">
        <p className="text-sm text-[#94a3b8]">
          By signing up, you'll receive 50 free credits to try our AI-powered privacy protection service
        </p>
      </div>
    </div>
  );
};

export default SignUpPage;

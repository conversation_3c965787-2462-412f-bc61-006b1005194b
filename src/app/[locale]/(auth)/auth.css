/* Custom styles for Clerk authentication components */

/* Main container */
.cl-rootBox {
  width: 100%;
  max-width: 100%;
}

.cl-card {
  background-color: transparent !important;
  border: none !important;
  box-shadow: none !important;
}

/* Force all text to be light by default */
.cl-card * {
  color: #e2e8f0 !important;
}

/* Override specific elements that need different colors */
.cl-formButtonPrimary {
  color: white !important;
}

.cl-socialButtonsBlockButtonText {
  color: white !important;
}

.cl-formFieldAction,
.cl-formResendCodeLink,
.cl-formFieldActionLink,
.cl-formFieldHintLink,
.cl-alertButtonText,
.cl-formFieldRow__termsAndPrivacy a {
  color: #22C55E !important;
}

/* Form fields */
.cl-formButtonPrimary {
  background-color: #22C55E !important;
  transition: all 0.2s ease-in-out !important;
  box-shadow: 0 4px 6px rgba(34, 197, 94, 0.25) !important;
  font-weight: 600 !important;
}

.cl-formButtonPrimary:hover {
  background-color: #4ADE80 !important;
  transform: translateY(-1px) !important;
}

.cl-formFieldInput {
  background-color: rgba(17, 34, 64, 0.9) !important;
  border: 1px solid rgba(59, 130, 246, 0.5) !important;
  color: white !important;
  box-shadow: 0 0 10px rgba(59, 130, 246, 0.1) !important;
  transition: all 0.2s ease-in-out !important;
}

.cl-formFieldInput:focus {
  border-color: #22C55E !important;
  box-shadow: 0 0 0 2px rgba(34, 197, 94, 0.3) !important;
}

.cl-formFieldLabel {
  color: #e2e8f0 !important;
  font-weight: 500 !important;
}

/* Social buttons */
.cl-socialButtonsBlockButton {
  background-color: rgba(17, 34, 64, 0.9) !important;
  border: 1px solid rgba(59, 130, 246, 0.5) !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
  transition: all 0.2s ease-in-out !important;
  color: white !important;
}

.cl-socialButtonsBlockButton:hover {
  background-color: rgba(30, 58, 111, 0.9) !important;
  transform: translateY(-1px) !important;
}

/* Ensure Google button text is white */
.cl-socialButtonsBlockButtonText {
  color: white !important;
}

/* Dividers */
.cl-dividerText {
  color: #94a3b8 !important;
}

.cl-dividerLine {
  background-color: #1e3a6f !important;
}

/* Links */
.cl-footerActionLink,
.cl-formFieldAction,
.cl-headerSubtitle a {
  color: #22C55E !important;
  transition: color 0.2s ease-in-out !important;
}

.cl-footerActionLink:hover,
.cl-formFieldAction:hover,
.cl-headerSubtitle a:hover {
  color: #4ADE80 !important;
}

/* Headers */
.cl-headerTitle {
  color: white !important;
  font-weight: bold !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important;
}

.cl-headerSubtitle {
  color: #94a3b8 !important;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.1) !important;
}

/* Fix dark text on dark background issues */
.cl-formFieldInfoText,
.cl-formFieldSuccessText,
.cl-formFieldWarningText,
.cl-formFieldErrorText,
.cl-formResendCodeLink,
.cl-formFieldActionText,
.cl-formFieldHintText,
.cl-identityPreviewText,
.cl-identityPreviewEditButtonText,
.cl-otpCodeFieldInput,
.cl-alertText,
.cl-alertButtonText,
.cl-main p,
.cl-main span,
.cl-main div,
.cl-main label {
  color: #e2e8f0 !important;
}

/* Ensure links are visible */
.cl-formFieldAction,
.cl-formResendCodeLink,
.cl-formFieldActionLink,
.cl-formFieldHintLink,
.cl-alertButtonText {
  color: #22C55E !important;
}

/* Ensure checkboxes and radio buttons have visible text */
.cl-formCheckboxLabel,
.cl-formRadioLabel {
  color: #e2e8f0 !important;
}

/* Fix Terms of Service text */
.cl-formFieldRow__termsAndPrivacy {
  color: #e2e8f0 !important;
}

.cl-formFieldRow__termsAndPrivacy a {
  color: #22C55E !important;
}

/* Footer */
.cl-footerActionText {
  color: #94a3b8 !important;
}

/* Error messages */
.cl-alert {
  background-color: rgba(239, 68, 68, 0.1) !important;
  border-color: rgba(239, 68, 68, 0.3) !important;
}

.cl-alertText {
  color: #ef4444 !important;
}

/* Success messages */
.cl-success {
  background-color: rgba(34, 197, 94, 0.1) !important;
  border-color: rgba(34, 197, 94, 0.3) !important;
}

.cl-successText {
  color: #22C55E !important;
}

/* Password strength indicator */
#password-strength-bar {
  height: 6px;
  border-radius: 3px;
  transition: width 0.3s, background-color 0.3s;
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.cl-card {
  animation: fadeIn 0.5s ease-out;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .cl-card {
    padding: 1.5rem !important;
  }
}

/* Hide Clerk branding */
.cl-footer,
.cl-footerAction,
.cl-footerPages,
.cl-footerPageLink,
.cl-logoBox,
.cl-logoImage,
.cl-badge {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
  height: 0 !important;
  width: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
  overflow: hidden !important;
}

/* Hide "Secured by Clerk" text and any other Clerk branding */
.cl-internal-b3fm6y,
.cl-internal-wkkub3,
.cl-internal-1v71s2k,
.cl-internal-uyu30o,
.cl-internal-1xpzp24,
.cl-internal-1d87bz4,
.cl-internal-13zhnx0,
.cl-internal-1hdbmrz,
.cl-internal-m3fgp9,
.cl-internal-1bpxb4s,
.cl-internal-1ck0y9s {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
}

/* Additional selectors to hide Clerk branding */
.cl-internal-1xpzp24,  /* Common class for Clerk branding */
.cl-internal-wkkub3,   /* Clerk logo in footer */
.cl-internal-1d87bz4,  /* "Secured by Clerk" text */
.cl-internal-13zhnx0,  /* Clerk badge */
.cl-internal-1hdbmrz,  /* Clerk footer container */
.cl-internal-m3fgp9,   /* Clerk footer links */
.cl-internal-1bpxb4s,  /* Another Clerk footer class */
.cl-internal-1ck0y9s   /* Another Clerk branding class */
{
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
  height: 0 !important;
  width: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
  overflow: hidden !important;
}

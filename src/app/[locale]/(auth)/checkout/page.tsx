'use client';

import { useEffect, useState, Suspense } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { useUser } from '@clerk/nextjs';

function CheckoutContent() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const { user, isSignedIn } = useUser();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const plan = searchParams.get('plan');
  const priceId = searchParams.get('priceId');

  useEffect(() => {
    if (!isSignedIn) {
      // Redirect to sign-in if not authenticated
      router.push('/sign-in');
      return;
    }

    if (!plan || !priceId) {
      // Redirect to pricing if no plan selected
      router.push('/pricing');
      return;
    }

    // Auto-initiate checkout
    handleCheckout();
  }, [isSignedIn, plan, priceId]);

  const handleCheckout = async () => {
    if (!priceId || !plan) return;

    setLoading(true);
    setError(null);

    try {
      // Create Stripe checkout session
      const response = await fetch('/api/stripe/create-checkout-session', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          priceId,
          planType: plan.toUpperCase(),
          isYearly: false, // For now, only monthly
        }),
      });

      const data = await response.json();

      if (data.error) {
        throw new Error(data.error);
      }

      // Redirect to Stripe Checkout
      const stripe = await import('@stripe/stripe-js').then(mod =>
        mod.loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!)
      );

      if (stripe) {
        const { error } = await stripe.redirectToCheckout({
          sessionId: data.sessionId
        });
        if (error) {
          throw new Error(error.message);
        }
      }
    } catch (error) {
      console.error('Error:', error);
      setError(error instanceof Error ? error.message : 'Something went wrong');
    } finally {
      setLoading(false);
    }
  };

  if (!isSignedIn) {
    return (
      <div className="min-h-screen bg-navy flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-400 mx-auto"></div>
          <p className="text-white mt-4">Redirecting to sign in...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-navy flex items-center justify-center">
        <div className="max-w-md mx-auto text-center">
          <div className="bg-red-500/10 border border-red-500 rounded-lg p-6">
            <h2 className="text-xl font-bold text-red-400 mb-2">Checkout Error</h2>
            <p className="text-red-300 mb-4">{error}</p>
            <div className="space-y-2">
              <button
                onClick={handleCheckout}
                className="w-full bg-green-500 hover:bg-green-600 text-white font-medium py-2 px-4 rounded-lg transition-colors"
              >
                Try Again
              </button>
              <button
                onClick={() => router.push('/pricing')}
                className="w-full bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
              >
                Back to Pricing
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-navy flex items-center justify-center">
      <div className="max-w-md mx-auto text-center">
        <div className="bg-navy-light border border-navy-light rounded-lg p-8">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-400 mx-auto mb-4"></div>
          <h2 className="text-xl font-bold text-white mb-2">Setting up your checkout...</h2>
          <p className="text-gray-300 mb-4">
            Preparing your {plan} plan subscription. You'll be redirected to Stripe checkout shortly.
          </p>
          <div className="text-sm text-gray-400">
            Plan: <span className="text-green-400 font-medium capitalize">{plan}</span>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function CheckoutPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-navy flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-400 mx-auto"></div>
          <p className="text-white mt-4">Loading checkout...</p>
        </div>
      </div>
    }>
      <CheckoutContent />
    </Suspense>
  );
}

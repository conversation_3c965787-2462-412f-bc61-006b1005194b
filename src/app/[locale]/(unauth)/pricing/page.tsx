import { unstable_setRequestLocale } from 'next-intl/server';
import { PricingSection } from '@/components/pricing/PricingSection';
import { InteractiveNavbar } from '@/components/landing/InteractiveNavbar';
import { ScrollAnimations } from '@/components/landing/ScrollAnimations';
import { Footer } from '@/templates/Footer';

interface PricingPageProps {
  params: {
    locale: string;
  };
}



export default function PricingPage({ params }: PricingPageProps) {
  const locale = params.locale || 'en';

  // Enable static rendering
  unstable_setRequestLocale(locale);

  return (
    <div className="min-h-screen bg-navy text-white">
      {/* Add scroll animations */}
      <ScrollAnimations />

      {/* Navbar */}
      <InteractiveNavbar locale={locale} />

      {/* Hero Section */}
      <div className="relative overflow-hidden bg-navy">
        <div className="container relative mx-auto px-4 py-12 sm:px-6 lg:px-8 lg:py-16">
          <div className="mx-auto max-w-3xl text-center">
            <h1 className="scroll-animate text-4xl font-bold tracking-tight text-white sm:text-6xl">
              Choose Your Plan
            </h1>
            <p className="scroll-animate delay-100 mt-6 text-xl text-gray-300">
              Secure your visual content with AI-powered privacy protection. Start free, upgrade anytime.
            </p>
          </div>
        </div>
      </div>

      {/* Pricing Section */}
      <PricingSection />

      {/* Footer */}
      <Footer />
    </div>
  );
}

export const metadata = {
  title: 'Pricing - Guardiavision',
  description: 'Choose the perfect plan for your AI-powered privacy protection needs. Start free, upgrade anytime.',
};

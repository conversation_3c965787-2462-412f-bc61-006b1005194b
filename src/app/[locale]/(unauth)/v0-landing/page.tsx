import { getTranslations, unstable_setRequestLocale } from 'next-intl/server';

import { Hero } from '@/components/v0/hero';
import { Demo } from '@/components/v0/demo';
import { Features } from '@/components/v0/features';
import { Navbar } from '@/components/v0/navbar';
import { Footer } from '@/components/v0/footer';
import { Pricing } from '@/templates/Pricing';
import { FAQ } from '@/templates/FAQ';
import { CTA } from '@/templates/CTA';

export async function generateMetadata(props: { params: { locale: string } }) {
  const t = await getTranslations({
    locale: props.params.locale,
    namespace: 'Index',
  });

  return {
    title: t('meta_title'),
    description: t('meta_description'),
  };
}

const V0LandingPage = (props: { params: { locale: string } }) => {
  unstable_setRequestLocale(props.params.locale);

  return (
    <div className="min-h-screen bg-navy text-white">
      <Navbar />
      <Hero />
      <Demo />
      <Features />
      <Pricing />
      <FAQ />
      <CTA />
      <Footer />
    </div>
  );
};

export default V0LandingPage;

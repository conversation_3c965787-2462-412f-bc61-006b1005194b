import { Code, Layers, Lock, Settings, Zap } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import { unstable_setRequestLocale } from 'next-intl/server';

// Import components
import { BasicImageCompare } from '@/components/BasicImageCompare';
import { ShieldCheck } from '@/components/icons/ShieldCheck';
import { UploadRedirect } from '@/components/landing/UploadRedirect';
import { InteractiveNavbar } from '@/components/landing/InteractiveNavbar';
import { ScrollAnimations } from '@/components/landing/ScrollAnimations';
import { PricingSection } from '@/components/pricing/PricingSection';
import { EnglishCTA } from '@/features/landing/EnglishCTA';
import { Footer } from '@/templates/Footer';

interface IndexPageProps {
  params: {
    locale: string;
  };
}



export default function IndexPage({ params }: IndexPageProps) {
  const locale = params.locale || 'en';

  // Enable static rendering
  unstable_setRequestLocale(locale);

  return (
    <div className="min-h-screen bg-navy text-white">
      {/* Add scroll animations */}
      <ScrollAnimations />

      {/* Navbar */}
      <InteractiveNavbar locale={locale} />

      {/* Hero Section */}
      <div className="relative overflow-hidden bg-navy">
        <div className="container relative mx-auto px-4 py-12 sm:px-6 lg:px-8 lg:py-16">
          <div className="grid grid-cols-1 gap-12 lg:grid-cols-2 lg:gap-8">
            <div className="flex flex-col justify-center">
              <div className="mb-6 inline-flex items-center rounded-full border border-navy-light bg-navy-dark px-3 py-1 text-sm text-gray-300">
                <ShieldCheck className="mr-1 size-3.5 text-green-400" />
                <span>Privacy-first content protection</span>
              </div>

              <h1 className="mb-6 text-4xl font-extrabold tracking-tight text-white sm:text-5xl md:text-6xl">
                <span className="block">Blur ANYTHING</span>
                <span className="block bg-gradient-to-r from-blue-400 to-green-400 bg-clip-text text-transparent">
                  in photos and videos with AI in 3 seconds with a simple description !
                </span>
              </h1>

              <p className="mb-8 max-w-2xl text-xl text-gray-300">
                No software installation needed, use it right on the web.
              </p>

              <div className="flex flex-col space-y-4 sm:flex-row sm:space-x-6 sm:space-y-0">
                <a
                  href="https://youtube.com/shorts/mvF6Vd01yks"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="blue-button-glow inline-flex w-full items-center justify-center rounded-lg border-2 border-blue-400 px-6 py-3.5 text-lg font-bold text-blue-400 transition-all duration-300 hover:border-transparent hover:bg-blue-500 hover:text-white sm:w-1/3"
                >
                  <svg className="mr-2 size-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                  </svg>
                  Watch Demo
                </a>

                <div className="group relative sm:w-2/3">
                  <div className="absolute -inset-1 rounded-lg bg-gradient-to-r from-green-400 to-green-600 opacity-70 blur-md transition-opacity duration-300 group-hover:opacity-100"></div>
                  <div className="relative">
                    <div className="rainbow-border !rounded-lg"></div>
                    <Link
                      href={`/${locale}/sign-up`}
                      className="relative z-10 inline-flex w-full items-center justify-center rounded-lg bg-green-400 px-8 py-4 text-lg font-bold text-navy shadow-lg transition-all duration-300 hover:bg-green-500 hover:shadow-green-400/50 group-hover:scale-105"
                    >
                      <ShieldCheck className="mr-2 size-5" />
                      Start For Free Now
                    </Link>
                  </div>
                </div>
              </div>

              <div className="mt-8 flex items-center text-sm text-gray-400">
                <Lock className="mr-2 size-4 text-green-400" />
                <span>No credit card required • Cancel anytime</span>
              </div>
            </div>

            <div className="flex items-center justify-center">
              <div className="demo-box relative">
                <div className="absolute -inset-1 rounded-xl bg-gradient-to-r from-blue-400 via-purple-400 to-green-400 opacity-80 blur-lg"></div>
                <div className="relative w-[300px] overflow-hidden rounded-lg border border-navy-light bg-navy-dark shadow-2xl">
                  <div className="flex items-center justify-between border-b border-navy-light bg-navy p-2">
                    <div className="flex space-x-1.5">
                      <div className="size-3 rounded-full bg-red-500"></div>
                      <div className="size-3 rounded-full bg-yellow-500"></div>
                      <div className="size-3 rounded-full bg-green-500"></div>
                    </div>
                    <div className="text-sm font-medium text-blue-400">Guardiavision Demo</div>
                    <div className="w-16"></div>
                  </div>
                  <div className="p-0">
                    <div className="flex justify-center">
                      <div className="h-[500px] w-full overflow-hidden rounded-none bg-gray-800">
                        <a
                          href="https://youtube.com/shorts/mvF6Vd01yks"
                          target="_blank"
                          rel="noopener noreferrer"
                          className="group relative flex size-full cursor-pointer items-center justify-center"
                        >
                          <div className="absolute inset-0 bg-black bg-opacity-50"></div>
                          <div className="relative z-10 rounded-full bg-blue-500 bg-opacity-80 p-6 shadow-lg transition-transform group-hover:scale-110">
                            <svg className="size-12 text-white" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                            </svg>
                          </div>
                          <img
                            src="https://img.youtube.com/vi/mvF6Vd01yks/maxresdefault.jpg"
                            alt="Video Thumbnail"
                            className="absolute inset-0 size-full object-cover"
                          />
                        </a>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Demo Section */}
      <section id="demo" className="bg-navy-dark py-24">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="mx-auto max-w-3xl text-center">
            <h2 className="scroll-animate text-3xl font-bold tracking-tight text-white sm:text-4xl">See Guardia<span className="vision-text">Vision</span> in Action</h2>
            <p className="scroll-animate delay-100 mt-4 text-lg text-gray-300">
              Drag the slider to reveal how our AI precisely blurs sensitive content while preserving the integrity of
              your media.
            </p>
          </div>

          <div className="mt-12 scroll-animate delay-200">
            <BasicImageCompare
              imageSets={[
                {
                  id: 'people',
                  name: 'People',
                  originalImage: '/images/people.jpg',
                  blurredImage: '/images/people_processed.jpg',
                  prompt: 'blur all peoples faces',
                },
                {
                  id: 'car',
                  name: 'Vehicle',
                  originalImage: '/images/car.jpg',
                  blurredImage: '/images/car_processed.jpg',
                  prompt: 'pixelate license plate',
                },
                {
                  id: 'computer',
                  name: 'Computer',
                  originalImage: '/images/computer.jpg',
                  blurredImage: '/images/computer_processed.jpg',
                  prompt: 'blur sensitive data on the screen',
                },
              ]}
              defaultImageSet="people"
            />
          </div>

          {/* Upload Your Own Image Section */}
          <div className="mt-20">
            <div className="mx-auto max-w-3xl text-center">
              <h2 className="scroll-animate text-3xl font-bold tracking-tight text-white sm:text-4xl">Try It With Your Own Media</h2>
              <p className="scroll-animate delay-100 mt-4 text-lg text-gray-300">
                Tell us what to blur, then upload your photos or videos to see how our AI can protect your sensitive content.
              </p>
            </div>

            <div className="scroll-animate delay-200">
              <UploadRedirect signUpUrl={`/${locale}/sign-up`} />
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-24">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="mx-auto max-w-3xl text-center">
            <h2 className="scroll-animate text-3xl font-bold tracking-tight text-white sm:text-4xl">
              Advanced Privacy Protection Features
            </h2>
            <p className="scroll-animate delay-100 mt-4 text-lg text-gray-300">
              Our AI-powered platform offers comprehensive tools to protect sensitive information in your visual content.
            </p>
          </div>

          <div className="mt-16 grid gap-8 md:grid-cols-2 lg:grid-cols-3">
            {[
              {
                icon: <Settings className="size-6 text-green-400" />,
                title: 'Customizable Blur',
                description: 'Specify exactly what you want to blur or remove: faces, license plates, text, or entire people.',
              },
              {
                icon: <Zap className="size-6 text-green-400" />,
                title: 'Real-Time Processing',
                description: 'Process videos and images in real-time with minimal latency, perfect for live streaming.',
              },
              {
                icon: <ShieldCheck className="size-6 text-green-400" />,
                title: 'AI Precision',
                description: 'Our advanced AI ensures accurate detection and blurring of sensitive content with 99.8% accuracy.',
              },
              {
                icon: <Code className="size-6 text-green-400" />,
                title: 'Developer API',
                description: 'Integrate our privacy protection directly into your applications with our simple REST API.',
              },
              {
                icon: <Layers className="size-6 text-green-400" />,
                title: 'Batch Processing',
                description: 'Upload and process thousands of files at once with our efficient batch processing system.',
              },
              {
                icon: <Lock className="size-6 text-green-400" />,
                title: 'Privacy Guaranteed',
                description: 'Your content is processed securely and never stored or used for training our AI models.',
              },
            ].map((feature, index) => (
              <div
                key={index}
                className={`scroll-animate delay-${index * 100} rounded-lg border border-navy-light bg-navy-dark p-6 text-white`}
              >
                <div className="mb-4 inline-flex size-12 items-center justify-center rounded-lg bg-navy">
                  {feature.icon}
                </div>
                <h3 className="mb-2 text-xl font-semibold">{feature.title}</h3>
                <p className="text-gray-300">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <PricingSection />

      {/* Use existing components for the rest */}
      <div className="bg-navy">
        <EnglishCTA />
        <Footer />
      </div>

    </div>
  );
}

"use client"

import Head from 'next/head'

export default function FaviconTestPage() {
  return (
    <>
      <Head>
        <link rel="icon" href="/favicon.ico?v=4" />
        <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png?v=4" />
        <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png?v=4" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png?v=4" />
        <title>Favicon Test</title>
      </Head>
      <div className="container mx-auto p-8">
        <h1 className="text-3xl font-bold mb-4">Favicon Test Page</h1>
        <p className="mb-4">This page is testing if the favicon is working correctly.</p>
        <p>You should see the favicon in the browser tab.</p>
      </div>
    </>
  )
}

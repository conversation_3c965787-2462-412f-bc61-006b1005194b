'use client';

import Link from 'next/link';

export default function LegalIndex() {
  return (
    <div className="container mx-auto px-4 py-12 max-w-4xl">
      <h1 className="text-3xl font-bold mb-8 text-center">Legal Information</h1>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-xl font-semibold mb-4">Terms of Service</h2>
          <p className="text-gray-600 mb-4">
            Our Terms of Service outline the rules and guidelines for using Guardiavision's platform and services. By using our service, you agree to these terms.
          </p>
          <Link
            href="/legal/terms"
            className="inline-block bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition"
          >
            Read Terms of Service
          </Link>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-xl font-semibold mb-4">Privacy Policy</h2>
          <p className="text-gray-600 mb-4">
            Our Privacy Policy explains how we collect, use, and protect your personal information when you use our platform and services.
          </p>
          <Link
            href="/legal/privacy"
            className="inline-block bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition"
          >
            Read Privacy Policy
          </Link>
        </div>
      </div>

      <div className="mt-12 bg-gray-100 p-6 rounded-lg">
        <h2 className="text-xl font-semibold mb-4">About Our Company</h2>
        <p className="text-gray-600 mb-4">
          <strong>Guardiavision OÜ is registered in Estonia as a startup company</strong> specializing in AI-powered privacy protection solutions for images and videos.
        </p>
        <p className="text-gray-600 mb-4">
          <strong>Registration Number:</strong> [Registration Number]<br />
          <strong>Registered Address:</strong> [Estonian Address]<br />
          <strong>Email:</strong> <EMAIL>
        </p>
      </div>

      <div className="mt-6 bg-yellow-50 p-6 rounded-lg border-l-4 border-yellow-400">
        <h2 className="text-xl font-semibold mb-4">Important Service Disclaimer</h2>
        <p className="text-gray-700 mb-4">
          <strong>While we strive to provide high-quality anonymization and privacy protection, we cannot guarantee that our Service will be 100% effective in all cases.</strong>
        </p>
        <p className="text-gray-700">
          The effectiveness of our technology may vary depending on the quality, resolution, and complexity of the content being processed. Users are responsible for verifying the results before using processed content for sensitive purposes.
        </p>
      </div>
    </div>
  );
}

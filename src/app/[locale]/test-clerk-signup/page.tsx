'use client';

import { useState, useEffect } from 'react';
import { useSignUp } from '@clerk/nextjs';
import { redirect } from 'next/navigation';

// Force dynamic rendering to avoid static generation errors
export const dynamic = 'force-dynamic';

// This ensures the page is never statically generated
export const runtime = 'edge';

// Check if test pages are disabled
const isTestPagesDisabled = process.env.NEXT_PUBLIC_DISABLE_TEST_PAGES === 'true';

export default function TestClerkSignup() {
  // Use state to track if the component is mounted
  const [isMounted, setIsMounted] = useState(false);

  // Only use Clerk hooks after component is mounted on the client
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Redirect to home page if test pages are disabled
  if (typeof window !== 'undefined' && isTestPagesDisabled) {
    redirect('/');
  }

  // Initialize states
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [result, setResult] = useState<any>(null);
  const [verifying, setVerifying] = useState(false);
  const [code, setCode] = useState('');

  // Only access Clerk hooks when component is mounted on client
  const { isLoaded, signUp } = isMounted ? useSignUp() : { isLoaded: false, signUp: null };

  const handleSignup = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!isLoaded || !signUp) {
      setError('Authentication system is not ready yet. Please try again in a moment.');
      return;
    }

    try {
      setLoading(true);
      setError('');
      setResult(null);

      // Create the signup
      const response = await signUp.create({
        emailAddress: email,
        password,
        firstName,
        lastName,
      });

      console.log('Signup created:', response);

      // Prepare email verification
      const prepareResponse = await signUp.prepareEmailAddressVerification({
        strategy: 'email_code',
      });

      console.log('Verification prepared:', prepareResponse);

      setResult({
        status: 'verification_needed',
        signUpId: signUp.id,
      });

      setVerifying(true);
    } catch (err: any) {
      console.error('Signup error:', err);
      setError(err.errors?.[0]?.message || err.message || 'Unknown error');
      setResult({
        status: 'error',
        error: err.errors || err.message,
      });
    } finally {
      setLoading(false);
    }
  };

  const handleVerify = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!isLoaded || !signUp) {
      setError('Authentication system is not ready yet. Please try again in a moment.');
      return;
    }

    try {
      setLoading(true);
      setError('');

      // Attempt verification
      const response = await signUp.attemptEmailAddressVerification({
        code,
      });

      console.log('Verification response:', response);

      setResult({
        status: response.status,
        response,
      });
    } catch (err: any) {
      console.error('Verification error:', err);
      setError(err.errors?.[0]?.message || err.message || 'Unknown error');
      setResult({
        status: 'verification_error',
        error: err.errors || err.message,
      });
    } finally {
      setLoading(false);
    }
  };

  // Show loading state if not mounted or Clerk is not loaded
  if (!isMounted || !isLoaded) {
    return <div className="p-8 max-w-md mx-auto">
      <h1 className="text-2xl font-bold mb-6">Test Clerk Signup</h1>
      <div className="bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded mb-4">
        Loading authentication system...
      </div>
    </div>;
  }

  return (
    <div className="p-8 max-w-md mx-auto">
      <h1 className="text-2xl font-bold mb-6">Test Clerk Signup</h1>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      {result && (
        <div className="bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded mb-4">
          <pre className="whitespace-pre-wrap">{JSON.stringify(result, null, 2)}</pre>
        </div>
      )}

      {!verifying ? (
        <form onSubmit={handleSignup} className="space-y-4">
          <div>
            <label className="block mb-1">Email</label>
            <input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="w-full p-2 border rounded"
              required
            />
          </div>

          <div>
            <label className="block mb-1">Password</label>
            <input
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="w-full p-2 border rounded"
              required
            />
          </div>

          <div>
            <label className="block mb-1">First Name</label>
            <input
              type="text"
              value={firstName}
              onChange={(e) => setFirstName(e.target.value)}
              className="w-full p-2 border rounded"
              required
            />
          </div>

          <div>
            <label className="block mb-1">Last Name</label>
            <input
              type="text"
              value={lastName}
              onChange={(e) => setLastName(e.target.value)}
              className="w-full p-2 border rounded"
              required
            />
          </div>

          <button
            type="submit"
            disabled={loading}
            className="w-full bg-blue-500 text-white p-2 rounded hover:bg-blue-600 disabled:bg-blue-300"
          >
            {loading ? 'Creating...' : 'Create Account'}
          </button>
        </form>
      ) : (
        <form onSubmit={handleVerify} className="space-y-4">
          <div>
            <label className="block mb-1">Verification Code</label>
            <input
              type="text"
              value={code}
              onChange={(e) => setCode(e.target.value)}
              className="w-full p-2 border rounded"
              required
            />
          </div>

          <button
            type="submit"
            disabled={loading}
            className="w-full bg-green-500 text-white p-2 rounded hover:bg-green-600 disabled:bg-green-300"
          >
            {loading ? 'Verifying...' : 'Verify Email'}
          </button>

          <button
            type="button"
            onClick={() => setVerifying(false)}
            className="w-full bg-gray-500 text-white p-2 rounded hover:bg-gray-600"
          >
            Back to Signup
          </button>
        </form>
      )}
    </div>
  );
}

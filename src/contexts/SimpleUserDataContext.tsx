'use client';

import { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { createClient } from '@supabase/supabase-js';
import { useUser } from '@clerk/nextjs';

interface UserDataContextType {
  credits: number;
  subscriptionType: string;
  isLoading: boolean;
}

const SimpleUserDataContext = createContext<UserDataContextType>({
  credits: 60,
  subscriptionType: 'Free',
  isLoading: false
});

// Create a direct Supabase client without Clerk integration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';

const supabase = createClient(supabaseUrl, supabaseKey);

export function SimpleUserDataProvider({ children }: { children: ReactNode }) {
  const { user } = useUser();
  const [credits, setCredits] = useState<number>(60);
  const [subscriptionType, setSubscriptionType] = useState<string>('Free');
  const [isLoading, setIsLoading] = useState(false);

  // Fetch user data on component mount
  useEffect(() => {
    // Skip if user is undefined
    if (!user) {
      console.log('Simple provider: user is undefined, skipping fetch');
      setIsLoading(false);
      return;
    }

    const userId = user.id;

    async function fetchUserData() {
      setIsLoading(true);

      try {
        console.log('Simple provider fetching data for user:', userId);

        // Check if user exists in the database
        const { data, error } = await supabase
          .from('users')
          .select('credits, subscription_type')
          .eq('id', userId)
          .single();

        console.log('Simple provider fetch result:', { data, error });

        if (!error && data) {
          setCredits(data.credits);
          setSubscriptionType(data.subscription_type);
        } else {
          // If error or no data, keep default values
          console.log('Simple provider: Using default values');
        }
      } catch (err) {
        console.error('Error in simple provider:', err);
      } finally {
        setIsLoading(false);
      }
    }

    fetchUserData();
  }, [user]);

  console.log('Simple provider rendering with:', { credits, subscriptionType, isLoading });

  return (
    <SimpleUserDataContext.Provider value={{ credits, subscriptionType, isLoading }}>
      {children}
    </SimpleUserDataContext.Provider>
  );
}

export function useSimpleUserData() {
  return useContext(SimpleUserDataContext);
}

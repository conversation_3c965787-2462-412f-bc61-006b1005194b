'use client';

import { createContext, useContext, useState, useEffect, useCallback, ReactNode } from 'react';
import { useUser } from '@clerk/nextjs';
import { useSupabaseClient } from '@/utils/supabase/client';

interface UserDataContextType {
  credits: number | null;
  subscriptionType: string | null;
  isLoading: boolean;
  refreshUserData: () => Promise<void>;
}

const UserDataContext = createContext<UserDataContextType | undefined>(undefined);

export function UserDataProvider({ children }: { children: ReactNode }) {
  const { user } = useUser();
  const supabase = useSupabaseClient();
  const [credits, setCredits] = useState<number | null>(null);
  const [subscriptionType, setSubscriptionType] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const refreshUserData = useCallback(async () => {
    // Skip if user is undefined
    if (!user) {
      console.log('UserDataContext: user is undefined, skipping refresh');
      setIsLoading(false);
      return;
    }

    setIsLoading(true);

    try {
      console.log('Refreshing user data for ID:', user.id);

      // Check if user exists in the database
      const { data: existingUser, error: checkError } = await supabase
        .from('users')
        .select('id, credits, subscription_type')
        .eq('id', user.id)
        .single();

      console.log('User data refresh result:', { existingUser, checkError });

      if (checkError || !existingUser) {
        console.log('User not found in database, creating new user record');

        try {
          // User doesn't exist, create a new record
          const { data: newUser, error: insertError } = await supabase
            .from('users')
            .insert([
              {
                id: user.id,
                email: user.emailAddresses[0]?.emailAddress || '',
                username: user.username || '',
                first_name: user.firstName || '',
                last_name: user.lastName || '',
                avatar_url: user.imageUrl || '',
                credits: 60,
                subscription_type: 'Free'
              }
            ])
            .select()
            .single();

          console.log('New user creation result:', { newUser, insertError });

          if (!insertError && newUser) {
            setCredits(newUser.credits);
            setSubscriptionType(newUser.subscription_type);
          } else {
            // If insert fails, set default values
            console.log('Insert failed, using default values');
            setCredits(60);
            setSubscriptionType('Free');
          }
        } catch (insertErr) {
          console.error('Error creating new user:', insertErr);
          // Set default values
          setCredits(60);
          setSubscriptionType('Free');
        }
      } else {
        // User exists, use their data
        console.log('User found, setting credits to:', existingUser.credits);
        setCredits(existingUser.credits);
        setSubscriptionType(existingUser.subscription_type);
      }
    } catch (err) {
      console.error('Error refreshing user data:', err);
      // Set default values in case of error
      setCredits(60);
      setSubscriptionType('Free');
    } finally {
      setIsLoading(false);
    }
  }, [user, supabase]);

  // Fetch user data on component mount
  useEffect(() => {
    if (user) {
      refreshUserData();
    }
  }, [user, refreshUserData]);

  // Log the current state before rendering
  console.log('UserDataProvider rendering with state:', {
    credits,
    subscriptionType,
    isLoading,
    userPresent: !!user
  });

  return (
    <UserDataContext.Provider value={{ credits, subscriptionType, isLoading, refreshUserData }}>
      {children}
    </UserDataContext.Provider>
  );
}

export function useUserData() {
  const context = useContext(UserDataContext);

  // Add debugging
  console.log('useUserData hook called, context:', context);

  if (context === undefined) {
    console.error('UserDataContext is undefined - not wrapped in provider');
    throw new Error('useUserData must be used within a UserDataProvider');
  }

  return context;
}

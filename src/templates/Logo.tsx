import Image from 'next/image';
import { AppConfig } from '@/utils/AppConfig';

export const Logo = (props: {
  isTextHidden?: boolean;
}) => (
  <div className="flex items-center text-3xl font-semibold">
    <div className="relative overflow-hidden rounded-full mr-4">
      <Image
        src="/photo-de-profil-linkedin.png"
        alt="Guardiavision Logo"
        width={80}
        height={80}
        className="object-cover"
        style={{ width: '80px', height: '80px' }}
      />
    </div>
    {!props.isTextHidden && AppConfig.name}
  </div>
);

'use client';

import { useState } from 'react';

interface User {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  created_at: string;
  subscription_type: string;
}

interface UserCheckResult {
  userExists: boolean;
  user: User | null;
  relatedRecords: {
    tasks: number;
    testItems: number;
    processedImages: number;
    processedVideos: number;
    creditTransactions: number;
  };
}

export function UserDeletionTest() {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);
  const [checkResult, setCheckResult] = useState<UserCheckResult | null>(null);
  const [message, setMessage] = useState('');
  const [userIdToCheck, setUserIdToCheck] = useState('');

  const fetchUsers = async () => {
    setLoading(true);
    setMessage('');

    try {
      const response = await fetch('/api/admin/test-user-deletion');
      const data = await response.json();

      if (data.success) {
        setUsers(data.users);
        setMessage(`✅ Found ${data.total} users in Supabase`);
      } else {
        setMessage(`❌ Error: ${data.error}`);
      }
    } catch (error) {
      setMessage(`❌ Error: ${error instanceof Error ? error.message : 'Something went wrong'}`);
    } finally {
      setLoading(false);
    }
  };

  const checkUser = async (userId: string) => {
    setLoading(true);
    setMessage('');
    setCheckResult(null);

    try {
      const response = await fetch('/api/admin/test-user-deletion', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ userId, action: 'check' }),
      });

      const data = await response.json();

      if (data.success) {
        setCheckResult(data);
        if (data.userExists) {
          setMessage(`✅ User ${userId} exists in Supabase`);
        } else {
          setMessage(`❌ User ${userId} NOT found in Supabase`);
        }
      } else {
        setMessage(`❌ Error: ${data.error}`);
      }
    } catch (error) {
      setMessage(`❌ Error: ${error instanceof Error ? error.message : 'Something went wrong'}`);
    } finally {
      setLoading(false);
    }
  };

  const deleteUser = async (userId: string) => {
    if (!confirm(`Are you sure you want to delete user ${userId} from Supabase? This action cannot be undone.`)) {
      return;
    }

    setLoading(true);
    setMessage('');

    try {
      const response = await fetch('/api/admin/test-user-deletion', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ userId, action: 'delete' }),
      });

      const data = await response.json();

      if (data.success) {
        setMessage(`✅ ${data.message}`);
        // Refresh users list
        await fetchUsers();
        // Clear check result if it was for the deleted user
        if (checkResult?.user?.id === userId) {
          setCheckResult(null);
        }
      } else {
        setMessage(`❌ Error: ${data.error}`);
      }
    } catch (error) {
      setMessage(`❌ Error: ${error instanceof Error ? error.message : 'Something went wrong'}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="bg-navy-light rounded-lg p-6 border border-navy-light">
      <h3 className="text-xl font-bold text-white mb-4">User Deletion Test</h3>
      <p className="text-gray-300 mb-6">
        Test user deletion functionality and check if Clerk webhook is properly deleting users from Supabase.
      </p>

      {/* Fetch Users */}
      <div className="mb-6">
        <button
          onClick={fetchUsers}
          disabled={loading}
          className={`bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors ${
            loading ? 'opacity-50 cursor-not-allowed' : ''
          }`}
        >
          {loading ? 'Loading...' : 'Fetch All Users'}
        </button>
      </div>

      {/* Check Specific User */}
      <div className="mb-6 p-4 bg-purple-500/10 border border-purple-500 rounded-lg">
        <h4 className="text-purple-400 font-semibold mb-3">Check Specific User</h4>
        <div className="flex gap-3">
          <input
            type="text"
            value={userIdToCheck}
            onChange={(e) => setUserIdToCheck(e.target.value)}
            placeholder="Enter user ID..."
            className="flex-1 bg-navy border border-gray-600 rounded-lg px-3 py-2 text-white placeholder-gray-400"
          />
          <button
            onClick={() => checkUser(userIdToCheck)}
            disabled={loading || !userIdToCheck.trim()}
            className={`bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg transition-colors ${
              loading || !userIdToCheck.trim() ? 'opacity-50 cursor-not-allowed' : ''
            }`}
          >
            {loading ? 'Checking...' : 'Check User'}
          </button>
        </div>
      </div>

      {/* Check Result */}
      {checkResult && (
        <div className="mb-6 p-4 bg-navy rounded-lg">
          <h4 className="text-white font-semibold mb-3">User Check Result:</h4>
          {checkResult.userExists ? (
            <div className="space-y-2">
              <p className="text-green-400">✅ User exists in Supabase</p>
              <div className="text-sm text-gray-300">
                <p><strong>Email:</strong> {checkResult.user?.email}</p>
                <p><strong>Name:</strong> {checkResult.user?.first_name} {checkResult.user?.last_name}</p>
                <p><strong>Plan:</strong> {checkResult.user?.subscription_type}</p>
                <p><strong>Created:</strong> {new Date(checkResult.user?.created_at || '').toLocaleString()}</p>
              </div>
              <div className="mt-3">
                <p className="text-white font-semibold">Related Records:</p>
                <div className="grid grid-cols-2 gap-2 text-sm text-gray-300">
                  <p>Tasks: {checkResult.relatedRecords.tasks}</p>
                  <p>Test Items: {checkResult.relatedRecords.testItems}</p>
                  <p>Images: {checkResult.relatedRecords.processedImages}</p>
                  <p>Videos: {checkResult.relatedRecords.processedVideos}</p>
                  <p>Transactions: {checkResult.relatedRecords.creditTransactions}</p>
                </div>
              </div>
              <button
                onClick={() => deleteUser(checkResult.user!.id)}
                disabled={loading}
                className={`mt-3 bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg transition-colors ${
                  loading ? 'opacity-50 cursor-not-allowed' : ''
                }`}
              >
                {loading ? 'Deleting...' : 'Delete User'}
              </button>
            </div>
          ) : (
            <p className="text-red-400">❌ User not found in Supabase</p>
          )}
        </div>
      )}

      {/* Message Display */}
      {message && (
        <div className={`p-4 rounded-lg mb-6 ${
          message.includes('✅') ? 'bg-green-500/10 border border-green-500 text-green-400' :
          message.includes('❌') ? 'bg-red-500/10 border border-red-500 text-red-400' :
          'bg-blue-500/10 border border-blue-500 text-blue-400'
        }`}>
          <p className="text-sm">{message}</p>
        </div>
      )}

      {/* Users List */}
      {users.length > 0 && (
        <div className="space-y-4">
          <h4 className="text-lg font-semibold text-white">Users in Supabase ({users.length}):</h4>
          
          <div className="max-h-96 overflow-y-auto space-y-2">
            {users.map((user) => (
              <div key={user.id} className="bg-navy rounded-lg p-4 border border-navy-light">
                <div className="flex justify-between items-start">
                  <div>
                    <h5 className="text-white font-semibold">{user.first_name} {user.last_name}</h5>
                    <p className="text-gray-400 text-sm">{user.email}</p>
                    <p className="text-gray-400 text-xs">Plan: {user.subscription_type}</p>
                    <p className="text-gray-400 text-xs">Created: {new Date(user.created_at).toLocaleString()}</p>
                  </div>
                  <div className="flex gap-2">
                    <button
                      onClick={() => checkUser(user.id)}
                      disabled={loading}
                      className={`text-xs bg-blue-500 hover:bg-blue-600 text-white px-2 py-1 rounded transition-colors ${
                        loading ? 'opacity-50 cursor-not-allowed' : ''
                      }`}
                    >
                      Check
                    </button>
                    <button
                      onClick={() => deleteUser(user.id)}
                      disabled={loading}
                      className={`text-xs bg-red-500 hover:bg-red-600 text-white px-2 py-1 rounded transition-colors ${
                        loading ? 'opacity-50 cursor-not-allowed' : ''
                      }`}
                    >
                      Delete
                    </button>
                  </div>
                </div>
                <code className="text-xs bg-navy-light px-2 py-1 rounded text-gray-300 mt-2 block">
                  {user.id}
                </code>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Instructions */}
      <div className="mt-6 p-4 bg-navy rounded-lg">
        <h4 className="text-white font-semibold mb-2">How to Test Webhook:</h4>
        <ol className="text-gray-300 text-sm space-y-1">
          <li>1. Create a test user in Clerk Dashboard</li>
          <li>2. Check if the user appears in Supabase (using "Fetch All Users")</li>
          <li>3. Delete the user from Clerk Dashboard</li>
          <li>4. Check if the user is automatically deleted from Supabase</li>
          <li>5. If not, the webhook might not be configured properly</li>
        </ol>
        
        <div className="mt-4 p-3 bg-yellow-500/10 border border-yellow-500 rounded-lg">
          <h5 className="text-yellow-400 font-semibold mb-1">Webhook Configuration:</h5>
          <p className="text-yellow-300 text-xs">
            Make sure your Clerk webhook is configured with the <code className="bg-navy px-1 rounded">user.deleted</code> event enabled.
          </p>
        </div>
      </div>
    </div>
  );
}

'use client';

import { useState } from 'react';

export function WebhookSetup() {
  const [activeTab, setActiveTab] = useState<'clerk' | 'stripe' | 'test'>('clerk');

  const getWebhookUrl = (platform: string) => {
    if (typeof window !== 'undefined') {
      const baseUrl = window.location.origin;
      return `${baseUrl}/api/webhooks/${platform}`;
    }
    return `https://yourdomain.com/api/webhooks/${platform}`;
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    alert('Copied to clipboard!');
  };

  return (
    <div className="bg-navy-light rounded-lg p-6 border border-navy-light">
      <h3 className="text-xl font-bold text-white mb-4">🔗 Webhook Configuration Guide</h3>
      <p className="text-gray-300 mb-6">
        Set up webhooks on all platforms for automatic user and payment synchronization.
      </p>

      {/* Tab Navigation */}
      <div className="flex space-x-1 mb-6">
        {[
          { id: 'clerk', label: 'Clerk Setup', icon: '👤' },
          { id: 'stripe', label: 'Stripe Setup', icon: '💳' },
          { id: 'test', label: 'Test Webhooks', icon: '🧪' },
        ].map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id as any)}
            className={`px-4 py-2 rounded-lg font-medium transition-all duration-300 ${
              activeTab === tab.id
                ? 'bg-green-500 text-white'
                : 'bg-navy text-gray-400 hover:text-white hover:bg-navy-light'
            }`}
          >
            {tab.icon} {tab.label}
          </button>
        ))}
      </div>

      {/* Clerk Setup Tab */}
      {activeTab === 'clerk' && (
        <div className="space-y-6">
          <div className="bg-blue-500/10 border border-blue-500 rounded-lg p-4">
            <h4 className="text-blue-400 font-semibold mb-3">📋 Clerk Webhook Setup</h4>
            
            <div className="space-y-4">
              <div>
                <label className="text-white font-semibold block mb-2">1. Webhook URL:</label>
                <div className="flex items-center gap-2">
                  <code className="flex-1 bg-navy p-2 rounded text-green-400 text-sm">
                    {getWebhookUrl('clerk')}
                  </code>
                  <button
                    onClick={() => copyToClipboard(getWebhookUrl('clerk'))}
                    className="bg-blue-500 hover:bg-blue-600 text-white px-3 py-2 rounded text-sm"
                  >
                    Copy
                  </button>
                </div>
              </div>

              <div>
                <label className="text-white font-semibold block mb-2">2. Events to Subscribe:</label>
                <div className="grid grid-cols-2 gap-2">
                  {['user.created', 'user.updated', 'user.deleted', 'session.created'].map((event) => (
                    <div key={event} className="bg-navy p-2 rounded">
                      <span className="text-green-400">✅</span>
                      <code className="text-gray-300 ml-2">{event}</code>
                    </div>
                  ))}
                </div>
              </div>

              <div>
                <label className="text-white font-semibold block mb-2">3. Environment Variable:</label>
                <code className="block bg-navy p-2 rounded text-yellow-400 text-sm">
                  CLERK_WEBHOOK_SECRET=whsec_your_signing_secret_here
                </code>
              </div>
            </div>

            <div className="mt-4 p-3 bg-yellow-500/10 border border-yellow-500 rounded">
              <p className="text-yellow-300 text-sm">
                <strong>📍 Setup Steps:</strong><br />
                1. Go to <a href="https://dashboard.clerk.com" target="_blank" className="underline">Clerk Dashboard</a><br />
                2. Configure → Webhooks → Add Endpoint<br />
                3. Paste the webhook URL above<br />
                4. Select the events listed above<br />
                5. Copy the signing secret to your .env.local
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Stripe Setup Tab */}
      {activeTab === 'stripe' && (
        <div className="space-y-6">
          <div className="bg-purple-500/10 border border-purple-500 rounded-lg p-4">
            <h4 className="text-purple-400 font-semibold mb-3">💳 Stripe Webhook Setup</h4>
            
            <div className="space-y-4">
              <div>
                <label className="text-white font-semibold block mb-2">1. Webhook URL:</label>
                <div className="flex items-center gap-2">
                  <code className="flex-1 bg-navy p-2 rounded text-green-400 text-sm">
                    {getWebhookUrl('stripe')}
                  </code>
                  <button
                    onClick={() => copyToClipboard(getWebhookUrl('stripe'))}
                    className="bg-purple-500 hover:bg-purple-600 text-white px-3 py-2 rounded text-sm"
                  >
                    Copy
                  </button>
                </div>
              </div>

              <div>
                <label className="text-white font-semibold block mb-2">2. Events to Send:</label>
                <div className="grid grid-cols-1 gap-2">
                  {[
                    'checkout.session.completed',
                    'invoice.payment_succeeded',
                    'invoice.payment_failed',
                    'customer.subscription.created',
                    'customer.subscription.updated',
                    'customer.subscription.deleted'
                  ].map((event) => (
                    <div key={event} className="bg-navy p-2 rounded">
                      <span className="text-green-400">✅</span>
                      <code className="text-gray-300 ml-2">{event}</code>
                    </div>
                  ))}
                </div>
              </div>

              <div>
                <label className="text-white font-semibold block mb-2">3. Environment Variables:</label>
                <div className="space-y-2">
                  <code className="block bg-navy p-2 rounded text-yellow-400 text-sm">
                    STRIPE_SECRET_KEY=sk_your_stripe_secret_key
                  </code>
                  <code className="block bg-navy p-2 rounded text-yellow-400 text-sm">
                    STRIPE_WEBHOOK_SECRET=whsec_your_stripe_webhook_secret
                  </code>
                </div>
              </div>
            </div>

            <div className="mt-4 p-3 bg-yellow-500/10 border border-yellow-500 rounded">
              <p className="text-yellow-300 text-sm">
                <strong>📍 Setup Steps:</strong><br />
                1. Go to <a href="https://dashboard.stripe.com" target="_blank" className="underline">Stripe Dashboard</a><br />
                2. Developers → Webhooks → Add endpoint<br />
                3. Paste the webhook URL above<br />
                4. Select the events listed above<br />
                5. Copy the signing secret to your .env.local
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Test Webhooks Tab */}
      {activeTab === 'test' && (
        <div className="space-y-6">
          <div className="bg-green-500/10 border border-green-500 rounded-lg p-4">
            <h4 className="text-green-400 font-semibold mb-3">🧪 Test Your Webhooks</h4>
            
            <div className="space-y-4">
              <div className="bg-navy rounded-lg p-4">
                <h5 className="text-white font-semibold mb-2">Test Clerk Webhook:</h5>
                <ol className="text-gray-300 text-sm space-y-1">
                  <li>1. Create a test user in Clerk Dashboard</li>
                  <li>2. Check if user appears in Supabase (User Deletion Test tool)</li>
                  <li>3. Delete user from Clerk Dashboard</li>
                  <li>4. Verify user is automatically deleted from Supabase</li>
                  <li>5. Check deployment logs for webhook activity</li>
                </ol>
              </div>

              <div className="bg-navy rounded-lg p-4">
                <h5 className="text-white font-semibold mb-2">Test Stripe Webhook:</h5>
                <ol className="text-gray-300 text-sm space-y-1">
                  <li>1. Make a test purchase through Clerk's PricingTable</li>
                  <li>2. Check Stripe Dashboard for successful payment</li>
                  <li>3. Verify user credits are automatically updated</li>
                  <li>4. Check deployment logs for webhook activity</li>
                  <li>5. Test subscription cancellation</li>
                </ol>
              </div>

              <div className="bg-navy rounded-lg p-4">
                <h5 className="text-white font-semibold mb-2">Webhook Status Check:</h5>
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center">
                    <p className="text-gray-400 text-sm">Clerk Webhook</p>
                    <div className="mt-2">
                      <span className="inline-block w-3 h-3 bg-yellow-500 rounded-full"></span>
                      <span className="text-yellow-400 ml-2 text-sm">Check Configuration</span>
                    </div>
                  </div>
                  <div className="text-center">
                    <p className="text-gray-400 text-sm">Stripe Webhook</p>
                    <div className="mt-2">
                      <span className="inline-block w-3 h-3 bg-yellow-500 rounded-full"></span>
                      <span className="text-yellow-400 ml-2 text-sm">Check Configuration</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-red-500/10 border border-red-500 rounded-lg p-4">
            <h5 className="text-red-400 font-semibold mb-2">🚨 Common Issues:</h5>
            <ul className="text-red-300 text-sm space-y-1">
              <li>• Webhook URL not accessible (check deployment)</li>
              <li>• Wrong webhook secret in environment variables</li>
              <li>• Events not selected in platform dashboard</li>
              <li>• Foreign key constraints not set up (run CASCADE DELETE migration)</li>
              <li>• RLS policies blocking webhook operations</li>
            </ul>
          </div>
        </div>
      )}

      {/* Quick Actions */}
      <div className="mt-6 flex gap-4">
        <a
          href="https://dashboard.clerk.com"
          target="_blank"
          rel="noopener noreferrer"
          className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors"
        >
          Open Clerk Dashboard
        </a>
        <a
          href="https://dashboard.stripe.com"
          target="_blank"
          rel="noopener noreferrer"
          className="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg transition-colors"
        >
          Open Stripe Dashboard
        </a>
      </div>
    </div>
  );
}

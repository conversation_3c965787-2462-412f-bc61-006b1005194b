'use client';

import { useState } from 'react';
import { useUser } from '@clerk/nextjs';

interface SyncResult {
  processed: number;
  updated: number;
  errors: number;
  details: Array<{
    email: string;
    status: string;
    message: string;
    oldPlan?: string;
    newPlan?: string;
    oldCredits?: number;
    newCredits?: number;
    priceId?: string;
  }>;
}

interface UserStripeStatus {
  user: {
    email: string;
    currentPlan: string;
    currentCredits: number;
    stripeCustomerId?: string;
  };
  stripe: {
    customerId: string;
    subscriptions: Array<{
      id: string;
      status: string;
      priceId: string;
      amount: number;
      interval: string;
      created: string;
    }>;
  } | null;
  needsSync: boolean;
  message?: string;
}

export function CustomerSync() {
  const [loading, setLoading] = useState(false);
  const [checkLoading, setCheckLoading] = useState(false);
  const [syncResults, setSyncResults] = useState<SyncResult | null>(null);
  const [userStatus, setUserStatus] = useState<UserStripeStatus | null>(null);
  const [message, setMessage] = useState('');
  const [emailToCheck, setEmailToCheck] = useState('');
  const { user } = useUser();

  const checkMyStatus = async () => {
    if (!user?.emailAddresses?.[0]?.emailAddress) {
      setMessage('❌ No email found for current user');
      return;
    }

    setCheckLoading(true);
    setMessage('');

    try {
      const response = await fetch(`/api/admin/sync-stripe-customers?email=${encodeURIComponent(user.emailAddresses[0].emailAddress)}`);
      const data = await response.json();

      if (response.ok) {
        setUserStatus(data);
        if (data.needsSync) {
          setMessage('🔄 Your account needs syncing with Stripe data');
        } else if (data.stripe) {
          setMessage('✅ Your account is already synced with Stripe');
        } else {
          setMessage('ℹ️ No Stripe customer found for your email');
        }
      } else {
        setMessage(`❌ Error: ${data.error}`);
      }
    } catch (error) {
      setMessage(`❌ Error: ${error instanceof Error ? error.message : 'Something went wrong'}`);
    } finally {
      setCheckLoading(false);
    }
  };

  const checkSpecificUser = async () => {
    if (!emailToCheck.trim()) {
      setMessage('❌ Please enter an email address');
      return;
    }

    setCheckLoading(true);
    setMessage('');

    try {
      const response = await fetch(`/api/admin/sync-stripe-customers?email=${encodeURIComponent(emailToCheck)}`);
      const data = await response.json();

      if (response.ok) {
        setUserStatus(data);
        if (data.needsSync) {
          setMessage(`🔄 User ${emailToCheck} needs syncing with Stripe data`);
        } else if (data.stripe) {
          setMessage(`✅ User ${emailToCheck} is already synced with Stripe`);
        } else {
          setMessage(`ℹ️ No Stripe customer found for ${emailToCheck}`);
        }
      } else {
        setMessage(`❌ Error: ${data.error}`);
      }
    } catch (error) {
      setMessage(`❌ Error: ${error instanceof Error ? error.message : 'Something went wrong'}`);
    } finally {
      setCheckLoading(false);
    }
  };

  const syncMyAccount = async () => {
    if (!user?.emailAddresses?.[0]?.emailAddress) {
      setMessage('❌ No email found for current user');
      return;
    }

    setLoading(true);
    setMessage('');

    try {
      const response = await fetch('/api/admin/sync-stripe-customers', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          syncAll: false,
          userEmail: user.emailAddresses[0].emailAddress
        }),
      });

      const data = await response.json();

      if (data.success) {
        setSyncResults(data.results);
        setMessage(`✅ ${data.summary}`);
        // Refresh status after sync
        setTimeout(() => checkMyStatus(), 1000);
      } else {
        setMessage(`❌ Error: ${data.error}`);
      }
    } catch (error) {
      setMessage(`❌ Error: ${error instanceof Error ? error.message : 'Something went wrong'}`);
    } finally {
      setLoading(false);
    }
  };

  const syncAllCustomers = async () => {
    if (!confirm('This will sync ALL users with their Stripe subscriptions. This may take a while. Continue?')) {
      return;
    }

    setLoading(true);
    setMessage('');

    try {
      const response = await fetch('/api/admin/sync-stripe-customers', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ syncAll: true }),
      });

      const data = await response.json();

      if (data.success) {
        setSyncResults(data.results);
        setMessage(`✅ ${data.summary}`);
      } else {
        setMessage(`❌ Error: ${data.error}`);
      }
    } catch (error) {
      setMessage(`❌ Error: ${error instanceof Error ? error.message : 'Something went wrong'}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="bg-navy-light rounded-lg p-6 border border-navy-light">
      <h3 className="text-xl font-bold text-white mb-4">Stripe Customer Sync</h3>
      <p className="text-gray-300 mb-6">
        Check and sync existing Stripe customers who have already paid but haven't received their credits.
      </p>

      {/* Check My Status */}
      <div className="mb-6 p-4 bg-blue-500/10 border border-blue-500 rounded-lg">
        <h4 className="text-blue-400 font-semibold mb-3">Check My Account Status</h4>
        <div className="flex gap-3">
          <button
            onClick={checkMyStatus}
            disabled={checkLoading}
            className={`bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors ${
              checkLoading ? 'opacity-50 cursor-not-allowed' : ''
            }`}
          >
            {checkLoading ? 'Checking...' : 'Check My Status'}
          </button>
          
          {userStatus?.needsSync && (
            <button
              onClick={syncMyAccount}
              disabled={loading}
              className={`bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition-colors ${
                loading ? 'opacity-50 cursor-not-allowed' : ''
              }`}
            >
              {loading ? 'Syncing...' : 'Sync My Account'}
            </button>
          )}
        </div>
      </div>

      {/* Check Specific User */}
      <div className="mb-6 p-4 bg-purple-500/10 border border-purple-500 rounded-lg">
        <h4 className="text-purple-400 font-semibold mb-3">Check Specific User</h4>
        <div className="flex gap-3">
          <input
            type="email"
            value={emailToCheck}
            onChange={(e) => setEmailToCheck(e.target.value)}
            placeholder="Enter user email..."
            className="flex-1 bg-navy border border-gray-600 rounded-lg px-3 py-2 text-white placeholder-gray-400"
          />
          <button
            onClick={checkSpecificUser}
            disabled={checkLoading}
            className={`bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg transition-colors ${
              checkLoading ? 'opacity-50 cursor-not-allowed' : ''
            }`}
          >
            {checkLoading ? 'Checking...' : 'Check User'}
          </button>
        </div>
      </div>

      {/* Sync All Customers */}
      <div className="mb-6 p-4 bg-orange-500/10 border border-orange-500 rounded-lg">
        <h4 className="text-orange-400 font-semibold mb-3">⚠️ Sync All Customers</h4>
        <p className="text-orange-300 text-sm mb-3">
          This will check ALL users in your database and sync them with their Stripe subscriptions.
        </p>
        <button
          onClick={syncAllCustomers}
          disabled={loading}
          className={`bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-lg transition-colors ${
            loading ? 'opacity-50 cursor-not-allowed' : ''
          }`}
        >
          {loading ? 'Syncing All...' : 'Sync All Customers'}
        </button>
      </div>

      {/* User Status Display */}
      {userStatus && (
        <div className="mb-6 p-4 bg-navy rounded-lg">
          <h4 className="text-white font-semibold mb-3">User Status:</h4>
          <div className="space-y-2 text-sm">
            <p className="text-gray-300">
              <span className="text-white">Email:</span> {userStatus.user.email}
            </p>
            <p className="text-gray-300">
              <span className="text-white">Current Plan:</span> {userStatus.user.currentPlan}
            </p>
            <p className="text-gray-300">
              <span className="text-white">Current Credits:</span> {userStatus.user.currentCredits.toLocaleString()}
            </p>
            
            {userStatus.stripe ? (
              <div className="mt-3">
                <p className="text-green-400 font-semibold">Stripe Subscriptions Found:</p>
                {userStatus.stripe.subscriptions.map((sub, index) => (
                  <div key={sub.id} className="ml-4 mt-2 p-2 bg-green-500/10 rounded">
                    <p className="text-green-300">
                      Subscription {index + 1}: ${sub.amount / 100}/{sub.interval} 
                      <span className="text-gray-400"> (Status: {sub.status})</span>
                    </p>
                    <p className="text-gray-400 text-xs">Price ID: {sub.priceId}</p>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-red-400">No Stripe customer found</p>
            )}
          </div>
        </div>
      )}

      {/* Message Display */}
      {message && (
        <div className={`p-4 rounded-lg mb-6 ${
          message.includes('✅') ? 'bg-green-500/10 border border-green-500 text-green-400' :
          message.includes('❌') ? 'bg-red-500/10 border border-red-500 text-red-400' :
          message.includes('🔄') ? 'bg-yellow-500/10 border border-yellow-500 text-yellow-400' :
          'bg-blue-500/10 border border-blue-500 text-blue-400'
        }`}>
          <p className="text-sm">{message}</p>
        </div>
      )}

      {/* Sync Results */}
      {syncResults && (
        <div className="p-4 bg-navy rounded-lg">
          <h4 className="text-white font-semibold mb-3">Sync Results:</h4>
          <div className="grid grid-cols-3 gap-4 mb-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-blue-400">{syncResults.processed}</p>
              <p className="text-gray-400 text-sm">Processed</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-green-400">{syncResults.updated}</p>
              <p className="text-gray-400 text-sm">Updated</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-red-400">{syncResults.errors}</p>
              <p className="text-gray-400 text-sm">Errors</p>
            </div>
          </div>
          
          <div className="max-h-60 overflow-y-auto space-y-2">
            {syncResults.details.map((detail, index) => (
              <div key={index} className={`p-2 rounded text-xs ${
                detail.status === 'updated' ? 'bg-green-500/10 text-green-400' :
                detail.status === 'error' ? 'bg-red-500/10 text-red-400' :
                detail.status === 'already_updated' ? 'bg-blue-500/10 text-blue-400' :
                'bg-gray-500/10 text-gray-400'
              }`}>
                <p><strong>{detail.email}</strong>: {detail.message}</p>
                {detail.oldPlan && detail.newPlan && (
                  <p className="mt-1">
                    {detail.oldPlan} ({detail.oldCredits?.toLocaleString()}) → {detail.newPlan} ({detail.newCredits?.toLocaleString()})
                  </p>
                )}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}

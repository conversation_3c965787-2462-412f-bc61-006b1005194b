'use client';

import { useState, useEffect } from 'react';

interface StripePrice {
  id: string;
  amount: number;
  currency: string;
  interval: string;
  product: {
    id: string;
    name: string;
    description: string;
  };
  active: boolean;
  created: string;
}

export function StripePriceMapper() {
  const [prices, setPrices] = useState<StripePrice[]>([]);
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');

  const fetchPrices = async () => {
    setLoading(true);
    setMessage('');

    try {
      const response = await fetch('/api/admin/list-stripe-prices');
      const data = await response.json();

      if (data.success) {
        setPrices(data.prices);
        setMessage(`✅ Found ${data.total} Stripe prices`);
      } else {
        setMessage(`❌ Error: ${data.error}`);
      }
    } catch (error) {
      setMessage(`❌ Error: ${error instanceof Error ? error.message : 'Something went wrong'}`);
    } finally {
      setLoading(false);
    }
  };

  const testPriceMapping = async (priceId: string) => {
    try {
      const response = await fetch('/api/admin/list-stripe-prices', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ priceId }),
      });

      const data = await response.json();

      if (data.success) {
        const status = data.isMapped ? '✅ MAPPED' : '❌ NOT MAPPED';
        setMessage(`${status} - Price ${priceId} → ${data.mappedPlan.type} (${data.mappedPlan.credits} credits)`);
      } else {
        setMessage(`❌ Error testing price: ${data.error}`);
      }
    } catch (error) {
      setMessage(`❌ Error: ${error instanceof Error ? error.message : 'Something went wrong'}`);
    }
  };

  const copyPriceId = (priceId: string) => {
    navigator.clipboard.writeText(priceId);
    setMessage(`📋 Copied price ID: ${priceId}`);
  };

  const generateMappingCode = () => {
    const relevantPrices = prices.filter(price => 
      price.product.name.toLowerCase().includes('standard') ||
      price.product.name.toLowerCase().includes('pro') ||
      price.product.name.toLowerCase().includes('premium') ||
      price.amount === 1 || price.amount === 26 || price.amount === 78
    );

    const mappingCode = relevantPrices.map(price => {
      let planType = 'Unknown';
      let credits = 0;

      if (price.product.name.toLowerCase().includes('standard') || price.amount === 1) {
        planType = 'Standard';
        credits = 700;
      } else if (price.product.name.toLowerCase().includes('pro') || price.amount === 26) {
        planType = 'Pro';
        credits = 999999;
      } else if (price.product.name.toLowerCase().includes('premium') || price.amount === 78) {
        planType = 'Premium';
        credits = 999999;
      }

      return `'${price.id}': { type: '${planType}', credits: ${credits} }, // ${price.product.name} - $${price.amount}/${price.interval}`;
    }).join('\n    ');

    return `const planMapping: Record<string, { type: string; credits: number }> = {\n    ${mappingCode}\n  };`;
  };

  return (
    <div className="bg-navy-light rounded-lg p-6 border border-navy-light">
      <h3 className="text-xl font-bold text-white mb-4">Stripe Price ID Mapper</h3>
      <p className="text-gray-300 mb-4">
        Use this tool to find your Stripe price IDs and map them to your plans for automatic credit updates.
      </p>

      {/* Fetch Prices Button */}
      <div className="mb-6">
        <button
          onClick={fetchPrices}
          disabled={loading}
          className={`bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors ${
            loading ? 'opacity-50 cursor-not-allowed' : ''
          }`}
        >
          {loading ? 'Loading...' : 'Fetch Stripe Prices'}
        </button>
      </div>

      {/* Message Display */}
      {message && (
        <div className={`p-4 rounded-lg mb-6 ${
          message.includes('✅') ? 'bg-green-500/10 border border-green-500 text-green-400' :
          message.includes('❌') ? 'bg-red-500/10 border border-red-500 text-red-400' :
          message.includes('📋') ? 'bg-blue-500/10 border border-blue-500 text-blue-400' :
          'bg-gray-500/10 border border-gray-500 text-gray-400'
        }`}>
          <p className="text-sm font-mono">{message}</p>
        </div>
      )}

      {/* Prices List */}
      {prices.length > 0 && (
        <div className="space-y-4">
          <h4 className="text-lg font-semibold text-white">Your Stripe Prices:</h4>
          
          <div className="grid gap-4">
            {prices.map((price) => (
              <div key={price.id} className="bg-navy rounded-lg p-4 border border-navy-light">
                <div className="flex justify-between items-start mb-2">
                  <div>
                    <h5 className="text-white font-semibold">{price.product.name}</h5>
                    <p className="text-gray-400 text-sm">{price.product.description}</p>
                  </div>
                  <div className="text-right">
                    <p className="text-green-400 font-bold">
                      ${price.amount} / {price.interval}
                    </p>
                    <p className="text-gray-400 text-xs">{price.currency.toUpperCase()}</p>
                  </div>
                </div>
                
                <div className="flex items-center justify-between">
                  <code className="text-xs bg-navy-light px-2 py-1 rounded text-gray-300">
                    {price.id}
                  </code>
                  <div className="flex gap-2">
                    <button
                      onClick={() => copyPriceId(price.id)}
                      className="text-xs bg-blue-500 hover:bg-blue-600 text-white px-2 py-1 rounded transition-colors"
                    >
                      Copy ID
                    </button>
                    <button
                      onClick={() => testPriceMapping(price.id)}
                      className="text-xs bg-purple-500 hover:bg-purple-600 text-white px-2 py-1 rounded transition-colors"
                    >
                      Test Mapping
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Generated Mapping Code */}
          <div className="mt-8 p-4 bg-navy rounded-lg border border-navy-light">
            <h4 className="text-lg font-semibold text-white mb-3">Generated Mapping Code:</h4>
            <p className="text-gray-300 text-sm mb-3">
              Copy this code and update the planMapping in your Stripe webhook:
            </p>
            <pre className="bg-black p-4 rounded text-green-400 text-xs overflow-x-auto">
              {generateMappingCode()}
            </pre>
            <button
              onClick={() => {
                navigator.clipboard.writeText(generateMappingCode());
                setMessage('📋 Copied mapping code to clipboard!');
              }}
              className="mt-3 bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition-colors"
            >
              Copy Mapping Code
            </button>
          </div>
        </div>
      )}

      {/* Instructions */}
      <div className="mt-6 p-4 bg-navy rounded-lg">
        <h4 className="text-white font-semibold mb-2">Setup Instructions:</h4>
        <ol className="text-gray-300 text-sm space-y-1">
          <li>1. Click "Fetch Stripe Prices" to see your current Stripe products</li>
          <li>2. Copy the generated mapping code</li>
          <li>3. Update the planMapping in /api/webhooks/stripe/route.ts</li>
          <li>4. Set up Stripe webhook endpoint in your Stripe dashboard</li>
          <li>5. Test with a real purchase to verify automatic updates</li>
        </ol>
      </div>
    </div>
  );
}

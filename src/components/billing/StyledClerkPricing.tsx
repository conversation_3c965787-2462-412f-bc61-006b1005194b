'use client';

import { PricingTable } from '@clerk/nextjs';
import { useUser } from '@clerk/nextjs';
import { CreditUpdater } from '@/components/admin/CreditUpdater';
import { StripePriceMapper } from '@/components/admin/StripePriceMapper';
import { CustomerSync } from '@/components/admin/CustomerSync';
import { UserDeletionTest } from '@/components/admin/UserDeletionTest';
import { WebhookSetup } from '@/components/admin/WebhookSetup';
import { SubscriptionManager } from '@/components/subscription/SubscriptionManager';
import './clerk-pricing-styles.css';

export function StyledClerkPricing() {
  const { user } = useUser();

  return (
    <div className="styled-clerk-pricing">
      {/* Header */}
      <div className="pricing-header">
        <h1 className="text-3xl font-bold text-white mb-4">Choose Your Plan</h1>
        <p className="text-gray-300 mb-8">
          Welcome {user?.firstName}! Upgrade your account to unlock more credits and advanced features.
        </p>
      </div>

      {/* Custom styled Clerk PricingTable */}
      <div className="clerk-pricing-wrapper">
        <PricingTable />
      </div>

      {/* Additional Features Section */}
      <div className="features-section mt-12">
        <h2 className="text-2xl font-bold text-white mb-8 text-center">What's Included</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="feature-card">
            <div className="feature-icon">
              <svg className="w-8 h-8 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-white mb-3">AI-Powered Privacy</h3>
            <ul className="text-gray-300 space-y-2">
              <li>• Advanced face detection</li>
              <li>• Vehicle anonymization</li>
              <li>• Real-time processing</li>
              <li>• High accuracy rates</li>
            </ul>
          </div>

          <div className="feature-card">
            <div className="feature-icon">
              <svg className="w-8 h-8 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-white mb-3">Fast Processing</h3>
            <ul className="text-gray-300 space-y-2">
              <li>• Batch processing</li>
              <li>• Cloud acceleration</li>
              <li>• Multiple formats</li>
              <li>• Quick turnaround</li>
            </ul>
          </div>

          <div className="feature-card">
            <div className="feature-icon">
              <svg className="w-8 h-8 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-white mb-3">Secure & Compliant</h3>
            <ul className="text-gray-300 space-y-2">
              <li>• GDPR compliant</li>
              <li>• End-to-end encryption</li>
              <li>• No data retention</li>
              <li>• Privacy by design</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Subscription Management Section */}
      <div className="mt-12">
        <SubscriptionManager />
      </div>

      {/* Webhook Setup Section */}
      <div className="mt-12">
        <WebhookSetup />
      </div>

      {/* Customer Sync Section */}
      <div className="mt-12">
        <CustomerSync />
      </div>

      {/* Credit Updater Section */}
      <div className="mt-12">
        <CreditUpdater />
      </div>

      {/* Stripe Price Mapper Section */}
      <div className="mt-12">
        <StripePriceMapper />
      </div>

      {/* User Deletion Test Section */}
      <div className="mt-12">
        <UserDeletionTest />
      </div>

      {/* Support Section */}
      <div className="support-section mt-12 text-center">
        <div className="bg-navy-light rounded-lg p-8">
          <h3 className="text-xl font-bold text-white mb-4">Need Help Choosing?</h3>
          <p className="text-gray-300 mb-6">
            Our team is here to help you find the perfect plan for your needs.
          </p>
          <div className="flex gap-4 justify-center">
            <a
              href="mailto:<EMAIL>"
              className="inline-flex items-center rounded-md bg-green-500 hover:bg-green-600 px-6 py-3 text-sm font-medium text-white transition-all duration-300"
            >
              Contact Support
            </a>
            <a
              href="/dashboard"
              className="inline-flex items-center rounded-md border border-gray-600 px-6 py-3 text-sm font-medium text-gray-300 hover:bg-gray-700 transition-all duration-300"
            >
              Back to Dashboard
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}

/* Custom styles for Clerk PricingTable to match GuardiaVision theme */

.styled-clerk-pricing {
  @apply space-y-8;
}

.pricing-header {
  @apply text-center mb-12;
}

.clerk-pricing-wrapper {
  /* Override Clerk's default styles */
  --clerk-color-primary: #10b981; /* Green-500 */
  --clerk-color-primary-hover: #059669; /* Green-600 */
  --clerk-color-background: #1e293b; /* Navy */
  --clerk-color-background-secondary: #334155; /* Navy-light */
  --clerk-color-text: #ffffff;
  --clerk-color-text-secondary: #d1d5db; /* Gray-300 */
  --clerk-border-radius: 0.75rem; /* rounded-xl */
  --clerk-border-color: #475569; /* Navy-light border */
}

/* Style the pricing cards */
.clerk-pricing-wrapper [data-clerk-pricing-table] {
  @apply grid gap-6 md:grid-cols-2 lg:grid-cols-4;
}

/* Individual pricing card styling */
.clerk-pricing-wrapper [data-clerk-pricing-card] {
  @apply bg-navy border border-navy-light rounded-xl p-6 shadow-xl transition-all duration-300 hover:shadow-2xl hover:border-green-400/30;
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
}

/* Popular plan highlight */
.clerk-pricing-wrapper [data-clerk-pricing-card][data-popular="true"] {
  @apply border-2 border-green-400 relative;
}

.clerk-pricing-wrapper [data-clerk-pricing-card][data-popular="true"]::before {
  content: "Most Popular";
  @apply absolute -top-4 left-0 right-0 mx-auto w-32 rounded-md bg-green-400 py-1.5 text-center text-sm font-bold text-navy;
}

/* Plan name styling */
.clerk-pricing-wrapper [data-clerk-plan-name] {
  @apply text-xl font-bold text-white mb-2;
}

/* Plan description */
.clerk-pricing-wrapper [data-clerk-plan-description] {
  @apply text-sm text-gray-400 mb-4;
}

/* Price styling */
.clerk-pricing-wrapper [data-clerk-price] {
  @apply text-4xl font-extrabold text-white mb-1;
}

.clerk-pricing-wrapper [data-clerk-price-period] {
  @apply text-sm text-gray-400;
}

/* Subscribe button styling */
.clerk-pricing-wrapper [data-clerk-subscribe-button] {
  @apply w-full h-12 rounded-lg font-medium transition-all duration-300 text-lg mt-6;
}

/* Free plan button */
.clerk-pricing-wrapper [data-clerk-pricing-card][data-plan="free"] [data-clerk-subscribe-button] {
  @apply border border-blue-400 text-blue-400 hover:text-white hover:border-transparent hover:bg-blue-500;
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
}

/* Standard plan button */
.clerk-pricing-wrapper [data-clerk-pricing-card][data-plan="standard"] [data-clerk-subscribe-button] {
  @apply border border-teal-400 text-teal-400 hover:text-white hover:border-transparent hover:bg-teal-500;
  box-shadow: 0 0 20px rgba(20, 184, 166, 0.3);
}

/* Pro plan button (popular) */
.clerk-pricing-wrapper [data-clerk-pricing-card][data-plan="pro"] [data-clerk-subscribe-button] {
  @apply bg-green-400 text-navy hover:bg-green-500;
  box-shadow: 0 0 20px rgba(34, 197, 94, 0.4);
}

/* Premium plan button */
.clerk-pricing-wrapper [data-clerk-pricing-card][data-plan="premium"] [data-clerk-subscribe-button] {
  @apply border border-purple-400 text-purple-400 hover:text-white hover:border-transparent hover:bg-purple-500;
  box-shadow: 0 0 20px rgba(168, 85, 247, 0.3);
}

/* Features list styling */
.clerk-pricing-wrapper [data-clerk-features] {
  @apply mt-6 space-y-3;
}

.clerk-pricing-wrapper [data-clerk-feature] {
  @apply flex items-start text-sm text-gray-300;
}

.clerk-pricing-wrapper [data-clerk-feature-icon] {
  @apply flex-shrink-0 rounded-full bg-green-400 p-1 mt-0.5 mr-3;
}

.clerk-pricing-wrapper [data-clerk-feature-icon] svg {
  @apply h-3 w-3 text-navy;
}

/* Toggle styling for billing period */
.clerk-pricing-wrapper [data-clerk-billing-toggle] {
  @apply flex items-center justify-center mb-8 space-x-4;
}

.clerk-pricing-wrapper [data-clerk-billing-toggle] button {
  @apply px-4 py-2 rounded-lg font-medium transition-all duration-300;
}

.clerk-pricing-wrapper [data-clerk-billing-toggle] button[data-active="true"] {
  @apply bg-green-400 text-navy;
}

.clerk-pricing-wrapper [data-clerk-billing-toggle] button[data-active="false"] {
  @apply text-gray-400 hover:text-white;
}

/* Feature cards styling */
.feature-card {
  @apply bg-navy-light rounded-lg p-6 border border-navy-light hover:border-green-400/30 transition-all duration-300;
}

.feature-icon {
  @apply w-12 h-12 bg-navy rounded-lg flex items-center justify-center mb-4;
}

/* Support section */
.support-section {
  @apply max-w-2xl mx-auto;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .clerk-pricing-wrapper [data-clerk-pricing-table] {
    @apply grid-cols-1 gap-4;
  }
  
  .features-section .grid {
    @apply grid-cols-1 gap-6;
  }
}

/* Animation for cards */
.clerk-pricing-wrapper [data-clerk-pricing-card] {
  animation: fadeInUp 0.6s ease-out forwards;
}

.clerk-pricing-wrapper [data-clerk-pricing-card]:nth-child(1) {
  animation-delay: 0.1s;
}

.clerk-pricing-wrapper [data-clerk-pricing-card]:nth-child(2) {
  animation-delay: 0.2s;
}

.clerk-pricing-wrapper [data-clerk-pricing-card]:nth-child(3) {
  animation-delay: 0.3s;
}

.clerk-pricing-wrapper [data-clerk-pricing-card]:nth-child(4) {
  animation-delay: 0.4s;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

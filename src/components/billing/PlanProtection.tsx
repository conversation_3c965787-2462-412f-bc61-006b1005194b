import { auth } from '@clerk/nextjs/server';
import { Protect } from '@clerk/nextjs';

// Server-side plan checking
export async function checkUserPlan() {
  const { has } = await auth();
  
  return {
    hasStandardPlan: has({ plan: 'standard' }),
    hasProPlan: has({ plan: 'pro' }),
    hasPremiumPlan: has({ plan: 'premium' }),
    // You can also check for specific features
    hasAdvancedFeatures: has({ feature: 'advanced-processing' }),
    hasUnlimitedUploads: has({ feature: 'unlimited-uploads' }),
  };
}

// Client-side protection component for React
interface PlanProtectProps {
  plan: string;
  fallback?: React.ReactNode;
  children: React.ReactNode;
}

export function PlanProtect({ plan, fallback, children }: PlanProtectProps) {
  return (
    <Protect
      plan={plan}
      fallback={fallback || <div className="text-red-400">You need the {plan} plan to access this feature.</div>}
    >
      {children}
    </Protect>
  );
}

// Feature protection component
interface FeatureProtectProps {
  feature: string;
  fallback?: React.ReactNode;
  children: React.ReactNode;
}

export function FeatureProtect({ feature, fallback, children }: FeatureProtectProps) {
  return (
    <Protect
      feature={feature}
      fallback={fallback || <div className="text-red-400">You need access to the {feature} feature.</div>}
    >
      {children}
    </Protect>
  );
}

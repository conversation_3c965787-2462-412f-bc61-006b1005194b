'use client';

import { useState, useEffect } from 'react';
import { useUser } from '@clerk/nextjs';
import { useSupabaseClient } from '@/utils/supabase/client';
import { Play, Clock, AlertCircle, CheckCircle, XCircle, Loader2 } from 'lucide-react';
import Image from 'next/image';

type MediaType = 'all' | 'image' | 'video';

interface MediaItem {
  id: number;
  user_id: string;
  original_url: string;
  processed_url: string | null;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  credits_used: number;
  created_at: string;
  metadata: any;
  // Video-specific fields
  duration_seconds?: number;
}

export function MediaGallery() {
  const { user } = useUser();
  const supabase = useSupabaseClient();
  const [mediaType, setMediaType] = useState<MediaType>('all');
  const [mediaItems, setMediaItems] = useState<MediaItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [sortOrder, setSortOrder] = useState<'latest' | 'oldest'>('latest');

  useEffect(() => {
    if (!user) return;
    
    async function fetchMedia() {
      setLoading(true);
      setError(null);
      
      try {
        // Fetch images
        const { data: images, error: imagesError } = await supabase
          .from('processed_images')
          .select('*')
          .eq('user_id', user.id)
          .order('created_at', { ascending: sortOrder === 'oldest' });
        
        if (imagesError) {
          throw imagesError;
        }
        
        // Fetch videos
        const { data: videos, error: videosError } = await supabase
          .from('processed_videos')
          .select('*')
          .eq('user_id', user.id)
          .order('created_at', { ascending: sortOrder === 'oldest' });
        
        if (videosError) {
          throw videosError;
        }
        
        // Combine and filter based on mediaType
        let combinedMedia: MediaItem[] = [];
        
        if (mediaType === 'all' || mediaType === 'image') {
          combinedMedia = [...combinedMedia, ...images];
        }
        
        if (mediaType === 'all' || mediaType === 'video') {
          combinedMedia = [...combinedMedia, ...videos];
        }
        
        // Sort by created_at
        combinedMedia.sort((a, b) => {
          const dateA = new Date(a.created_at).getTime();
          const dateB = new Date(b.created_at).getTime();
          return sortOrder === 'latest' ? dateB - dateA : dateA - dateB;
        });
        
        setMediaItems(combinedMedia);
      } catch (err: any) {
        console.error('Error fetching media:', err);
        setError(`Failed to load media: ${err.message}`);
      } finally {
        setLoading(false);
      }
    }
    
    fetchMedia();
  }, [user, mediaType, sortOrder, supabase]);

  // Format the time elapsed since upload
  const formatTimeElapsed = (dateString: string) => {
    const uploadDate = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - uploadDate.getTime();
    
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    const diffMonths = Math.floor(diffDays / 30);
    
    if (diffMonths > 0) {
      return `${diffMonths} month${diffMonths !== 1 ? 's' : ''} ago`;
    } else if (diffDays > 0) {
      return `${diffDays} day${diffDays !== 1 ? 's' : ''} ago`;
    } else if (diffHours > 0) {
      return `${diffHours} hour${diffHours !== 1 ? 's' : ''} ago`;
    } else {
      return `${diffMins} minute${diffMins !== 1 ? 's' : ''} ago`;
    }
  };

  // Format video duration
  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  // Render status icon
  const renderStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-5 w-5 text-yellow-500" />;
      case 'processing':
        return <Loader2 className="h-5 w-5 text-blue-500 animate-spin" />;
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'failed':
        return <XCircle className="h-5 w-5 text-red-500" />;
      default:
        return null;
    }
  };

  return (
    <div className="space-y-6">
      {/* Filter tabs */}
      <div className="flex border-b border-gray-700">
        <button
          className={`px-4 py-2 font-medium ${mediaType === 'all' ? 'text-white border-b-2 border-blue-500' : 'text-gray-400 hover:text-white'}`}
          onClick={() => setMediaType('all')}
        >
          All
        </button>
        <button
          className={`px-4 py-2 font-medium ${mediaType === 'video' ? 'text-white border-b-2 border-blue-500' : 'text-gray-400 hover:text-white'}`}
          onClick={() => setMediaType('video')}
        >
          Video
        </button>
        <button
          className={`px-4 py-2 font-medium ${mediaType === 'image' ? 'text-white border-b-2 border-blue-500' : 'text-gray-400 hover:text-white'}`}
          onClick={() => setMediaType('image')}
        >
          Photo
        </button>
        
        <div className="ml-auto">
          <select
            value={sortOrder}
            onChange={(e) => setSortOrder(e.target.value as 'latest' | 'oldest')}
            className="bg-navy-light text-white border border-gray-700 rounded-md px-2 py-1 text-sm"
          >
            <option value="latest">Latest</option>
            <option value="oldest">Oldest</option>
          </select>
        </div>
      </div>
      
      {/* Error message */}
      {error && (
        <div className="bg-red-900/50 text-white p-3 rounded-md flex items-center space-x-2">
          <AlertCircle className="h-5 w-5" />
          <span>{error}</span>
        </div>
      )}
      
      {/* Loading state */}
      {loading && (
        <div className="flex justify-center items-center py-12">
          <Loader2 className="h-8 w-8 text-blue-500 animate-spin" />
          <span className="ml-2 text-gray-300">Loading your media...</span>
        </div>
      )}
      
      {/* Empty state */}
      {!loading && mediaItems.length === 0 && (
        <div className="text-center py-12">
          <p className="text-gray-400 mb-2">No media found</p>
          <p className="text-gray-500 text-sm">Upload some files to get started</p>
        </div>
      )}
      
      {/* Media grid */}
      {!loading && mediaItems.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {mediaItems.map((item) => {
            const isVideo = 'duration_seconds' in item;
            const fileName = item.metadata?.originalName || 'Unnamed file';
            
            return (
              <div key={`${isVideo ? 'video' : 'image'}-${item.id}`} className="bg-navy-light rounded-lg overflow-hidden">
                {/* Thumbnail */}
                <div className="relative aspect-video bg-navy-dark">
                  {item.processed_url ? (
                    <Image
                      src={item.processed_url}
                      alt={fileName}
                      fill
                      className="object-cover"
                    />
                  ) : (
                    <div className="absolute inset-0 flex items-center justify-center">
                      <p className="text-gray-500">Processing...</p>
                    </div>
                  )}
                  
                  {/* Video duration badge */}
                  {isVideo && item.duration_seconds && (
                    <div className="absolute bottom-2 right-2 bg-black/70 text-white text-xs px-1.5 py-0.5 rounded flex items-center">
                      <Play className="h-3 w-3 mr-1" />
                      {formatDuration(item.duration_seconds)}
                    </div>
                  )}
                  
                  {/* Status badge */}
                  <div className="absolute top-2 right-2">
                    {renderStatusIcon(item.status)}
                  </div>
                </div>
                
                {/* Info */}
                <div className="p-3">
                  <p className="text-white text-sm font-medium truncate">{fileName}</p>
                  <p className="text-gray-400 text-xs">{formatTimeElapsed(item.created_at)}</p>
                </div>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
}

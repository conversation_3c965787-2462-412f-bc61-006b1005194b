'use client';

import { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { useUser, auth } from '@clerk/nextjs';
import { useSupabaseClient } from '@/utils/supabase/client';
import { Upload, X, FileImage, FileVideo, AlertCircle } from 'lucide-react';
import Link from 'next/link';
import { useUserData } from '@/contexts/UserDataContext';
import { ProPlanProtection } from '@/components/auth/PlanProtection';

interface FileUploaderProps {
  onUploadComplete?: (fileData: any) => void;
}

interface FileWithStatus extends File {
  id?: string;
  status?: 'pending' | 'uploading' | 'processing' | 'completed' | 'error';
  uploadProgress?: number;
  error?: string;
}

export function FileUploader({ onUploadComplete }: FileUploaderProps) {
  const { user } = useUser();
  const supabase = useSupabaseClient();
  const { refreshUserData } = useUserData();
  const [files, setFiles] = useState<FileWithStatus[]>([]);
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const onDrop = useCallback((acceptedFiles: File[]) => {
    // Filter out unsupported file types
    const supportedFiles = acceptedFiles.filter(file => {
      const isImage = file.type.startsWith('image/');
      const isVideo = file.type.startsWith('video/');
      return isImage || isVideo;
    });

    if (supportedFiles.length !== acceptedFiles.length) {
      setError('Some files were rejected. Only images and videos are supported.');
    } else {
      setError(null);
    }

    // Add status to files
    const filesWithStatus: FileWithStatus[] = supportedFiles.map(file => ({
      ...file,
      id: `${Date.now()}-${Math.random().toString(36).substring(2, 15)}`,
      status: 'pending' as const,
      uploadProgress: 0,
    }));

    setFiles(prev => [...prev, ...filesWithStatus]);
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.gif'],
      'video/*': ['.mp4', '.mov', '.avi']
    },
    maxSize: 50 * 1024 * 1024, // 50MB
  });

  const removeFile = (index: number) => {
    setFiles(prev => prev.filter((_, i) => i !== index));
  };

  const uploadFiles = async () => {
    if (!user || files.length === 0) return;

    setUploading(true);
    setError(null);

    const uploadPromises = files.map(async (file) => {
      try {
        // Create a unique file name
        const fileExt = file.name.split('.').pop();
        const fileName = `${user.id}/${Date.now()}-${Math.random().toString(36).substring(2, 15)}.${fileExt}`;

        // Upload to Supabase Storage
        const { data, error } = await supabase.storage
          .from('original-uploads')
          .upload(fileName, file, {
            cacheControl: '3600',
            upsert: false,
            onUploadProgress: (progress) => {
              const progressPercent = Math.round((progress.loaded / progress.total) * 100);
              setFiles(prev => prev.map(f =>
                f.id === (file as FileWithStatus).id
                  ? { ...f, status: 'uploading' as const, uploadProgress: progressPercent }
                  : f
              ));
            }
          });

        if (error) {
          console.error('Error uploading file:', error);
          throw error;
        }

        // Get the public URL
        const { data: { publicUrl } } = supabase.storage
          .from('original-uploads')
          .getPublicUrl(fileName);

        // Determine file type
        const fileType = file.type.startsWith('image/') ? 'image' : 'video';

        // Insert record into database
        const { data: recordData, error: recordError } = await supabase
          .from(fileType === 'image' ? 'processed_images' : 'processed_videos')
          .insert({
            user_id: user.id,
            original_url: publicUrl,
            status: 'pending',
            credits_used: fileType === 'image' ? 1 : Math.ceil(file.size / (1024 * 1024 * 10)), // 1 credit per image, 1 credit per 10MB of video
            metadata: {
              originalName: file.name,
              size: file.size,
              type: file.type
            }
          })
          .select()
          .single();

        if (recordError) {
          console.error('Error creating database record:', recordError);
          throw recordError;
        }

        return { file, data, recordData };
      } catch (err: any) {
        console.error('Upload error:', err);
        setError(`Error uploading ${file.name}: ${err.message}`);
        return null;
      }
    });

    try {
      const results = await Promise.all(uploadPromises);
      const successfulUploads = results.filter(Boolean);

      if (successfulUploads.length > 0) {
        // Trigger processing for each uploaded file
        successfulUploads.forEach(async (upload: any) => {
          if (!upload) return;

          const mediaType = upload.file.type.startsWith('image/') ? 'image' : 'video';
          const mediaId = upload.recordData.id;

          try {
            // Call the process-media API to start processing
            await fetch('/api/process-media', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                mediaType,
                mediaId,
              }),
            });
          } catch (processError) {
            console.error('Error triggering media processing:', processError);
          }
        });

        // Update files to show processing status instead of clearing
        setFiles(prev => prev.map(file => ({
          ...file,
          status: 'processing' as const,
          uploadProgress: 100,
        })));

        // Clear files after processing completes (5 seconds)
        setTimeout(() => {
          setFiles(prev => prev.map(file => ({
            ...file,
            status: 'completed' as const,
          })));

          // Clear completely after showing completion (3 more seconds)
          setTimeout(() => {
            setFiles([]);
          }, 3000);
        }, 5000);

        // Refresh user data to update credits
        refreshUserData();

        if (onUploadComplete) {
          onUploadComplete(successfulUploads);
        }
      }
    } catch (err: any) {
      console.error('Error in upload process:', err);
      setError(`Upload process error: ${err.message}`);
    } finally {
      setUploading(false);
    }
  };

  return (
    <div className="w-full space-y-4">
      <div
        {...getRootProps()}
        className={`flex h-40 cursor-pointer flex-col items-center justify-center rounded-lg border-2 border-dashed ${
          isDragActive ? 'border-blue-500 bg-blue-500/10' : 'border-gray-600'
        } transition-colors`}
      >
        <input {...getInputProps()} />
        <div className="flex flex-col items-center justify-center space-y-2">
          <Upload className="h-6 w-6 text-gray-400" />
          <p className="text-center text-lg font-medium text-white">Drop photo or video files here</p>
          <p className="mt-1 text-center text-sm text-gray-400">
            <Link href="/dashboard/billing" className="text-orange-500 hover:underline">
              Upgrade
            </Link>{' '}
            to upload multiple files at once
          </p>
        </div>
      </div>

      {error && (
        <div className="flex items-center space-x-2 rounded-md bg-red-900/50 p-3 text-white">
          <AlertCircle className="h-5 w-5" />
          <span>{error}</span>
        </div>
      )}

      {files.length > 0 && (
        <div className="space-y-3">
          <h3 className="text-lg font-medium text-white">Files to upload ({files.length})</h3>
          <div className="space-y-2">
            {files.map((file, index) => {
              const isImage = file.type.startsWith('image/');
              const progress = file.uploadProgress || 0;
              const status = file.status || 'pending';

              const getStatusColor = () => {
                switch (status) {
                  case 'pending': return 'text-gray-400';
                  case 'uploading': return 'text-blue-400';
                  case 'processing': return 'text-yellow-400';
                  case 'completed': return 'text-green-400';
                  case 'error': return 'text-red-400';
                  default: return 'text-gray-400';
                }
              };

              const getStatusText = () => {
                switch (status) {
                  case 'pending': return 'Ready';
                  case 'uploading': return `${progress}%`;
                  case 'processing': return 'Processing...';
                  case 'completed': return 'Done ✓';
                  case 'error': return 'Error ✗';
                  default: return 'Unknown';
                }
              };

              return (
                <div key={file.id || index} className="flex items-center justify-between rounded-md bg-navy-light p-3">
                  <div className="flex items-center space-x-3">
                    {isImage ? (
                      <FileImage className="h-5 w-5 text-blue-400" />
                    ) : (
                      <FileVideo className="h-5 w-5 text-purple-400" />
                    )}
                    <div className="min-w-0 flex-1">
                      <p className="truncate text-sm font-medium text-white">{file.name}</p>
                      <p className="text-xs text-gray-400">{(file.size / (1024 * 1024)).toFixed(2)} MB</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className={`text-xs font-medium ${getStatusColor()}`}>
                      {getStatusText()}
                    </div>
                    {status === 'uploading' && (
                      <div className="w-12 bg-gray-700 rounded-full h-2">
                        <div
                          className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${progress}%` }}
                        ></div>
                      </div>
                    )}
                    {status === 'processing' && (
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-yellow-400"></div>
                    )}
                    {(status === 'pending' || status === 'error') && (
                      <button
                        onClick={() => removeFile(index)}
                        className="text-gray-400 hover:text-red-500"
                        disabled={uploading}
                      >
                        <X className="h-5 w-5" />
                      </button>
                    )}
                  </div>
                </div>
              );
            })}
          </div>

          {files.length > 1 ? (
            <ProPlanProtection fallbackMessage="Multiple file uploads require the Pro plan or higher. Upgrade to upload multiple files at once.">
              <button
                onClick={uploadFiles}
                disabled={uploading || files.length === 0}
                className="w-full rounded-md bg-orange-500 py-2 px-4 font-medium text-white transition-colors hover:bg-orange-600 disabled:cursor-not-allowed disabled:opacity-50"
              >
                {uploading ? 'Uploading...' : `Upload ${files.length} files`}
              </button>
            </ProPlanProtection>
          ) : (
            <button
              onClick={uploadFiles}
              disabled={uploading || files.length === 0}
              className="w-full rounded-md bg-orange-500 py-2 px-4 font-medium text-white transition-colors hover:bg-orange-600 disabled:cursor-not-allowed disabled:opacity-50"
            >
              {uploading ? 'Uploading...' : `Upload ${files.length} file`}
            </button>
          )}
        </div>
      )}
    </div>
  );
}

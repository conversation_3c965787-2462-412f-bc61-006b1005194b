"use client"

import { useState, useEffect, useRef } from "react"
import { Card, CardContent } from "@/components/ui/Card"
import { Slider } from "@/components/ui/Slider"

interface BeforeAfterComparisonSliderProps {
  originalImage: string
  blurredImage: string
}

export function BeforeAfterComparisonSlider({
  originalImage,
  blurredImage
}: BeforeAfterComparisonSliderProps) {
  const [sliderValue, setSliderValue] = useState(50)
  // Commented out unused state
  // const [isMounted, setIsMounted] = useState(false)
  const [isDragging, setIsDragging] = useState(false)
  const imageContainerRef = useRef<HTMLDivElement>(null)

  // Commented out unused effect
  // useEffect(() => {
  //   setIsMounted(true)
  // }, [])

  const handleMouseDown = () => {
    setIsDragging(true)
  }

  const handleMouseUp = () => {
    setIsDragging(false)
  }

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!isDragging || !imageContainerRef.current) return

    const rect = imageContainerRef.current.getBoundingClientRect()
    const x = e.clientX - rect.left
    const newValue = (x / rect.width) * 100

    // Clamp the value between 0 and 100
    setSliderValue(Math.max(0, Math.min(100, newValue)))
  }

  const handleTouchStart = () => {
    setIsDragging(true)
  }

  const handleTouchEnd = () => {
    setIsDragging(false)
  }

  // Completely rewritten to avoid type errors
  const handleTouchMove = (e: React.TouchEvent<HTMLDivElement>) => {
    if (!isDragging || !imageContainerRef.current) return

    // Safely access touch data
    const touch = e.touches?.[0]
    if (!touch) return

    const rect = imageContainerRef.current.getBoundingClientRect()
    const x = touch.clientX - rect.left
    const newValue = (x / rect.width) * 100

    // Clamp the value between 0 and 100
    setSliderValue(Math.max(0, Math.min(100, newValue)))
  }

  useEffect(() => {
    const handleGlobalMouseUp = () => {
      setIsDragging(false)
    }

    const handleGlobalMouseMove = (e: MouseEvent) => {
      if (isDragging && imageContainerRef.current) {
        const rect = imageContainerRef.current.getBoundingClientRect()
        const x = e.clientX - rect.left
        const newValue = (x / rect.width) * 100

        // Clamp the value between 0 and 100
        setSliderValue(Math.max(0, Math.min(100, newValue)))
      }
    }

    window.addEventListener('mouseup', handleGlobalMouseUp)
    window.addEventListener('mousemove', handleGlobalMouseMove)

    return () => {
      window.removeEventListener('mouseup', handleGlobalMouseUp)
      window.removeEventListener('mousemove', handleGlobalMouseMove)
    }
  }, [isDragging])

  return (
    <div className="mx-auto mt-16 max-w-4xl">
      <Card className="overflow-hidden border-navy-light bg-navy shadow-xl">
        <CardContent className="p-0">
          <div
            ref={imageContainerRef}
            className="relative aspect-video w-full overflow-hidden cursor-ew-resize"
            onMouseDown={handleMouseDown}
            onMouseUp={handleMouseUp}
            onMouseMove={handleMouseMove}
            onTouchStart={handleTouchStart}
            onTouchEnd={handleTouchEnd}
            onTouchMove={handleTouchMove}
          >
            <div className="absolute inset-0">
              {/* Original image (right side) */}
              <img
                src={originalImage}
                alt="Original content"
                className="h-full w-full object-cover"
              />
            </div>

            <div className="absolute inset-0 overflow-hidden" style={{ width: `${sliderValue}%` }}>
              {/* Blurred image (left side) */}
              <div className="h-full" style={{ width: `${100 / (sliderValue / 100)}%` }}>
                <img
                  src={blurredImage}
                  alt="Blurred content"
                  className="h-full w-full object-cover"
                />
              </div>
            </div>

            {/* Divider line */}
            <div
              className="absolute bottom-0 top-0 w-1 cursor-ew-resize bg-white shadow-[0_0_10px_rgba(0,0,0,0.3)]"
              style={{ left: `${sliderValue}%` }}
              onMouseDown={handleMouseDown}
              onTouchStart={handleTouchStart}
            >
              <div
                className="absolute left-1/2 top-1/2 flex h-10 w-10 -translate-x-1/2 -translate-y-1/2 items-center justify-center rounded-full border-4 border-white bg-green-400 shadow-lg"
                onMouseDown={(e) => {
                  e.stopPropagation();
                  handleMouseDown();
                }}
                onTouchStart={(e) => {
                  e.stopPropagation();
                  handleTouchStart();
                }}
              >
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M8 5L3 10L8 15M16 5L21 10L16 15"
                    stroke="#0a192f"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              </div>
            </div>
          </div>

          <div className="bg-navy-light p-6">
            <Slider
              value={[sliderValue]}
              onValueChange={(value) => {
                if (value && value.length > 0 && typeof value[0] === 'number') {
                  setSliderValue(value[0])
                }
              }}
              min={0}
              max={100}
              step={1}
              className="py-4"
            />
            <div className="mt-2 flex justify-between text-sm text-gray-300">
              <span>Protected Content</span>
              <span>Original Content</span>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="mt-8 flex flex-wrap justify-center gap-4 text-center">
        <div className="min-w-[180px] rounded-lg bg-navy-light p-4">
          <div className="text-2xl font-bold text-green-400">99.8%</div>
          <div className="text-sm text-gray-300">Detection Accuracy</div>
        </div>
        <div className="min-w-[180px] rounded-lg bg-navy-light p-4">
          <div className="text-2xl font-bold text-green-400">&lt;50ms</div>
          <div className="text-sm text-gray-300">Processing Time</div>
        </div>
        <div className="min-w-[180px] rounded-lg bg-navy-light p-4">
          <div className="text-2xl font-bold text-green-400">GDPR</div>
          <div className="text-sm text-gray-300">Compliant</div>
        </div>
      </div>
    </div>
  )
}

'use client';

import { useEffect, useState } from 'react';
import { useSession, useUser } from '@clerk/nextjs';
import { createClient } from '@supabase/supabase-js';
import { useSupabaseClient, createClerkSupabaseClient } from '@/utils/supabase/client';

export function ClerkSupabaseClientExample() {
  const [userData, setUserData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const { user } = useUser();
  const { session } = useSession();
  
  // Method 1: Using our custom hook
  const supabase = useSupabaseClient();
  
  // Method 2: Using the function directly from the docs
  function createDirectSupabaseClient() {
    return createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        async accessToken() {
          return session?.getToken() ?? null;
        },
      }
    );
  }
  
  // Create a client using the direct approach
  const directClient = createDirectSupabaseClient();
  
  // This useEffect will wait for the User object to be loaded before requesting
  // the user data from Supabase
  useEffect(() => {
    if (!user) return;
    
    async function loadUserData() {
      setLoading(true);
      
      // Method 1: Using our custom hook
      const { data, error } = await supabase
        .from('users')
        .select('id, credits, subscription_type')
        .eq('id', user.id)
        .single();
      
      if (!error && data) {
        setUserData(data);
      } else {
        console.error('Error fetching user data with custom hook:', error);
        
        // Method 2: Try with the direct client as fallback
        const { data: directData, error: directError } = await directClient
          .from('users')
          .select('id, credits, subscription_type')
          .eq('id', user.id)
          .single();
        
        if (!directError && directData) {
          setUserData(directData);
        } else {
          console.error('Error fetching user data with direct client:', directError);
        }
      }
      
      setLoading(false);
    }
    
    loadUserData();
  }, [user, supabase, directClient]);
  
  if (loading) {
    return <div>Loading user data...</div>;
  }
  
  if (!userData) {
    return <div>No user data found. Make sure RLS policies are configured correctly.</div>;
  }
  
  return (
    <div className="p-4 bg-gray-800 rounded-lg">
      <h2 className="text-xl font-bold mb-4">Client-Side Clerk-Supabase Integration</h2>
      <div className="space-y-2">
        <p><span className="font-medium">User ID:</span> {userData.id}</p>
        <p><span className="font-medium">Credits:</span> {userData.credits}</p>
        <p><span className="font-medium">Subscription:</span> {userData.subscription_type}</p>
      </div>
    </div>
  );
}

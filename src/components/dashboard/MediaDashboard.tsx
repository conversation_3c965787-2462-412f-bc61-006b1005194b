'use client';

import { useState, useEffect } from 'react';
import { useUser } from '@clerk/nextjs';
import { useSupabaseClient } from '@/utils/supabase/client';
import { useUserData } from '@/contexts/UserDataContext';
import { FileUploader } from '@/components/uploads/FileUploader';
import { Clock, ChevronDown, Upload, RefreshCw } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';

type MediaType = 'all' | 'video' | 'photo';

interface MediaItem {
  id: number;
  user_id: string;
  original_url: string;
  processed_url: string | null;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  credits_used: number;
  created_at: string;
  metadata: any;
  duration_seconds?: number;
}

export function MediaDashboard() {
  const { user } = useUser();
  const supabase = useSupabaseClient();
  const { credits, subscriptionType, isLoading, refreshUserData } = useUserData();
  const [mediaType, setMediaType] = useState<MediaType>('all');
  const [mediaItems, setMediaItems] = useState<MediaItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [sortOrder, setSortOrder] = useState<'latest' | 'oldest'>('latest');
  const [isDragging, setIsDragging] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  // Use the credits and subscriptionType from the context
  console.log('MediaDashboard rendering with user data:', { credits, subscriptionType, isLoading });

  // Function to fetch media data
  const fetchMediaData = async () => {
    if (!user) return;

    setLoading(true);

    try {
      // Fetch images
      const { data: images, error: imagesError } = await supabase
        .from('processed_images')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: sortOrder === 'oldest' });

      // Fetch videos
      const { data: videos, error: videosError } = await supabase
        .from('processed_videos')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: sortOrder === 'oldest' });

      // Combine and filter based on mediaType
      let combinedMedia: MediaItem[] = [];

      if (mediaType === 'all' || mediaType === 'photo') {
        combinedMedia = [...combinedMedia, ...(images || [])];
      }

      if (mediaType === 'all' || mediaType === 'video') {
        combinedMedia = [...combinedMedia, ...(videos || [])];
      }

      // Sort by created_at
      combinedMedia.sort((a, b) => {
        const dateA = new Date(a.created_at).getTime();
        const dateB = new Date(b.created_at).getTime();
        return sortOrder === 'latest' ? dateB - dateA : dateA - dateB;
      });

      setMediaItems(combinedMedia);
    } catch (err) {
      console.error('Error fetching media data:', err);
    } finally {
      setLoading(false);
    }
  };

  // Function to refresh all data
  const refreshAllData = async () => {
    if (!user) return;

    setRefreshing(true);

    try {
      // Refresh user data first
      await refreshUserData();

      // Then refresh media data
      await fetchMediaData();
    } catch (err) {
      console.error('Error refreshing data:', err);
    } finally {
      setRefreshing(false);
    }
  };

  // Fetch media on initial load and when filters change
  useEffect(() => {
    fetchMediaData();
  }, [user, mediaType, sortOrder, supabase]);

  // Handle upload completion
  const handleUploadComplete = () => {
    // Refresh the media items and user data
    if (user) {
      // Use the refreshAllData function to update both user data and media items
      refreshAllData();
    }
  };

  // Format time elapsed
  const formatTimeElapsed = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    const diffMonths = Math.floor(diffDays / 30);

    if (diffMonths > 0) {
      return `${diffMonths} month${diffMonths !== 1 ? 's' : ''} ago`;
    } else if (diffDays > 0) {
      return `${diffDays} day${diffDays !== 1 ? 's' : ''} ago`;
    } else {
      const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
      if (diffHours > 0) {
        return `${diffHours} hour${diffHours !== 1 ? 's' : ''} ago`;
      } else {
        const diffMinutes = Math.floor(diffMs / (1000 * 60));
        return `${diffMinutes} minute${diffMinutes !== 1 ? 's' : ''} ago`;
      }
    }
  };

  // Format video duration
  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  if (!user) {
    return null; // Don't render anything if user is not logged in
  }

  return (
    <div className="flex h-full flex-col">
      <div className="mb-4 flex items-center justify-between">
        <h1 className="text-xl font-bold text-white">All</h1>

        {/* Refresh button */}
        <button
          onClick={refreshAllData}
          disabled={refreshing || loading}
          className="group flex items-center rounded-md bg-gradient-to-r from-purple-500/10 to-indigo-500/10 px-3 py-1.5 text-sm text-purple-300 transition-all duration-300 hover:from-purple-500/20 hover:to-indigo-500/20 hover:text-purple-200 hover:shadow-sm hover:shadow-purple-500/10"
          title="Sync data"
        >
          <svg
            className={`mr-2 h-4 w-4 transition-transform duration-700 ${refreshing ? 'animate-spin' : 'group-hover:rotate-180'}`}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M21.1679 8C19.6247 4.46819 16.1006 2 11.9999 2C6.81459 2 2.55104 5.94668 2.04932 11"
              stroke="currentColor"
              strokeWidth="1.5"
              strokeLinecap="round"
            />
            <path
              d="M17 8H21.4C21.7314 8 22 7.73137 22 7.4V3"
              stroke="currentColor"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M2.88146 16C4.42458 19.5318 7.94874 22 12.0494 22C17.2347 22 21.4983 18.0533 22 13"
              stroke="currentColor"
              strokeWidth="1.5"
              strokeLinecap="round"
            />
            <path
              d="M7.04932 16H2.64932C2.31795 16 2.04932 16.2686 2.04932 16.6V21"
              stroke="currentColor"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
          <span className="transition-all duration-300 group-hover:tracking-wide">Sync</span>
        </button>
      </div>

      {/* Upload area with actual FileUploader */}
      <div className="mb-6">
        <FileUploader onUploadComplete={handleUploadComplete} />
      </div>

      {/* Media type tabs */}
      <div className="mb-4 flex border-b border-gray-700">
        <button
          className={`px-6 py-2 text-sm font-medium ${
            mediaType === 'all' ? 'border-b-2 border-white text-white' : 'text-gray-400 hover:text-white'
          }`}
          onClick={() => setMediaType('all')}
        >
          All
        </button>
        <button
          className={`px-6 py-2 text-sm font-medium ${
            mediaType === 'video' ? 'border-b-2 border-white text-white' : 'text-gray-400 hover:text-white'
          }`}
          onClick={() => setMediaType('video')}
        >
          Video
        </button>
        <button
          className={`px-6 py-2 text-sm font-medium ${
            mediaType === 'photo' ? 'border-b-2 border-white text-white' : 'text-gray-400 hover:text-white'
          }`}
          onClick={() => setMediaType('photo')}
        >
          Photo
        </button>

        {/* Sort dropdown */}
        <div className="ml-auto flex items-center">
          <button
            className="flex items-center space-x-1 text-sm text-white"
            onClick={() => setSortOrder(sortOrder === 'latest' ? 'oldest' : 'latest')}
          >
            <span>{sortOrder === 'latest' ? 'Latest' : 'Oldest'}</span>
            <ChevronDown className="h-4 w-4" />
          </button>
        </div>
      </div>

      {/* Media grid */}
      {loading ? (
        <div className="flex h-40 items-center justify-center">
          <div className="text-center text-gray-400">Loading...</div>
        </div>
      ) : mediaItems.length === 0 ? (
        <div className="flex h-40 items-center justify-center">
          <div className="text-center text-gray-400">No media found</div>
        </div>
      ) : (
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
          {mediaItems.map((item) => {
            const isVideo = 'duration_seconds' in item;
            const fileName = item.metadata?.originalName || 'Unnamed file';

            return (
              <div key={`${isVideo ? 'video' : 'image'}-${item.id}`} className="overflow-hidden rounded-lg">
                {/* Thumbnail */}
                <div className="relative aspect-video bg-gray-800">
                  {item.processed_url ? (
                    <Image
                      src={item.processed_url}
                      alt={fileName}
                      fill
                      className="object-cover"
                    />
                  ) : (
                    <div className="absolute inset-0 flex items-center justify-center">
                      <Clock className="h-8 w-8 text-gray-500" />
                    </div>
                  )}

                  {/* Video duration badge */}
                  {isVideo && item.duration_seconds && (
                    <div className="absolute bottom-2 right-2 rounded bg-black/70 px-1.5 py-0.5 text-xs text-white">
                      {formatDuration(item.duration_seconds)}
                    </div>
                  )}
                </div>

                {/* File info */}
                <div className="mt-2">
                  <p className="truncate text-sm font-medium text-white">{fileName}</p>
                  <p className="text-xs text-gray-400">{formatTimeElapsed(item.created_at)}</p>
                </div>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
}

'use client';

import { useState } from 'react';
import { useUserData } from '@/contexts/UserDataContext';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

export function RefreshButton() {
  const { refreshUserData } = useUserData();
  const [isRefreshing, setIsRefreshing] = useState(false);

  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await refreshUserData();
    } catch (error) {
      console.error('Error refreshing user data:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <button
            onClick={handleRefresh}
            disabled={isRefreshing}
            className="group h-8 w-8 rounded-full bg-gradient-to-r from-purple-500/20 to-indigo-500/20 p-1.5 text-purple-300 transition-all duration-300 hover:from-purple-500/30 hover:to-indigo-500/30 hover:text-purple-200 hover:shadow-sm hover:shadow-purple-500/20"
          >
            <svg
              className={`h-full w-full transition-transform duration-700 ${isRefreshing ? 'animate-spin' : 'group-hover:rotate-180'}`}
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M21.1679 8C19.6247 4.46819 16.1006 2 11.9999 2C6.81459 2 2.55104 5.94668 2.04932 11"
                stroke="currentColor"
                strokeWidth="1.5"
                strokeLinecap="round"
              />
              <path
                d="M17 8H21.4C21.7314 8 22 7.73137 22 7.4V3"
                stroke="currentColor"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <path
                d="M2.88146 16C4.42458 19.5318 7.94874 22 12.0494 22C17.2347 22 21.4983 18.0533 22 13"
                stroke="currentColor"
                strokeWidth="1.5"
                strokeLinecap="round"
              />
              <path
                d="M7.04932 16H2.64932C2.31795 16 2.04932 16.2686 2.04932 16.6V21"
                stroke="currentColor"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
            <span className="sr-only">Sync data</span>
          </button>
        </TooltipTrigger>
        <TooltipContent>
          <p>Sync data</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}

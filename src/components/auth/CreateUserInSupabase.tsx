'use client';

import { useEffect, useState } from 'react';
import { useUser } from '@clerk/nextjs';
import { createUserInSupabase } from '@/app/actions';

/**
 * This component automatically creates a user in Supabase when a user signs up with Clerk
 * It should be added to the layout component that wraps the entire application
 */
export function CreateUserInSupabase() {
  const { user, isLoaded } = useUser();
  const [isCreatingUser, setIsCreatingUser] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  useEffect(() => {
    // Only run this effect when the user is loaded and exists
    if (!isLoaded || !user || isCreatingUser || success) return;

    async function syncUserToSupabase() {
      setIsCreatingUser(true);
      setError(null);

      try {
        console.log('Creating user in Supabase via server action:', user.id);
        console.log('User details:', {
          id: user.id,
          email: user.primaryEmailAddress?.emailAddress,
          username: user.username,
          firstName: user.firstName,
          lastName: user.lastName
        });

        // Call the server action to create the user
        const result = await createUserInSupabase(
          user.id,
          user.primaryEmailAddress?.emailAddress || '',
          user.username || '',
          user.firstName || '',
          user.lastName || '',
          user.imageUrl || '',
          'Free' // Add subscription_type parameter
        );

        if (!result.success) {
          console.error('Error from server action:', result.error);
          setError(result.error || 'Unknown error');
          setIsCreatingUser(false);
          return;
        }

        console.log('User created in Supabase:', result.message);
        setSuccess(true);
        setIsCreatingUser(false);
      } catch (err: any) {
        console.error('Error creating user in Supabase:', err);
        setError(err.message || 'Unknown error');
        setIsCreatingUser(false);
      }
    }

    syncUserToSupabase();
  }, [user, isLoaded, isCreatingUser, success]);

  // This component doesn't render anything visible
  return null;
}

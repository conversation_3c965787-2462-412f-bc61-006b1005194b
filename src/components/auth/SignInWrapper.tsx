'use client';

import { SignIn } from '@clerk/nextjs';
import Link from 'next/link';

interface SignInWrapperProps {
  path: string;
  redirectUrl: string;
  afterSignInUrl: string;
  signUpUrl: string;
}

export function SignInWrapper({ path, redirectUrl, afterSignInUrl, signUpUrl }: SignInWrapperProps) {
  return (
    <div className="relative">
      <style jsx global>{`
        /* Force all text to be light by default */
        .cl-card * {
          color: #e2e8f0 !important;
        }

        /* Override specific elements that need different colors */
        .cl-formButtonPrimary {
          color: white !important;
        }

        .cl-socialButtonsBlockButtonText {
          color: white !important;
        }

        .cl-formFieldAction,
        .cl-formResendCodeLink,
        .cl-formFieldActionLink,
        .cl-formFieldHintLink,
        .cl-alertButtonText,
        .cl-formFieldRow__termsAndPrivacy a {
          color: #22C55E !important;
        }
        /* Hide Clerk branding in this specific page */
        .cl-footerAction,
        .cl-footerPages,
        .cl-footer,
        .cl-internal-1xpzp24,
        .cl-internal-wkkub3,
        .cl-internal-1d87bz4,
        .cl-internal-13zhnx0,
        .cl-internal-1hdbmrz,
        .cl-internal-m3fgp9,
        .cl-internal-1bpxb4s,
        .cl-internal-1ck0y9s {
          display: none !important;
          visibility: hidden !important;
          opacity: 0 !important;
        }

        /* Improve form field visibility */
        .cl-formFieldInput {
          background-color: rgba(17, 34, 64, 0.9) !important;
          border: 1px solid rgba(59, 130, 246, 0.5) !important;
          color: white !important;
          box-shadow: 0 0 10px rgba(59, 130, 246, 0.1) !important;
        }

        .cl-formFieldInput:focus {
          border-color: #22C55E !important;
          box-shadow: 0 0 0 2px rgba(34, 197, 94, 0.3) !important;
        }

        /* Improve text readability */
        .cl-headerTitle {
          color: white !important;
          font-weight: bold !important;
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important;
        }

        .cl-headerSubtitle {
          color: #94a3b8 !important;
          text-shadow: 0 1px 1px rgba(0, 0, 0, 0.1) !important;
        }

        .cl-formFieldLabel {
          color: #e2e8f0 !important;
          font-weight: 500 !important;
        }

        /* Fix dark text on dark background issues */
        .cl-formFieldInfoText,
        .cl-formFieldSuccessText,
        .cl-formFieldWarningText,
        .cl-formFieldErrorText,
        .cl-formResendCodeLink,
        .cl-formFieldActionText,
        .cl-formFieldHintText,
        .cl-identityPreviewText,
        .cl-identityPreviewEditButtonText,
        .cl-otpCodeFieldInput,
        .cl-alertText,
        .cl-alertButtonText,
        .cl-main p,
        .cl-main span,
        .cl-main div,
        .cl-main label {
          color: #e2e8f0 !important;
        }

        /* Ensure links are visible */
        .cl-formFieldAction,
        .cl-formResendCodeLink,
        .cl-formFieldActionLink,
        .cl-formFieldHintLink,
        .cl-alertButtonText {
          color: #22C55E !important;
        }

        /* Ensure checkboxes and radio buttons have visible text */
        .cl-formCheckboxLabel,
        .cl-formRadioLabel {
          color: #e2e8f0 !important;
        }

        /* Make buttons more visible */
        .cl-formButtonPrimary {
          background-color: #22C55E !important;
          box-shadow: 0 4px 6px rgba(34, 197, 94, 0.25) !important;
          font-weight: 600 !important;
        }

        .cl-formButtonPrimary:hover {
          background-color: #4ADE80 !important;
          transform: translateY(-1px) !important;
        }

        /* Improve social buttons */
        .cl-socialButtonsBlockButton {
          background-color: rgba(17, 34, 64, 0.9) !important;
          border: 1px solid rgba(59, 130, 246, 0.5) !important;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
          color: white !important;
        }

        .cl-socialButtonsBlockButton:hover {
          background-color: rgba(30, 58, 111, 0.9) !important;
          transform: translateY(-1px) !important;
        }

        /* Ensure Google button text is white */
        .cl-socialButtonsBlockButtonText {
          color: white !important;
        }

        /* Hide the default footer action (Don't have an account) */
        .cl-footerActionText, .cl-footerActionLink {
          display: none !important;
        }

        /* Add a subtle glow to the card */
        .cl-card {
          box-shadow: 0 0 20px rgba(59, 130, 246, 0.1) !important;
        }
      `}</style>

      {/* Clerk SignIn Component */}
      <div className="rounded-lg border-2 border-[#1e3a6f] bg-[#0a192f]/95 shadow-lg">
        <SignIn
          path={path}
          redirectUrl={redirectUrl}
          afterSignInUrl={afterSignInUrl}
          socialButtonsPlacement="bottom"
          socialButtonsVariant="blockButton"
          signUpUrl={signUpUrl}
          unsafeMetadata={{
            customSocialProviderStrategies: ['oauth_google']
          }}
        />
      </div>

      {/* Custom "Don't have an account" section */}
      <div className="mt-6 rounded-lg border border-[#1e3a6f] bg-[#112240]/80 p-4 text-center shadow-md backdrop-blur-sm">
        <p className="text-[#e2e8f0] text-xs">
          Don't have an account?{' '}
          <Link
            href={signUpUrl}
            className="font-medium text-[#22C55E] transition-colors hover:text-[#4ADE80] hover:underline"
          >
            Sign up
          </Link>
          {' '}and get 50 free credits
        </p>
      </div>
    </div>
  );
}

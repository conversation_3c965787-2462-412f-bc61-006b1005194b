'use client';

import { Protect } from '@clerk/nextjs';
import { ReactNode } from 'react';
import Link from 'next/link';

interface PlanProtectionProps {
  plan: 'standard' | 'pro' | 'premium';
  children: ReactNode;
  fallbackMessage?: string;
}

export function PlanProtection({ plan, children, fallbackMessage }: PlanProtectionProps) {
  const defaultMessage = `This feature requires the ${plan.charAt(0).toUpperCase() + plan.slice(1)} plan or higher.`;
  
  return (
    <Protect
      plan={plan}
      fallback={
        <div className="rounded-lg border border-yellow-500/20 bg-yellow-500/10 p-6 text-center">
          <div className="mx-auto mb-4 h-12 w-12 rounded-full bg-yellow-500/20 flex items-center justify-center">
            <svg className="h-6 w-6 text-yellow-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m0 0v2m0-2h2m-2 0H10m2-6V9m0 0V7m0 2h2m-2 0H10" />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-white mb-2">Upgrade Required</h3>
          <p className="text-gray-300 mb-4">
            {fallbackMessage || defaultMessage}
          </p>
          <Link
            href="/dashboard/billing"
            className="inline-flex items-center rounded-md bg-gradient-to-r from-purple-500 to-indigo-500 px-4 py-2 text-sm font-medium text-white hover:from-purple-600 hover:to-indigo-600 transition-all duration-300"
          >
            Upgrade Now
          </Link>
        </div>
      }
    >
      {children}
    </Protect>
  );
}

// Specific plan protection components for easier use
export function StandardPlanProtection({ children, fallbackMessage }: { children: ReactNode; fallbackMessage?: string }) {
  return (
    <PlanProtection plan="standard" fallbackMessage={fallbackMessage}>
      {children}
    </PlanProtection>
  );
}

export function ProPlanProtection({ children, fallbackMessage }: { children: ReactNode; fallbackMessage?: string }) {
  return (
    <PlanProtection plan="pro" fallbackMessage={fallbackMessage}>
      {children}
    </PlanProtection>
  );
}

export function PremiumPlanProtection({ children, fallbackMessage }: { children: ReactNode; fallbackMessage?: string }) {
  return (
    <PlanProtection plan="premium" fallbackMessage={fallbackMessage}>
      {children}
    </PlanProtection>
  );
}

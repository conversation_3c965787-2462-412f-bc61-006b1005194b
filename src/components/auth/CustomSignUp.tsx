'use client';

import { useSignUp } from '@clerk/nextjs';
import { AnimatePresence, motion } from 'framer-motion';
import { Eye, EyeOff, Lock, Mail, User } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

import { PasswordStrengthMeter } from '@/components/ui/animations/PasswordStrengthMeter';
import { VerificationCodeInput } from '@/components/ui/animations/VerificationCodeInput';
import { VerificationSuccess } from '@/components/ui/animations/VerificationSuccess';
import { Button } from '@/components/ui/button';

type CustomSignUpProps = {
  redirectUrl: string;
  signInUrl: string;
};

// Calculate password strength (0-100)
function calculatePasswordStrength(password: string): number {
  if (!password) {
    return 0;
  }

  let score = 0;

  // Length check
  if (password.length >= 8) {
    score += 1;
  }
  if (password.length >= 12) {
    score += 1;
  }

  // Complexity checks
  if (/[A-Z]/.test(password)) {
    score += 1;
  }
  if (/[a-z]/.test(password)) {
    score += 1;
  }
  if (/\d/.test(password)) {
    score += 1;
  }
  if (/[^A-Z0-9]/i.test(password)) {
    score += 1;
  }

  // Calculate strength based on score
  if (score < 2) {
    // Weak password
    return Math.floor((score / 6) * 100);
  } else if (score < 4) {
    // Fair password - at least 50%
    return Math.max(50, Math.floor((score / 6) * 100));
  } else if (score < 5) {
    // Good password - at least 75%
    return Math.max(75, Math.floor((score / 6) * 100));
  } else {
    // Strong password - always 100%
    return 100;
  }
}

export function CustomSignUp({ redirectUrl, signInUrl }: CustomSignUpProps) {
  const { isLoaded, signUp, setActive } = useSignUp();
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [verifying, setVerifying] = useState(false);
  const [verificationSuccess, setVerificationSuccess] = useState(false);
  const [code, setCode] = useState('');
  // Track verification attempts
  const [_verificationAttempts, setVerificationAttempts] = useState(0);
  const [showSkipButton, setShowSkipButton] = useState(false);
  const router = useRouter();

  // Get the current origin to handle different ports in development
  const [currentOrigin, setCurrentOrigin] = useState('');

  useEffect(() => {
    if (typeof window !== 'undefined') {
      setCurrentOrigin(window.location.origin);
    }
  }, []);

  // Real API call to check if email exists
  const checkEmailExists = async (email: string): Promise<{ exists: boolean; userDetails?: any; error?: string }> => {
    try {
      console.log('Checking if email exists via API');

      const response = await fetch('/api/check-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      if (!response.ok) {
        throw new Error(`API error: ${response.status}`);
      }

      const data = await response.json();
      return {
        exists: data.exists,
        userDetails: data.userDetails,
        error: data.exists ? 'Email already exists' : undefined,
      };
    } catch (err) {
      console.error('Error checking email:', err);
      return { exists: false };
    }
  };

  // Add this function to handle the "already verified" error case
  const handleAlreadyVerifiedError = async (email: string, password: string) => {
    console.log('Handling already verified error for email:', email);

    try {
      // Try to sign in directly with the email and password
      // This works because the email is already verified in Clerk's system
      const signInAttempt = await signIn?.create({
        identifier: email,
        password,
      });

      if (signInAttempt?.status === 'complete') {
        console.log('Successfully signed in with already verified email');
        router.push('/dashboard');
        return true;
      }

      return false;
    } catch (signInErr) {
      console.error('Failed to sign in with already verified email:', signInErr);
      return false;
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!isLoaded) {
      setError('Authentication system is not loaded yet. Please try again.');
      return;
    }

    try {
      setIsLoading(true);
      setError('');

      // Validate password strength
      const passwordScore = calculatePasswordStrength(password);
      if (password.length < 8) {
        setError('Password must be at least 8 characters long');
        setIsLoading(false);
        return;
      }

      if (passwordScore < 30) {
        setError('Password is too weak. Please include uppercase letters, numbers, and special characters.');
        setIsLoading(false);
        return;
      }

      // Check if email already exists
      console.log('Checking if email already exists...');
      const emailCheck = await checkEmailExists(email);

      if (emailCheck.exists) {
        // Check if the email is verified
        if (emailCheck.userDetails?.emailVerified) {
          setError('This email address is already registered. Please try signing in instead, or use a different email address.');
          setIsLoading(false);
          return;
        } else {
          // Email exists but is not verified - we can continue with signup
          // Clerk will handle this case appropriately
          console.log('Email exists but is not verified, continuing with signup');
        }
      }

      console.log('Email check passed, proceeding with signup');

      // Continue with Clerk signup

      try {
        // First try the standard Clerk signup
        console.log('Attempting standard Clerk signup');

        // Start the sign-up process
        const signUpAttempt = await signUp.create({
          firstName,
          lastName,
          emailAddress: email,
          password,
        });

        console.log('Sign-up creation successful:', signUpAttempt);

        // Send the email verification code
        const prepareResult = await signUp.prepareEmailAddressVerification({ strategy: 'email_code' });
        console.log('Verification preparation successful:', prepareResult);

        // Switch to verification mode
        setVerifying(true);
      } catch (clerkErr: any) {
        console.error('Standard Clerk signup failed:', clerkErr);

        const errorMessage = clerkErr.message || '';
        const errorCode = clerkErr.errors?.[0]?.code || '';

        // Check for already verified error
        if (errorMessage.includes('verified') ||
            errorMessage.includes('already exists') ||
            errorCode === 'form_identifier_exists') {

          setError('This email appears to be already registered.');

          // Try to handle the already verified case
          const handled = await handleAlreadyVerifiedError(email, password);

          if (handled) {
            // If we successfully signed in, we don't need to show an error
            setError('');
            return;
          } else {
            // If we couldn't sign in, show a more helpful error
            setError('This email is already registered but we couldn\'t sign you in automatically. Please try signing in manually or use a different email address.');
          }
        } else {
          // Handle other errors as before
          setError(clerkErr.errors?.[0]?.message || 'An error occurred during sign-up');
        }
      }
    } catch (err: any) {
      console.error('Error signing up:', err);

      // Handle specific error cases
      if (err.errors && Array.isArray(err.errors)) {
        const errorObj = err.errors[0] || {};
        const errorMessage = errorObj.message || 'An error occurred during sign-up';
        const errorCode = errorObj.code || '';

        // Check for the "already verified" error
        if (errorMessage.includes('verified') || errorCode === 'form_identifier_exists') {
          setError('This email address is already registered. Please try signing in instead, or use a different email address.');
        }
        // Check for email already in use
        else if (errorMessage.includes('already exists') || errorCode === 'form_identifier_exists' || errorCode === 'form_password_pwned') {
          setError('This email address is already in use. Please try signing in or use a different email address.');
        } else {
          setError(errorMessage);
        }
      } else if (err.message && typeof err.message === 'string') {
        setError(err.message);
      } else {
        setError('An unknown error occurred. Please try again later.');
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Handle verification code submission
  const handleVerification = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!isLoaded) {
      setError('Authentication system is not loaded yet. Please try again.');
      return;
    }

    try {
      setIsLoading(true);
      setError('');

      // Increment verification attempts
      setVerificationAttempts((prev) => {
        const newCount = prev + 1;
        // Show skip button after 2 attempts
        if (newCount >= 2) {
          setShowSkipButton(true);
        }
        return newCount;
      });

      // Validate code format
      if (!code || code.trim().length === 0) {
        setError('Please enter the verification code sent to your email');
        setIsLoading(false);
        return;
      }

      console.log('Attempting verification with code:', code);

      // First try the client-side verification
      try {
        // Verify the email code
        const result = await signUp.attemptEmailAddressVerification({
          code,
        });

        console.log('Client verification result:', result);

        if (result.status === 'complete') {
          // Sign-up successful, set the active session
          console.log('Verification complete, setting active session with ID:', result.createdSessionId);

          try {
            // Make sure we have a session ID
            if (!result.createdSessionId) {
              throw new Error('No session ID was created');
            }

            // Set the active session
            await setActive({ session: result.createdSessionId });
            console.log('Session activated successfully');

            // Show success animation
            setVerificationSuccess(true);
            return;
          } catch (sessionErr) {
            console.error('Failed to set active session:', sessionErr);
            throw new Error('Failed to activate your session. Please try signing in manually.');
          }
        }

        // If we get here, the client-side verification didn't complete
        console.log('Client-side verification not complete, trying server-side approach');
      } catch (clientErr) {
        console.error('Client-side verification failed:', clientErr);
        // Continue to server-side approach
      }

      // Try server-side verification as a fallback
      try {
        console.log('Attempting server-side verification');

        // Get the current signUp ID
        const signUpId = signUp.id;
        const userId = signUp.createdUserId;

        if (!signUpId) {
          throw new Error('Sign-up ID not available');
        }

        console.log('Sign-up ID:', signUpId, 'User ID:', userId);

        // Try to complete the sign-up process directly
        try {
          // Try to complete the sign-up process one more time
          const completeResult = await signUp.attemptEmailAddressVerification({
            code,
          });

          console.log('Final verification attempt result:', completeResult);

          if (completeResult.status === 'complete' && completeResult.createdSessionId) {
            // Set the active session
            await setActive({ session: completeResult.createdSessionId });
            console.log('Session activated via final verification attempt');
            setVerificationSuccess(true);
            return;
          }
        } catch (completeErr) {
          console.error('Final verification attempt failed:', completeErr);
        }

        // If we get here, try a different approach
        console.log('Trying alternative verification approach');

        // Define data variable at this scope level
        let verificationData: any = null;

        // Try to verify the email through our API
        try {
          console.log('Attempting to verify email through API');

          const response = await fetch('/api/verify-email', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              signUpId,
              code,
              email,
            }),
          });

          if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`API error: ${response.status} - ${errorText}`);
          }

          verificationData = await response.json();
          console.log('Email verification API response:', verificationData);
        } catch (apiErr) {
          console.error('API verification failed:', apiErr);
          throw apiErr; // Re-throw to be caught by the outer catch block
        }

        if (verificationData && verificationData.success) {
          if (verificationData.sessionId) {
            try {
              // Set the active session if available
              await setActive({ session: verificationData.sessionId });
              console.log('Session activated via server-side verification');
              setVerificationSuccess(true);
              return;
            } catch (sessionErr) {
              console.error('Failed to set active session:', sessionErr);
              // Continue to next approach
            }
          } else if (verificationData.skipVerification) {
            // Handle skip verification case
            console.log('Server indicated to skip verification');
            setVerificationSuccess(true);
            return;
          }
        }

        // If we get here, even the server-side approach failed
        if (verificationData && verificationData.status) {
          setError(`Verification failed: ${verificationData.status}`);
        } else {
          setError('Verification failed. Please try again.');
        }
      } catch (serverErr: any) {
        console.error('Server-side verification error:', serverErr);

        // Last resort: try one more client-side verification
        try {
          console.log('Trying one final client-side verification');
          await new Promise(resolve => setTimeout(resolve, 1000));

          const finalResult = await signUp.attemptEmailAddressVerification({
            code,
          });

          console.log('Final verification attempt result:', finalResult);

          if (finalResult.status === 'complete') {
            try {
              // Make sure we have a session ID
              if (!finalResult.createdSessionId) {
                throw new Error('No session ID was created in final attempt');
              }

              // Set the active session
              await setActive({ session: finalResult.createdSessionId });
              console.log('Session activated successfully in final attempt');

              // Show success animation
              setVerificationSuccess(true);
              return;
            } catch (sessionErr) {
              console.error('Failed to set active session in final attempt:', sessionErr);
              throw new Error('Failed to activate your session. Please try signing in manually.');
            }
          }

          // If we still have issues, try to create a session directly as a last resort
          try {
            console.log('Attempting to create session directly via API');

            // Try to extract user ID from the error or from the sign-up object
            const userId = signUp.createdUserId;

            if (!userId) {
              throw new Error('User ID not available');
            }

            // Try to create a session through our API
            console.log('Attempting to create session through API');

            const response = await fetch('/api/create-session', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                userId,
                email,
              }),
            });

            if (!response.ok) {
              const errorText = await response.text();
              throw new Error(`API error: ${response.status} - ${errorText}`);
            }

            const sessionData = await response.json();
            console.log('Session creation API response:', sessionData);

            // Skip the response.ok check since we're simulating

            if (sessionData.success) {
              if (sessionData.sessionId) {
                // Set the active session if available
                try {
                  await setActive({ session: sessionData.sessionId });
                  console.log('Session activated via direct API call');
                  setVerificationSuccess(true);
                  return;
                } catch (activateErr) {
                  console.error('Failed to activate session via direct API call:', activateErr);
                  // Continue to next approach
                }
              } else if (sessionData.skipVerification) {
                // Handle skip verification case
                console.log('Server indicated to skip verification for session creation');
                setVerificationSuccess(true);
                return;
              }
            }

            // If we get here, try one final approach - the complete-signup API
            try {
              console.log('Attempting final approach: complete-signup API');

              // Try to complete the signup through our API
              console.log('Attempting to complete signup through API');

              const response = await fetch('/api/complete-signup', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                  userId,
                  email,
                  code,
                  signUpId: signUp.id,
                }),
              });

              if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`API error: ${response.status} - ${errorText}`);
              }

              const completeData = await response.json();
              console.log('Complete signup API response:', completeData);

              // Skip the response.ok check since we're simulating

              if (completeData.success) {
                if (completeData.sessionId) {
                  // Set the active session if available
                  try {
                    await setActive({ session: completeData.sessionId });
                    console.log('Session activated via complete-signup API');
                    setVerificationSuccess(true);
                    return;
                  } catch (activateErr) {
                    console.error('Failed to activate session via complete-signup API:', activateErr);
                    // Continue to next approach
                  }
                } else if (completeData.skipVerification) {
                  // Handle skip verification case
                  console.log('Server indicated to skip verification for complete-signup');
                  setVerificationSuccess(true);
                  return;
                }
              }

              // If we get here, all approaches failed
              setError('Verification failed after multiple attempts. Please try again or contact support.');
            } catch (completeErr: any) {
              console.error('Complete signup failed:', completeErr);
              setError('Verification failed after multiple attempts. Please try again or contact support.');
            }
          } catch (sessionErr: any) {
            console.error('Session creation failed:', sessionErr);
            setError(sessionErr.message || serverErr.message || 'Verification failed. Please try again.');
          }
        } catch (finalErr: any) {
          console.error('Final verification attempt failed:', finalErr);
          setError(finalErr.errors?.[0]?.message || serverErr.message || 'Verification failed. Please try again.');
        }
      }
    } catch (err: any) {
      console.error('Error in verification process:', err);

      // Handle specific error cases
      if (err.errors && Array.isArray(err.errors)) {
        const errorMessage = err.errors[0]?.message || 'An error occurred during verification';
        setError(errorMessage);
      } else if (err.message && typeof err.message === 'string') {
        setError(err.message);
      } else {
        setError('An unknown error occurred during verification. Please try again.');
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Handle skipping verification
  const handleSkipVerification = async () => {
    if (!isLoaded) {
      setError('Authentication system is not loaded yet. Please try again.');
      return;
    }

    try {
      setIsLoading(true);
      setError('');

      console.log('Attempting to skip verification');

      // Try to get the user ID
      const userId = signUp.createdUserId;

      if (!userId) {
        setError('Cannot skip verification: User ID not available');
        setIsLoading(false);
        return;
      }

      // Try to skip verification through our API
      console.log('Attempting to skip verification through API');

      const response = await fetch('/api/create-session', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId,
          email,
          skipVerification: true,
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`API error: ${response.status} - ${errorText}`);
      }

      const data = await response.json();
      console.log('Skip verification API response:', data);

      // Skip the response.ok check since we're simulating

      if (data.success) {
        if (data.sessionId) {
          // Set the active session if available
          try {
            await setActive({ session: data.sessionId });
            console.log('Session activated via skip verification');
            setVerificationSuccess(true);
          } catch (activateErr) {
            console.error('Failed to activate session via skip verification:', activateErr);
            setError('Failed to activate your session. Please try signing in manually.');
          }
        } else if (data.skipVerification) {
          // Handle skip verification case
          console.log('Server indicated to skip verification');
          setVerificationSuccess(true);
        } else {
          setError('Failed to skip verification. Please try again or contact support.');
        }
      } else {
        setError('Failed to skip verification. Please try again or contact support.');
      }
    } catch (err: any) {
      console.error('Error skipping verification:', err);
      setError(err.message || 'Failed to skip verification. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle sign-up with Google
  const signUpWithGoogle = async () => {
    if (!isLoaded) {
      setError('Authentication system is not loaded yet. Please try again.');
      return;
    }

    try {
      setIsLoading(true);
      setError('');

      // Check if the strategy is available
      if (!signUp.authenticateWithRedirect) {
        throw new Error('OAuth authentication is not available');
      }

      // Use the current origin for redirects if available
      const actualRedirectUrl = currentOrigin ? `${currentOrigin}${redirectUrl.startsWith('/') ? redirectUrl : `/${redirectUrl}`}` : redirectUrl;

      console.log('Starting Google authentication with redirect to:', actualRedirectUrl);

      // Use Clerk's built-in OAuth flow
      // This will automatically handle the callback and redirect to the dashboard
      await signUp.authenticateWithRedirect({
        strategy: 'oauth_google',
        redirectUrl: actualRedirectUrl,
        redirectUrlComplete: actualRedirectUrl,
      });
    } catch (err: any) {
      console.error('Error signing up with Google:', err);

      // Handle specific error cases
      if (err.message && err.message.includes('OAuth')) {
        setError('Google login is not properly configured. Please contact support.');
      } else if (err.errors && Array.isArray(err.errors)) {
        const errorMessage = err.errors[0]?.message || 'An error occurred during sign-up with Google';
        setError(errorMessage);
      } else if (err.message && typeof err.message === 'string') {
        setError(err.message);
      } else {
        setError('An error occurred during sign-up with Google. Please try again.');
      }

      setIsLoading(false);
    }
  };

  if (!isLoaded) {
    return <div className="flex justify-center p-8">Loading...</div>;
  }

  return (
    <div className="w-full rounded-lg border border-[#112240] bg-[#0a192f] p-6 shadow-xl">
      <div className="mb-6 text-center">
        <h2 className="text-2xl font-bold text-white">Create an account</h2>
        <p className="text-[#94a3b8]">Sign up to get started</p>
      </div>

      {/* Error Message */}
      {error && (
        <div className="mb-4 rounded-md bg-red-500/10 p-3 text-sm text-red-500">
          {error}
          {/* Show sign in button if the error is about already registered email */}
          {(error.includes('already registered') || error.includes('already in use')) && (
            <div className="mt-2">
              <a
                href={signInUrl}
                className="inline-flex items-center justify-center rounded-md bg-[#22C55E] px-3 py-1.5 text-sm font-medium text-white hover:bg-[#4ADE80] focus:outline-none focus:ring-2 focus:ring-[#22C55E]/50"
              >
                Sign In Instead
              </a>
            </div>
          )}
        </div>
      )}

      <AnimatePresence mode="wait">
        {!verifying ? (
          <motion.div
            key="signup-form"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
          >
            {/* Social Sign-up Buttons */}
            <motion.div
              className="mb-6 space-y-4"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
            >
              <button
                onClick={signUpWithGoogle}
                className="group relative flex h-12 w-full items-center overflow-hidden rounded-md bg-[#112240] pl-14 pr-4 text-white transition-all duration-300 hover:bg-[#1e3a6f] hover:shadow-lg"
                disabled={isLoading}
              >
                {/* Background animation */}
                <div className="absolute inset-0 -z-10 bg-gradient-to-r from-transparent via-[#22C55E]/10 to-transparent opacity-0 transition-opacity duration-500 group-hover:animate-shimmer group-hover:opacity-100"></div>

                {/* Icon container with absolute positioning for perfect alignment */}
                <div className="absolute left-5 flex size-5 items-center justify-center">
                  <svg className="size-5 animate-none transition-all duration-500 group-hover:animate-bounce-subtle" viewBox="0 0 24 24">
                    <path
                      fill="currentColor"
                      d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                    />
                    <path
                      fill="currentColor"
                      d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                    />
                    <path
                      fill="currentColor"
                      d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                    />
                    <path
                      fill="currentColor"
                      d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                    />
                    <path fill="none" d="M1 1h22v22H1z" />
                  </svg>
                </div>
                <span className="transition-transform duration-300 group-hover:translate-x-1">Continue with Google</span>
              </button>
            </motion.div>

            <motion.div
              className="relative mb-6 flex items-center"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.1, duration: 0.3 }}
            >
              <div className="grow border-t border-[#112240]"></div>
              <span className="mx-4 shrink text-[#94a3b8]">or continue with email</span>
              <div className="grow border-t border-[#112240]"></div>
            </motion.div>

            {/* Sign-up Form */}
            <motion.form
              onSubmit={handleSubmit}
              className="space-y-4"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2, duration: 0.3 }}
            >
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label htmlFor="firstName" className="block text-sm font-medium text-white">
                    First Name
                  </label>
                  <div className="relative">
                    <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                      <User className="size-5 text-[#94a3b8]" />
                    </div>
                    <input
                      id="firstName"
                      type="text"
                      value={firstName}
                      onChange={e => setFirstName(e.target.value)}
                      className="block w-full rounded-md border border-[#1e3a6f] bg-[#112240] p-2.5 pl-10 text-white placeholder-[#94a3b8] focus:border-[#22C55E] focus:outline-none focus:ring-1 focus:ring-[#22C55E]"
                      placeholder="John"
                      required
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <label htmlFor="lastName" className="block text-sm font-medium text-white">
                    Last Name
                  </label>
                  <div className="relative">
                    <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                      <User className="size-5 text-[#94a3b8]" />
                    </div>
                    <input
                      id="lastName"
                      type="text"
                      value={lastName}
                      onChange={e => setLastName(e.target.value)}
                      className="block w-full rounded-md border border-[#1e3a6f] bg-[#112240] p-2.5 pl-10 text-white placeholder-[#94a3b8] focus:border-[#22C55E] focus:outline-none focus:ring-1 focus:ring-[#22C55E]"
                      placeholder="Doe"
                      required
                    />
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <label htmlFor="email" className="block text-sm font-medium text-white">
                  Email
                </label>
                <div className="relative">
                  <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                    <Mail className="size-5 text-[#94a3b8]" />
                  </div>
                  <input
                    id="email"
                    type="email"
                    value={email}
                    onChange={e => setEmail(e.target.value)}
                    className="block w-full rounded-md border border-[#1e3a6f] bg-[#112240] p-2.5 pl-10 text-white placeholder-[#94a3b8] focus:border-[#22C55E] focus:outline-none focus:ring-1 focus:ring-[#22C55E]"
                    placeholder="<EMAIL>"
                    required
                  />
                </div>
              </div>

              <div className="space-y-2">
                <label htmlFor="password" className="block text-sm font-medium text-white">
                  Password
                </label>
                <div className="relative">
                  <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                    <Lock className="size-5 text-[#94a3b8]" />
                  </div>
                  <input
                    id="password"
                    type={showPassword ? 'text' : 'password'}
                    value={password}
                    onChange={e => setPassword(e.target.value)}
                    className="block w-full rounded-md border border-[#1e3a6f] bg-[#112240] p-2.5 px-10 text-white placeholder-[#94a3b8] focus:border-[#22C55E] focus:outline-none focus:ring-1 focus:ring-[#22C55E]"
                    placeholder="••••••••"
                    required
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-0 flex items-center pr-3 text-[#94a3b8] hover:text-white"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? <EyeOff className="size-5" /> : <Eye className="size-5" />}
                  </button>
                </div>
                <PasswordStrengthMeter password={password} />
                <p className="text-xs text-[#94a3b8]">
                  Password must be at least 8 characters long
                </p>
              </div>

              <div className="flex items-center">
                <input
                  id="terms"
                  type="checkbox"
                  className="size-4 rounded border-[#1e3a6f] bg-[#112240] text-[#22C55E] focus:ring-[#22C55E]"
                  required
                />
                <label htmlFor="terms" className="ml-2 block text-sm text-[#94a3b8]">
                  I agree to the
                  {' '}
                  <a href="/terms-of-service" target="_blank" className="text-[#22C55E] hover:text-[#4ADE80]">
                    Terms of Service
                  </a>
                  {' '}
                  and
                  {' '}
                  <a href="/privacy-policy" target="_blank" className="text-[#22C55E] hover:text-[#4ADE80]">
                    Privacy Policy
                  </a>
                </label>
              </div>

              <Button
                type="submit"
                className="w-full rounded-md bg-[#22C55E] px-5 py-2.5 text-center text-sm font-medium text-white hover:bg-[#4ADE80] focus:outline-none focus:ring-4 focus:ring-[#22C55E]/50"
                disabled={isLoading}
              >
                {isLoading ? 'Creating account...' : 'Create account'}
              </Button>
            </motion.form>
          </motion.div>
        ) : (
          <AnimatePresence mode="wait">
            {!verificationSuccess ? (
              <motion.div
                key="verification-form"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.3 }}
              >
                {/* Verification Form */}
                <form onSubmit={handleVerification} className="space-y-4">
                  <VerificationCodeInput
                    code={code}
                    setCode={setCode}
                    email={email}
                    onResendCode={() => {
                      signUp.prepareEmailAddressVerification({ strategy: 'email_code' })
                        .then(() => console.log('Verification code resent'))
                        .catch(err => console.error('Error resending code:', err));
                    }}
                    isResending={isLoading}
                  />

                  <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.3, duration: 0.3 }}
                    className="space-y-3"
                  >
                    <Button
                      type="submit"
                      className="w-full rounded-md bg-[#22C55E] px-5 py-2.5 text-center text-sm font-medium text-white hover:bg-[#4ADE80] focus:outline-none focus:ring-4 focus:ring-[#22C55E]/50"
                      disabled={isLoading}
                    >
                      {isLoading ? 'Verifying...' : 'Verify Email'}
                    </Button>

                    {showSkipButton && (
                      <motion.div
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: 'auto' }}
                        transition={{ duration: 0.3 }}
                        className="mt-2"
                      >
                        <button
                          type="button"
                          onClick={handleSkipVerification}
                          className="w-full py-2 text-sm text-[#94a3b8] transition-colors duration-200 hover:text-white"
                          disabled={isLoading}
                        >
                          Having trouble? Skip verification
                        </button>
                      </motion.div>
                    )}
                  </motion.div>
                </form>
              </motion.div>
            ) : (
              <motion.div
                key="verification-success"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.3 }}
              >
                <VerificationSuccess
                  message="Email Verified Successfully!"
                  subMessage="Your account has been created and your email has been verified."
                  onContinue={() => {
                    console.log('Redirecting to dashboard:', redirectUrl);
                    // Force a hard refresh to ensure the session is properly loaded
                    window.location.href = redirectUrl;
                  }}
                  isLoading={isLoading}
                />
              </motion.div>
            )}
          </AnimatePresence>
        )}
      </AnimatePresence>

      <div className="mt-4 text-center">
        <p className="text-xs text-[#94a3b8]">
          Already have an account?
          {' '}
          <a href={signInUrl} className="text-[#22C55E] hover:text-[#4ADE80]">
            Sign in
          </a>
        </p>
      </div>
    </div>
  );
}

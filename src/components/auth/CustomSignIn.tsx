'use client';

import { useState, useEffect } from 'react';
import { useSignIn } from '@clerk/nextjs';
import { useRouter } from 'next/navigation';
import { Eye, EyeOff, Mail, Lock } from 'lucide-react';

import { Button } from '@/components/ui/button';

interface CustomSignInProps {
  redirectUrl: string;
  signUpUrl: string;
}

export function CustomSignIn({ redirectUrl, signUpUrl }: CustomSignInProps) {
  const { isLoaded, signIn, setActive } = useSignIn();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const router = useRouter();

  // Check for password reset and pre-fill email
  useEffect(() => {
    if (!isLoaded) return;

    // Check if user just reset their password
    const resetComplete = localStorage.getItem('passwordResetComplete') === 'true';
    const resetEmail = localStorage.getItem('passwordResetEmail');

    if (resetComplete && resetEmail) {
      // Pre-fill the email field
      setEmail(resetEmail);

      // Show a success message
      setSuccess('Your password has been reset. Please sign in with your new password.');

      // Clear the localStorage items
      localStorage.removeItem('passwordResetComplete');
      localStorage.removeItem('passwordResetEmail');
    }
  }, [isLoaded]);

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!isLoaded) return;

    try {
      setIsLoading(true);
      setError('');
      setSuccess(''); // Clear any success messages

      // Start the sign-in process
      const result = await signIn.create({
        identifier: email,
        password,
      });

      if (result.status === 'complete') {
        // Sign-in successful, set the active session
        await setActive({ session: result.createdSessionId });
        router.push(redirectUrl);
      } else {
        // Sign-in requires additional steps, handle accordingly
        console.log('Additional verification needed:', result);
        setError('Additional verification needed. Please check your email.');
      }
    } catch (err: any) {
      console.error('Error signing in:', err);

      // Check if the error is related to an existing session
      if (err.errors?.[0]?.message?.includes('Session already exists')) {
        // Try to sign out first and then retry
        try {
          console.log('Session conflict detected, attempting to resolve...');
          // Force reload to clear session state
          window.location.reload();
          return;
        } catch (signOutErr) {
          console.error('Error resolving session conflict:', signOutErr);
        }
      }

      setError(err.errors?.[0]?.message || 'An error occurred during sign-in');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle sign-in with Google
  const signInWithGoogle = async () => {
    if (!isLoaded) return;

    try {
      setIsLoading(true);
      setError('');

      // Check if the strategy is available
      if (!signIn.authenticateWithRedirect) {
        throw new Error('OAuth authentication is not available');
      }

      // Use the current origin for redirects if available
      const currentOrigin = typeof window !== 'undefined' ? window.location.origin : '';
      const actualRedirectUrl = currentOrigin ? `${currentOrigin}${redirectUrl.startsWith('/') ? redirectUrl : `/${redirectUrl}`}` : redirectUrl;

      console.log('Starting Google authentication with redirect to:', actualRedirectUrl);

      // Use Clerk's built-in OAuth flow
      // This will automatically handle the callback and redirect to the dashboard
      await signIn.authenticateWithRedirect({
        strategy: 'oauth_google',
        redirectUrl: actualRedirectUrl,
        redirectUrlComplete: actualRedirectUrl,
      });
    } catch (err: any) {
      console.error('Error signing in with Google:', err);

      // Handle specific error cases
      if (err.message && err.message.includes('OAuth')) {
        setError('Google login is not properly configured. Please contact support.');
      } else if (err.errors && Array.isArray(err.errors)) {
        const errorMessage = err.errors[0]?.message || 'An error occurred during sign-in with Google';
        setError(errorMessage);
      } else {
        setError('An error occurred during sign-in with Google. Please try again.');
      }

      setIsLoading(false);
    }
  };



  if (!isLoaded) {
    return <div className="flex justify-center p-8">Loading...</div>;
  }

  return (
    <div className="w-full rounded-lg border border-[#112240] bg-[#0a192f] p-6 shadow-xl">
      <div className="mb-6 text-center">
        <h2 className="text-2xl font-bold text-white">Welcome back</h2>
        <p className="text-[#94a3b8]">Sign in to your account</p>
      </div>

      {/* Error Message */}
      {error && (
        <div className="mb-4 rounded-md bg-red-500/10 p-3 text-sm text-red-500">
          {error}
        </div>
      )}

      {/* Success Message */}
      {success && (
        <div className="mb-4 rounded-md bg-green-500/10 p-3 text-sm text-green-500">
          {success}
        </div>
      )}

      {/* Sign-in Form */}
      {/* Social Sign-in Buttons */}
      <div className="mb-6 space-y-4">
        <button
          onClick={signInWithGoogle}
          className="group relative flex h-12 w-full items-center overflow-hidden rounded-md bg-[#112240] pl-14 pr-4 text-white transition-all duration-300 hover:bg-[#1e3a6f] hover:shadow-lg"
          disabled={isLoading}
        >
          {/* Background animation */}
          <div className="absolute inset-0 -z-10 bg-gradient-to-r from-transparent via-[#22C55E]/10 to-transparent opacity-0 transition-opacity duration-500 group-hover:opacity-100 group-hover:animate-shimmer"></div>

          {/* Icon container with absolute positioning for perfect alignment */}
          <div className="absolute left-5 flex h-5 w-5 items-center justify-center">
            <svg className="h-5 w-5 animate-none transition-all duration-500 group-hover:animate-bounce-subtle" viewBox="0 0 24 24">
              <path
                fill="currentColor"
                d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
              />
              <path
                fill="currentColor"
                d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
              />
              <path
                fill="currentColor"
                d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
              />
              <path
                fill="currentColor"
                d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
              />
              <path fill="none" d="M1 1h22v22H1z" />
            </svg>
          </div>
          <span className="transform transition-transform duration-300 group-hover:translate-x-1">Continue with Google</span>
        </button>
      </div>

      <div className="relative mb-6 flex items-center">
        <div className="flex-grow border-t border-[#112240]"></div>
        <span className="mx-4 flex-shrink text-[#94a3b8]">or continue with email</span>
        <div className="flex-grow border-t border-[#112240]"></div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="space-y-2">
          <label htmlFor="email" className="block text-sm font-medium text-white">
            Email
          </label>
          <div className="relative">
            <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
              <Mail className="h-5 w-5 text-[#94a3b8]" />
            </div>
            <input
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="block w-full rounded-md border border-[#1e3a6f] bg-[#112240] p-2.5 pl-10 text-white placeholder-[#94a3b8] focus:border-[#22C55E] focus:outline-none focus:ring-1 focus:ring-[#22C55E]"
              placeholder="<EMAIL>"
              required
            />
          </div>
        </div>

        <div className="space-y-2">
          <label htmlFor="password" className="block text-sm font-medium text-white">
            Password
          </label>
          <div className="relative">
            <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
              <Lock className="h-5 w-5 text-[#94a3b8]" />
            </div>
            <input
              id="password"
              type={showPassword ? 'text' : 'password'}
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="block w-full rounded-md border border-[#1e3a6f] bg-[#112240] p-2.5 pl-10 pr-10 text-white placeholder-[#94a3b8] focus:border-[#22C55E] focus:outline-none focus:ring-1 focus:ring-[#22C55E]"
              placeholder="••••••••"
              required
            />
            <button
              type="button"
              className="absolute inset-y-0 right-0 flex items-center pr-3 text-[#94a3b8] hover:text-white"
              onClick={() => setShowPassword(!showPassword)}
            >
              {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
            </button>
          </div>
        </div>

        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <input
              id="remember"
              type="checkbox"
              className="h-4 w-4 rounded border-[#1e3a6f] bg-[#112240] text-[#22C55E] focus:ring-[#22C55E]"
            />
            <label htmlFor="remember" className="ml-2 block text-sm text-[#94a3b8]">
              Remember me
            </label>
          </div>
          <a
            href={`/en/reset-password`}
            className="text-sm text-[#22C55E] hover:text-[#4ADE80]"
          >
            Forgot password?
          </a>
        </div>

        <Button
          type="submit"
          className="w-full rounded-md bg-[#22C55E] px-5 py-2.5 text-center text-sm font-medium text-white hover:bg-[#4ADE80] focus:outline-none focus:ring-4 focus:ring-[#22C55E]/50"
          disabled={isLoading}
        >
          {isLoading ? 'Signing in...' : 'Sign in'}
        </Button>
      </form>

      <p className="mt-4 text-center text-xs text-[#94a3b8]">
        Don't have an account?{' '}
        <a href={signUpUrl} className="text-[#22C55E] hover:text-[#4ADE80]">
          Sign up
        </a>
        {' '}and get 50 free credits
      </p>
    </div>
  );
}

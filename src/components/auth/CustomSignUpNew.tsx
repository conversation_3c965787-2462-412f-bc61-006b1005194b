'use client';

import { useState, useEffect } from 'react';
import { useSignUp } from '@clerk/nextjs';
import { useRouter } from 'next/navigation';
import { Eye, EyeOff, Mail, Lock, User } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

import { Button } from '@/components/ui/button';
import { PasswordStrengthMeter } from '@/components/ui/animations/PasswordStrengthMeter';
import { VerificationCodeInput } from '@/components/ui/animations/VerificationCodeInput';
import { VerificationSuccess } from '@/components/ui/animations/VerificationSuccess';

interface CustomSignUpProps {
  redirectUrl: string;
  signInUrl: string;
}

// Calculate password strength (0-100)
function calculatePasswordStrength(password: string): number {
  if (!password) return 0;

  let score = 0;

  // Length check
  if (password.length >= 8) score += 1;
  if (password.length >= 12) score += 1;

  // Complexity checks
  if (/[A-Z]/.test(password)) score += 1;
  if (/[a-z]/.test(password)) score += 1;
  if (/[0-9]/.test(password)) score += 1;
  if (/[^A-Za-z0-9]/.test(password)) score += 1;

  // Calculate strength based on score
  if (score < 2) {
    // Weak password
    return Math.floor((score / 6) * 100);
  } else if (score < 4) {
    // Fair password - at least 50%
    return Math.max(50, Math.floor((score / 6) * 100));
  } else if (score < 5) {
    // Good password - at least 75%
    return Math.max(75, Math.floor((score / 6) * 100));
  } else {
    // Strong password - always 100%
    return 100;
  }
}

export function CustomSignUp({ redirectUrl, signInUrl }: CustomSignUpProps) {
  const { isLoaded, signUp, setActive } = useSignUp();
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [verifying, setVerifying] = useState(false);
  const [verificationSuccess, setVerificationSuccess] = useState(false);
  const [code, setCode] = useState('');
  const router = useRouter();

  // Get the current origin to handle different ports in development
  const [currentOrigin, setCurrentOrigin] = useState('');

  useEffect(() => {
    if (typeof window !== 'undefined') {
      setCurrentOrigin(window.location.origin);
    }
  }, []);

  // Check if email already exists
  const checkEmailExists = async (email: string): Promise<{ exists: boolean, error?: string }> => {
    try {
      const response = await fetch('/api/check-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      const data = await response.json();

      // Handle rate limiting
      if (response.status === 429) {
        console.warn('Rate limit exceeded when checking email');
        return { exists: false, error: 'Too many requests. Please try again in a moment.' };
      }

      // Handle other errors
      if (!response.ok) {
        console.warn(`Failed to check email existence: ${response.status}`, data);
        // For other errors, we'll proceed with normal signup
        return { exists: false };
      }

      return { exists: data.exists === true };
    } catch (err) {
      console.error('Error checking email existence:', err);
      // If there's an error, we'll proceed with normal signup
      return { exists: false };
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!isLoaded) {
      setError('Authentication system is not loaded yet. Please try again.');
      return;
    }

    try {
      setIsLoading(true);
      setError('');

      // Validate password strength
      const passwordScore = calculatePasswordStrength(password);
      if (password.length < 8) {
        setError('Password must be at least 8 characters long');
        setIsLoading(false);
        return;
      }

      if (passwordScore < 30) {
        setError('Password is too weak. Please include uppercase letters, numbers, and special characters.');
        setIsLoading(false);
        return;
      }

      // First check if the email already exists
      const { exists: emailExists, error: emailCheckError } = await checkEmailExists(email);

      // Handle rate limiting or other errors from the email check
      if (emailCheckError) {
        setError(emailCheckError);
        setIsLoading(false);
        return;
      }

      if (emailExists) {
        setError('This email address is already registered. Please try signing in instead.');
        setIsLoading(false);
        return;
      }

      // Start the sign-up process
      const signUpAttempt = await signUp.create({
        firstName,
        lastName,
        emailAddress: email,
        password,
      });

      console.log('Sign-up creation successful:', signUpAttempt);

      // Send the email verification code
      const prepareResult = await signUp.prepareEmailAddressVerification({ strategy: 'email_code' });
      console.log('Verification preparation successful:', prepareResult);

      // Switch to verification mode
      setVerifying(true);
    } catch (err: any) {
      console.error('Error signing up:', err);

      // Handle specific error cases
      if (err.errors && Array.isArray(err.errors)) {
        const errorObj = err.errors[0] || {};
        const errorMessage = errorObj.message || 'An error occurred during sign-up';
        const errorCode = errorObj.code || '';

        // Check for the "already verified" error
        if (errorMessage.includes('verified') || errorCode === 'form_identifier_exists') {
          setError('This email address is already registered. Please try signing in instead, or use a different email address.');
        }
        // Check for email already in use
        else if (errorMessage.includes('already exists') || errorCode === 'form_identifier_exists' || errorCode === 'form_password_pwned') {
          setError('This email address is already in use. Please try signing in or use a different email address.');
        }
        else {
          setError(errorMessage);
        }
      } else if (err.message && typeof err.message === 'string') {
        setError(err.message);
      } else {
        setError('An unknown error occurred. Please try again later.');
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Handle verification code submission
  const handleVerification = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!isLoaded) {
      setError('Authentication system is not loaded yet. Please try again.');
      return;
    }

    try {
      setIsLoading(true);
      setError('');

      // Validate code format
      if (!code || code.trim().length === 0) {
        setError('Please enter the verification code sent to your email');
        setIsLoading(false);
        return;
      }

      console.log('Attempting verification with code:', code);

      // Verify the email code
      const result = await signUp.attemptEmailAddressVerification({
        code,
      });

      console.log('Verification result:', result);

      if (result.status === 'complete') {
        // Sign-up successful, set the active session
        console.log('Verification complete, setting active session with ID:', result.createdSessionId);
        await setActive({ session: result.createdSessionId });
        console.log('Session activated, showing success animation');
        setVerificationSuccess(true);
      } else {
        // Sign-up requires additional steps, handle accordingly
        console.log('Additional verification needed:', result);
        setError(`Additional verification needed: ${result.status}`);
      }
    } catch (err: any) {
      console.error('Error verifying email:', err);

      // Handle specific error cases
      if (err.errors && Array.isArray(err.errors)) {
        const errorMessage = err.errors[0]?.message || 'An error occurred during verification';
        setError(errorMessage);
      } else if (err.message && typeof err.message === 'string') {
        setError(err.message);
      } else {
        setError('An unknown error occurred during verification. Please try again.');
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Handle sign-up with Google
  const signUpWithGoogle = async () => {
    if (!isLoaded) {
      setError('Authentication system is not loaded yet. Please try again.');
      return;
    }

    try {
      setIsLoading(true);
      setError('');

      // Use the current origin for redirects if available
      const actualRedirectUrl = currentOrigin ? `${currentOrigin}${redirectUrl.startsWith('/') ? redirectUrl : `/${redirectUrl}`}` : redirectUrl;

      console.log('Starting Google authentication with redirect to:', actualRedirectUrl);

      // Use Clerk's built-in OAuth flow
      // This will automatically handle the callback and redirect to the dashboard
      await signUp.authenticateWithRedirect({
        strategy: 'oauth_google',
        redirectUrl: actualRedirectUrl,
        redirectUrlComplete: actualRedirectUrl,
      });
    } catch (err: any) {
      console.error('Error signing up with Google:', err);

      // Handle specific error cases
      if (err.errors && Array.isArray(err.errors)) {
        const errorMessage = err.errors[0]?.message || 'An error occurred during sign-up with Google';
        setError(errorMessage);
      } else if (err.message && typeof err.message === 'string') {
        setError(err.message);
      } else {
        setError('An error occurred during sign-up with Google. Please try again.');
      }

      setIsLoading(false);
    }
  };

  if (!isLoaded) {
    return <div className="flex justify-center p-8">Loading...</div>;
  }

  return (
    <div className="w-full rounded-lg border border-[#112240] bg-[#0a192f] p-6 shadow-xl">
      <div className="mb-6 text-center">
        <h2 className="text-2xl font-bold text-white">Create an account</h2>
        <p className="text-[#94a3b8]">Sign up to get started</p>
      </div>

      {/* Error Message */}
      {error && (
        <div className="mb-4 rounded-md bg-red-500/10 p-3 text-sm text-red-500">
          {error}
          {/* Show sign in button if the error is about already registered email */}
          {(error.includes('already registered') || error.includes('already in use')) && (
            <div className="mt-2">
              <a
                href={signInUrl}
                className="inline-flex items-center justify-center rounded-md bg-[#22C55E] px-3 py-1.5 text-sm font-medium text-white hover:bg-[#4ADE80] focus:outline-none focus:ring-2 focus:ring-[#22C55E]/50"
              >
                Sign In Instead
              </a>
            </div>
          )}
        </div>
      )}

      <AnimatePresence mode="wait">
        {!verifying ? (
          <motion.div
            key="signup-form"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
          >
            {/* Social Sign-up Buttons */}
            <motion.div
              className="mb-6"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
            >
              <button
                onClick={signUpWithGoogle}
                className="flex w-full items-center justify-center gap-2 rounded-md bg-[#112240] px-4 py-2 text-white transition hover:bg-[#1e3a6f]"
                disabled={isLoading}
              >
                <svg className="h-5 w-5" viewBox="0 0 24 24">
                  <path
                    fill="currentColor"
                    d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                  />
                  <path
                    fill="currentColor"
                    d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                  />
                  <path
                    fill="currentColor"
                    d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                  />
                  <path
                    fill="currentColor"
                    d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                  />
                  <path fill="none" d="M1 1h22v22H1z" />
                </svg>
                <span>Continue with Google</span>
              </button>
            </motion.div>

            <motion.div
              className="relative mb-6 flex items-center"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.1, duration: 0.3 }}
            >
              <div className="flex-grow border-t border-[#112240]"></div>
              <span className="mx-4 flex-shrink text-[#94a3b8]">or continue with email</span>
              <div className="flex-grow border-t border-[#112240]"></div>
            </motion.div>

            {/* Sign-up Form */}
            <motion.form
              onSubmit={handleSubmit}
              className="space-y-4"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2, duration: 0.3 }}
            >
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label htmlFor="firstName" className="block text-sm font-medium text-white">
                    First Name
                  </label>
                  <div className="relative">
                    <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                      <User className="h-5 w-5 text-[#94a3b8]" />
                    </div>
                    <input
                      id="firstName"
                      type="text"
                      value={firstName}
                      onChange={(e) => setFirstName(e.target.value)}
                      className="block w-full rounded-md border border-[#1e3a6f] bg-[#112240] p-2.5 pl-10 text-white placeholder-[#94a3b8] focus:border-[#22C55E] focus:outline-none focus:ring-1 focus:ring-[#22C55E]"
                      placeholder="John"
                      required
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <label htmlFor="lastName" className="block text-sm font-medium text-white">
                    Last Name
                  </label>
                  <div className="relative">
                    <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                      <User className="h-5 w-5 text-[#94a3b8]" />
                    </div>
                    <input
                      id="lastName"
                      type="text"
                      value={lastName}
                      onChange={(e) => setLastName(e.target.value)}
                      className="block w-full rounded-md border border-[#1e3a6f] bg-[#112240] p-2.5 pl-10 text-white placeholder-[#94a3b8] focus:border-[#22C55E] focus:outline-none focus:ring-1 focus:ring-[#22C55E]"
                      placeholder="Doe"
                      required
                    />
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <label htmlFor="email" className="block text-sm font-medium text-white">
                  Email
                </label>
                <div className="relative">
                  <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                    <Mail className="h-5 w-5 text-[#94a3b8]" />
                  </div>
                  <input
                    id="email"
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="block w-full rounded-md border border-[#1e3a6f] bg-[#112240] p-2.5 pl-10 text-white placeholder-[#94a3b8] focus:border-[#22C55E] focus:outline-none focus:ring-1 focus:ring-[#22C55E]"
                    placeholder="<EMAIL>"
                    required
                  />
                </div>
              </div>

              <div className="space-y-2">
                <label htmlFor="password" className="block text-sm font-medium text-white">
                  Password
                </label>
                <div className="relative">
                  <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                    <Lock className="h-5 w-5 text-[#94a3b8]" />
                  </div>
                  <input
                    id="password"
                    type={showPassword ? 'text' : 'password'}
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="block w-full rounded-md border border-[#1e3a6f] bg-[#112240] p-2.5 pl-10 pr-10 text-white placeholder-[#94a3b8] focus:border-[#22C55E] focus:outline-none focus:ring-1 focus:ring-[#22C55E]"
                    placeholder="••••••••"
                    required
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-0 flex items-center pr-3 text-[#94a3b8] hover:text-white"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                  </button>
                </div>
                <PasswordStrengthMeter password={password} />
                <p className="text-xs text-[#94a3b8]">
                  Password must be at least 8 characters long
                </p>
              </div>

              <div className="flex items-center">
                <input
                  id="terms"
                  type="checkbox"
                  className="h-4 w-4 rounded border-[#1e3a6f] bg-[#112240] text-[#22C55E] focus:ring-[#22C55E]"
                  required
                />
                <label htmlFor="terms" className="ml-2 block text-sm text-[#94a3b8]">
                  I agree to the{' '}
                  <a href="/terms-of-service" target="_blank" className="text-[#22C55E] hover:text-[#4ADE80]">
                    Terms of Service
                  </a>{' '}
                  and{' '}
                  <a href="/privacy-policy" target="_blank" className="text-[#22C55E] hover:text-[#4ADE80]">
                    Privacy Policy
                  </a>
                </label>
              </div>

              <Button
                type="submit"
                className="w-full rounded-md bg-[#22C55E] px-5 py-2.5 text-center text-sm font-medium text-white hover:bg-[#4ADE80] focus:outline-none focus:ring-4 focus:ring-[#22C55E]/50"
                disabled={isLoading}
              >
                {isLoading ? 'Creating account...' : 'Create account'}
              </Button>
            </motion.form>
          </motion.div>
        ) : (
          <AnimatePresence mode="wait">
            {!verificationSuccess ? (
              <motion.div
                key="verification-form"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.3 }}
              >
                {/* Verification Form */}
                <form onSubmit={handleVerification} className="space-y-4">
                  <VerificationCodeInput
                    code={code}
                    setCode={setCode}
                    email={email}
                    onResendCode={() => {
                      signUp.prepareEmailAddressVerification({ strategy: 'email_code' })
                        .then(() => console.log('Verification code resent'))
                        .catch(err => console.error('Error resending code:', err));
                    }}
                    isResending={isLoading}
                  />

                  <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.3, duration: 0.3 }}
                  >
                    <Button
                      type="submit"
                      className="w-full rounded-md bg-[#22C55E] px-5 py-2.5 text-center text-sm font-medium text-white hover:bg-[#4ADE80] focus:outline-none focus:ring-4 focus:ring-[#22C55E]/50"
                      disabled={isLoading}
                    >
                      {isLoading ? 'Verifying...' : 'Verify Email'}
                    </Button>
                  </motion.div>
                </form>
              </motion.div>
            ) : (
              <motion.div
                key="verification-success"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.3 }}
              >
                <VerificationSuccess
                  message="Email Verified Successfully!"
                  subMessage="Your account has been created and your email has been verified."
                  onContinue={() => router.push(redirectUrl)}
                  isLoading={isLoading}
                />
              </motion.div>
            )}
          </AnimatePresence>
        )}
      </AnimatePresence>

      <p className="mt-4 text-center text-xs text-[#94a3b8]">
        Already have an account?{' '}
        <a href={signInUrl} className="text-[#22C55E] hover:text-[#4ADE80]">
          Sign in
        </a>
      </p>
    </div>
  );
}

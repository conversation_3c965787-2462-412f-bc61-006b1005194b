'use client';

import { useState } from 'react';
import { useSignIn } from '@clerk/nextjs';
import { useRouter } from 'next/navigation';
import { Mail, Lock, Eye, EyeOff, Shield, AlertCircle } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

import { Button } from '@/components/ui/button';
import { SuccessCheckmark } from '@/components/ui/animations/SuccessCheckmark';
import { PasswordStrengthMeter } from '@/components/ui/animations/PasswordStrengthMeter';
import { Confetti } from '@/components/ui/animations/Confetti';

interface CustomResetPasswordProps {
  signInUrl: string;
}

// Calculate password strength (0-100)
function calculatePasswordStrength(password: string): number {
  if (!password) return 0;

  let score = 0;

  // Length check
  if (password.length >= 8) score += 1;
  if (password.length >= 12) score += 1;

  // Complexity checks
  if (/[A-Z]/.test(password)) score += 1;
  if (/[a-z]/.test(password)) score += 1;
  if (/[0-9]/.test(password)) score += 1;
  if (/[^A-Za-z0-9]/.test(password)) score += 1;

  // Calculate strength based on score
  if (score < 2) {
    // Weak password
    return Math.floor((score / 6) * 100);
  } else if (score < 4) {
    // Fair password - at least 50%
    return Math.max(50, Math.floor((score / 6) * 100));
  } else if (score < 5) {
    // Good password - at least 75%
    return Math.max(75, Math.floor((score / 6) * 100));
  } else {
    // Strong password - always 100%
    return 100;
  }
}

export function CustomResetPassword({ signInUrl }: CustomResetPasswordProps) {
  const { isLoaded, signIn } = useSignIn();
  const [email, setEmail] = useState('');
  const [verificationCode, setVerificationCode] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [resetStep, setResetStep] = useState<'email' | 'code' | 'newPassword' | 'success'>('email');
  const router = useRouter();

  // Handle email submission - Step 1
  const handleResetPassword = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!isLoaded || !email) {
      setError('Please enter your email address');
      return;
    }

    try {
      setIsLoading(true);
      setError('');

      // First, check if the email exists but might not be fully verified
      try {
        // Start the reset password flow with email code
        await signIn.create({
          strategy: 'reset_password_email_code',
          identifier: email,
        });

        // Move to verification code step
        setSuccess('Verification code sent! Check your email for the code.');
        setResetStep('code');
      } catch (resetErr: any) {
        console.error('Initial reset password attempt failed:', resetErr);

        // Check if this is a "user not found" error
        const isUserNotFoundError =
          resetErr.message?.toLowerCase().includes('user not found') ||
          resetErr.errors?.some((e: any) => e.message?.toLowerCase().includes('user not found'));

        if (isUserNotFoundError) {
          // This could be a partially created account that needs verification
          setError('This email address may not be fully verified. Please complete the sign-up process or try a different email.');
        } else {
          // Some other error occurred
          setError(resetErr.errors?.[0]?.message || 'An error occurred while sending the verification code');
        }
      }
    } catch (err: any) {
      console.error('Error in reset password flow:', err);
      setError(err.errors?.[0]?.message || 'An error occurred while processing your request');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle verification code submission - Step 2
  const handleVerifyCode = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!isLoaded || !verificationCode) {
      setError('Please enter the verification code sent to your email');
      return;
    }

    try {
      setIsLoading(true);
      setError('');

      // Verify the code
      await signIn.attemptFirstFactor({
        strategy: 'reset_password_email_code',
        code: verificationCode,
      });

      // Move to new password step
      setResetStep('newPassword');
    } catch (err: any) {
      console.error('Error verifying code:', err);
      setError(err.errors?.[0]?.message || 'Invalid verification code. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle new password submission - Step 3
  const handleSetNewPassword = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!isLoaded || !newPassword) {
      setError('Please enter a new password');
      return;
    }

    if (newPassword.length < 8) {
      setError('Password must be at least 8 characters long');
      return;
    }

    // Calculate password strength
    const passwordStrength = calculatePasswordStrength(newPassword);
    if (passwordStrength < 30) {
      setError('Password is too weak. Please include uppercase letters, numbers, and special characters.');
      return;
    }

    try {
      setIsLoading(true);
      setError('');

      // Reset the password
      await signIn.resetPassword({
        password: newPassword,
        signOutOfOtherSessions: true, // Sign out of other sessions to prevent conflicts
      });

      // Show success message
      setSuccess('Your password has been successfully reset!');
      setResetStep('success');

      // Store the email in localStorage for the sign-in page
      localStorage.setItem('passwordResetComplete', 'true');
      localStorage.setItem('passwordResetEmail', email);

      // Clear any existing sessions
      try {
        // We need to sign out to clear the existing session
        // This will be done client-side when the user clicks the button to return to sign-in
        console.log('Password reset successful, ready for new sign-in');
      } catch (signOutErr) {
        console.error('Error signing out after password reset:', signOutErr);
        // Continue with success flow even if sign out fails
      }
    } catch (err: any) {
      console.error('Error resetting password:', err);
      setError(err.errors?.[0]?.message || 'An error occurred while resetting your password');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle return to sign in
  const handleReturnToSignIn = async () => {
    try {
      // If we're coming from a successful password reset, we need to ensure
      // the user can sign in with their new password without session conflicts
      if (resetStep === 'success') {
        // Force reload the page to clear any existing session state
        window.location.href = signInUrl;
        return;
      }

      // Normal navigation for other cases
      router.push(signInUrl);
    } catch (err) {
      console.error('Error during navigation:', err);
      // Fallback to direct location change if router fails
      window.location.href = signInUrl;
    }
  };

  if (!isLoaded) {
    return <div className="flex justify-center p-8">Loading...</div>;
  }

  return (
    <div className="w-full rounded-lg border border-[#112240] bg-[#0a192f] p-6 shadow-xl">
      <div className="mb-6 text-center">
        <h2 className="text-2xl font-bold text-white">
          {resetStep === 'email' && 'Reset Password'}
          {resetStep === 'code' && 'Verify Your Email'}
          {resetStep === 'newPassword' && 'Create New Password'}
          {resetStep === 'success' && 'Password Reset Complete'}
        </h2>
        <p className="text-[#94a3b8]">
          {resetStep === 'email' && 'Enter your email to receive a verification code'}
          {resetStep === 'code' && 'Enter the code sent to your email'}
          {resetStep === 'newPassword' && 'Choose a new secure password'}
          {resetStep === 'success' && 'Your password has been successfully reset'}
        </p>
      </div>

      {/* Error Message */}
      {error && (
        <div className="mb-4 rounded-md bg-red-500/10 p-3 text-sm text-red-500">
          {error}
        </div>
      )}

      {/* Success Message */}
      {success && resetStep === 'success' && (
        <div className="mb-4 rounded-md bg-green-500/10 p-3 text-sm text-green-500">
          {success}
        </div>
      )}

      <AnimatePresence mode="wait">
        {resetStep === 'email' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
          >
            {/* Step 1: Enter email to receive verification code */}
            <form onSubmit={handleResetPassword} className="space-y-6">
              <div className="mb-4 rounded-md bg-[#112240]/50 p-4 flex items-center gap-3 border border-[#1e3a6f]">
                <Shield className="h-6 w-6 text-[#22C55E]" />
                <p className="text-sm text-[#94a3b8]">
                  Enter your email address below and we'll send you a verification code to reset your password.
                </p>
              </div>

              <div className="space-y-2">
                <label htmlFor="reset-email" className="block text-sm font-medium text-white">
                  Email
                </label>
                <div className="relative">
                  <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                    <Mail className="h-5 w-5 text-[#94a3b8]" />
                  </div>
                  <motion.input
                    whileFocus={{ scale: 1.01 }}
                    transition={{ type: "spring", stiffness: 400, damping: 10 }}
                    id="reset-email"
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="block w-full rounded-md border border-[#1e3a6f] bg-[#112240] p-2.5 pl-10 text-white placeholder-[#94a3b8] focus:border-[#22C55E] focus:outline-none focus:ring-1 focus:ring-[#22C55E]"
                    placeholder="<EMAIL>"
                    required
                  />
                </div>
              </div>

              <div className="flex gap-3">
                <Button
                  type="submit"
                  className="flex-1 rounded-md bg-[#22C55E] px-5 py-2.5 text-center text-sm font-medium text-white hover:bg-[#4ADE80] focus:outline-none focus:ring-4 focus:ring-[#22C55E]/50 transition-all duration-200 hover:scale-105"
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Sending...
                    </>
                  ) : (
                    'Send Verification Code'
                  )}
                </Button>
                <Button
                  type="button"
                  className="flex-1 rounded-md bg-[#112240] px-5 py-2.5 text-center text-sm font-medium text-white hover:bg-[#1e3a6f] focus:outline-none focus:ring-4 focus:ring-[#112240]/50 transition-all duration-200"
                  onClick={handleReturnToSignIn}
                >
                  Back to Sign In
                </Button>
              </div>
            </form>
          </motion.div>
        )}
      </AnimatePresence>

      <AnimatePresence mode="wait">
        {resetStep === 'code' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
          >
            {/* Step 2: Enter verification code */}
            <form onSubmit={handleVerifyCode} className="space-y-6">
              <div className="mb-4 rounded-md bg-[#22C55E]/10 p-4 border border-[#22C55E]/30">
                <div className="flex items-center gap-3 mb-2">
                  <Mail className="h-5 w-5 text-[#22C55E]" />
                  <h3 className="font-medium text-[#22C55E]">Verification Code Sent!</h3>
                </div>
                <p className="text-sm text-[#94a3b8] pl-8">
                  We've sent a verification code to <strong className="text-white">{email}</strong>
                </p>
              </div>

              <div className="space-y-3">
                <label htmlFor="verification-code" className="block text-sm font-medium text-white">
                  Enter Verification Code
                </label>
                <motion.div
                  className="relative"
                  initial={{ scale: 0.95 }}
                  animate={{ scale: 1 }}
                  transition={{ type: "spring", stiffness: 400, damping: 10 }}
                >
                  <motion.input
                    whileFocus={{ scale: 1.02 }}
                    transition={{ type: "spring", stiffness: 400, damping: 10 }}
                    id="verification-code"
                    type="text"
                    value={verificationCode}
                    onChange={(e) => setVerificationCode(e.target.value)}
                    className="block w-full rounded-md border border-[#1e3a6f] bg-[#112240] p-4 text-center text-2xl tracking-widest text-white placeholder-[#94a3b8] focus:border-[#22C55E] focus:outline-none focus:ring-1 focus:ring-[#22C55E] font-mono"
                    placeholder="••••••"
                    maxLength={6}
                    required
                  />
                </motion.div>
                <p className="text-xs text-[#94a3b8] text-center">Enter the 6-digit code sent to your email</p>
              </div>

              <div className="flex gap-3">
                <Button
                  type="submit"
                  className="flex-1 rounded-md bg-[#22C55E] px-5 py-2.5 text-center text-sm font-medium text-white hover:bg-[#4ADE80] focus:outline-none focus:ring-4 focus:ring-[#22C55E]/50 transition-all duration-200 hover:scale-105"
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Verifying...
                    </>
                  ) : (
                    'Verify Code'
                  )}
                </Button>
                <Button
                  type="button"
                  className="flex-1 rounded-md bg-[#112240] px-5 py-2.5 text-center text-sm font-medium text-white hover:bg-[#1e3a6f] focus:outline-none focus:ring-4 focus:ring-[#112240]/50 transition-all duration-200"
                  onClick={() => setResetStep('email')}
                >
                  Back
                </Button>
              </div>

              <div className="mt-2 text-center">
                <button
                  type="button"
                  className="text-sm text-[#22C55E] hover:text-[#4ADE80] transition-colors duration-200 flex items-center justify-center gap-1 mx-auto"
                  onClick={() => setResetStep('email')}
                >
                  <AlertCircle className="h-3 w-3" />
                  Didn't receive a code? Try again
                </button>
              </div>
            </form>
          </motion.div>
        )}
      </AnimatePresence>

      <AnimatePresence mode="wait">
        {resetStep === 'newPassword' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
          >
            {/* Step 3: Enter new password */}
            <form onSubmit={handleSetNewPassword} className="space-y-6">
              <div className="mb-4 rounded-md bg-[#3b82f6]/10 p-4 border border-[#3b82f6]/30">
                <div className="flex items-center gap-3 mb-2">
                  <Shield className="h-5 w-5 text-[#3b82f6]" />
                  <h3 className="font-medium text-[#3b82f6]">Create a New Password</h3>
                </div>
                <p className="text-sm text-[#94a3b8] pl-8">
                  Choose a strong password that you haven't used before.
                </p>
              </div>

              <div className="space-y-3">
                <label htmlFor="new-password" className="block text-sm font-medium text-white">
                  New Password
                </label>
                <div className="relative">
                  <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                    <Lock className="h-5 w-5 text-[#94a3b8]" />
                  </div>
                  <motion.input
                    whileFocus={{ scale: 1.01 }}
                    transition={{ type: "spring", stiffness: 400, damping: 10 }}
                    id="new-password"
                    type={showPassword ? 'text' : 'password'}
                    value={newPassword}
                    onChange={(e) => setNewPassword(e.target.value)}
                    className="block w-full rounded-md border border-[#1e3a6f] bg-[#112240] p-2.5 pl-10 pr-10 text-white placeholder-[#94a3b8] focus:border-[#22C55E] focus:outline-none focus:ring-1 focus:ring-[#22C55E]"
                    placeholder="••••••••"
                    required
                    minLength={8}
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-0 flex items-center pr-3 text-[#94a3b8] hover:text-white transition-colors duration-200"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                  </button>
                </div>

                {/* Password strength meter */}
                <PasswordStrengthMeter password={newPassword} />
              </div>

              <div className="flex gap-3">
                <Button
                  type="submit"
                  className="flex-1 rounded-md bg-[#22C55E] px-5 py-2.5 text-center text-sm font-medium text-white hover:bg-[#4ADE80] focus:outline-none focus:ring-4 focus:ring-[#22C55E]/50 transition-all duration-200 hover:scale-105"
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Resetting...
                    </>
                  ) : (
                    'Reset Password'
                  )}
                </Button>
                <Button
                  type="button"
                  className="flex-1 rounded-md bg-[#112240] px-5 py-2.5 text-center text-sm font-medium text-white hover:bg-[#1e3a6f] focus:outline-none focus:ring-4 focus:ring-[#112240]/50 transition-all duration-200"
                  onClick={() => setResetStep('code')}
                >
                  Back
                </Button>
              </div>
            </form>
          </motion.div>
        )}
      </AnimatePresence>

      <AnimatePresence mode="wait">
        {resetStep === 'success' && (
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5 }}
            className="space-y-6"
          >
            {/* Step 4: Success message */}
            <div className="rounded-md bg-green-500/10 p-6 text-center text-green-500 border border-green-500/20">
              <SuccessCheckmark />

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5, duration: 0.5 }}
              >
                <h3 className="mb-2 text-xl font-semibold">{success}</h3>
                <p className="text-[#94a3b8]">You can now sign in with your new password.</p>
                <p className="mt-2 text-sm text-[#94a3b8]">Click the button below to go to the sign-in page.</p>
              </motion.div>
            </div>

            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.8, duration: 0.5 }}
            >
              <Button
                type="button"
                className="w-full rounded-md bg-[#22C55E] px-5 py-3 text-center text-sm font-medium text-white hover:bg-[#4ADE80] focus:outline-none focus:ring-4 focus:ring-[#22C55E]/50 transition-all duration-300 hover:scale-105"
                onClick={handleReturnToSignIn}
                disabled={isLoading}
              >
                {isLoading ? 'Redirecting...' : 'Go to Sign In Page'}
              </Button>
            </motion.div>

            {/* Confetti animation */}
            <Confetti duration={5000} />
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}

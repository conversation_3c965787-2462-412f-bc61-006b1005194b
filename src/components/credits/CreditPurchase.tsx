'use client';

import { useState } from 'react';
import { Zap, Check, Star } from 'lucide-react';

interface CreditPackage {
  id: string;
  name: string;
  credits: number;
  price: number;
  description: string;
  popular: boolean;
}

const CREDIT_PACKAGES: CreditPackage[] = [
  {
    id: 'small',
    name: '500 Credits',
    credits: 500,
    price: 5.00,
    description: 'Perfect for occasional use',
    popular: false,
  },
  {
    id: 'medium',
    name: '1,200 Credits',
    credits: 1200,
    price: 10.00,
    description: 'Great value for regular users',
    popular: true,
  },
  {
    id: 'large',
    name: '3,000 Credits',
    credits: 3000,
    price: 20.00,
    description: 'Best for heavy usage',
    popular: false,
  },
  {
    id: 'xlarge',
    name: '7,500 Credits',
    credits: 7500,
    price: 40.00,
    description: 'Maximum value pack',
    popular: false,
  },
];

interface CreditPurchaseProps {
  currentCredits: number;
  onClose?: () => void;
}

export function CreditPurchase({ currentCredits, onClose }: CreditPurchaseProps) {
  const [loading, setLoading] = useState<string | null>(null);

  const handlePurchase = async (packageId: string) => {
    setLoading(packageId);

    try {
      console.log(`🛒 Purchasing credit package: ${packageId}`);

      const response = await fetch('/api/purchase-credits', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ packageId }),
      });

      const result = await response.json();

      if (result.success && result.checkoutUrl) {
        console.log('✅ Redirecting to Stripe checkout:', result.checkoutUrl);
        window.location.href = result.checkoutUrl;
      } else {
        console.error('❌ Failed to create checkout session:', result.error);
        alert(`Error: ${result.error || 'Failed to create checkout session'}`);
      }
    } catch (error) {
      console.error('❌ Error purchasing credits:', error);
      alert(`Error: ${error instanceof Error ? error.message : 'Something went wrong'}`);
    } finally {
      setLoading(null);
    }
  };

  const calculateValue = (credits: number, price: number) => {
    const baseRate = 500 / 5; // 100 credits per dollar for small package
    const actualRate = credits / price;
    const bonus = ((actualRate - baseRate) / baseRate) * 100;
    return Math.round(bonus);
  };

  return (
    <div className="bg-gray-900 rounded-lg p-6 border border-gray-700">
      <div className="text-center mb-6">
        <div className="flex items-center justify-center mb-2">
          <Zap className="h-6 w-6 text-yellow-400 mr-2" />
          <h2 className="text-2xl font-bold text-white">Purchase Credits</h2>
        </div>
        <p className="text-gray-400">
          You currently have <span className="text-white font-semibold">{currentCredits}</span> credits
        </p>
        <p className="text-sm text-gray-500 mt-1">
          Credits expire 30 days after purchase
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {CREDIT_PACKAGES.map((package_) => {
          const bonus = calculateValue(package_.credits, package_.price);
          const isLoading = loading === package_.id;

          return (
            <div
              key={package_.id}
              className={`relative bg-gray-800 rounded-lg p-4 border transition-all duration-200 hover:scale-105 ${
                package_.popular
                  ? 'border-purple-500 ring-2 ring-purple-500/20'
                  : 'border-gray-600 hover:border-gray-500'
              }`}
            >
              {package_.popular && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <div className="bg-purple-500 text-white px-3 py-1 rounded-full text-xs font-semibold flex items-center">
                    <Star className="h-3 w-3 mr-1" />
                    POPULAR
                  </div>
                </div>
              )}

              <div className="text-center">
                <h3 className="text-lg font-bold text-white mb-1">{package_.name}</h3>
                <p className="text-sm text-gray-400 mb-3">{package_.description}</p>

                <div className="mb-4">
                  <div className="text-3xl font-bold text-white">${package_.price}</div>
                  <div className="text-sm text-gray-400">
                    {package_.credits.toLocaleString()} credits
                  </div>
                  {bonus > 0 && (
                    <div className="text-xs text-green-400 font-semibold mt-1">
                      +{bonus}% bonus credits
                    </div>
                  )}
                </div>

                <div className="space-y-2 mb-4">
                  <div className="flex items-center text-sm text-gray-300">
                    <Check className="h-4 w-4 text-green-400 mr-2" />
                    <span>30-day expiration</span>
                  </div>
                  <div className="flex items-center text-sm text-gray-300">
                    <Check className="h-4 w-4 text-green-400 mr-2" />
                    <span>Instant delivery</span>
                  </div>
                  <div className="flex items-center text-sm text-gray-300">
                    <Check className="h-4 w-4 text-green-400 mr-2" />
                    <span>Secure payment</span>
                  </div>
                </div>

                <button
                  onClick={() => handlePurchase(package_.id)}
                  disabled={isLoading}
                  className={`w-full py-2 px-4 rounded-lg font-semibold transition-all duration-200 ${
                    package_.popular
                      ? 'bg-purple-600 hover:bg-purple-700 text-white'
                      : 'bg-gray-700 hover:bg-gray-600 text-white'
                  } ${
                    isLoading
                      ? 'opacity-50 cursor-not-allowed'
                      : 'hover:scale-105'
                  }`}
                >
                  {isLoading ? (
                    <div className="flex items-center justify-center">
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                      Processing...
                    </div>
                  ) : (
                    'Purchase Now'
                  )}
                </button>
              </div>
            </div>
          );
        })}
      </div>

      {onClose && (
        <div className="text-center mt-6">
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white transition-colors"
          >
            Close
          </button>
        </div>
      )}

      <div className="mt-6 p-4 bg-gray-800 rounded-lg">
        <h4 className="text-sm font-semibold text-white mb-2">💡 Credit Usage Guide</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-xs text-gray-400">
          <div>• 1 image = ~1 credit</div>
          <div>• 1 second video = ~60 credits</div>
          <div>• HD processing = standard rate</div>
          <div>• 4K processing = 2x rate</div>
        </div>
      </div>
    </div>
  );
}

'use client';

import { useEffect, useState } from 'react';
import { motion } from 'framer-motion';

export function SuccessCheckmark() {
  const [animate, setAnimate] = useState(false);

  useEffect(() => {
    // Start animation after component mounts
    const timer = setTimeout(() => setAnimate(true), 100);
    return () => clearTimeout(timer);
  }, []);

  const circleVariants = {
    hidden: {
      scale: 0,
      opacity: 0
    },
    visible: {
      scale: 1,
      opacity: 1,
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    }
  };

  const checkVariants = {
    hidden: {
      pathLength: 0,
      opacity: 0
    },
    visible: {
      pathLength: 1,
      opacity: 1,
      transition: {
        delay: 0.3,
        duration: 0.8,
        ease: "easeInOut"
      }
    }
  };

  const pulseVariants = {
    hidden: {
      scale: 0.8,
      opacity: 0
    },
    visible: {
      scale: [1, 1.1, 1],
      opacity: [0.7, 0.3, 0],
      transition: {
        delay: 0.5,
        duration: 2,
        repeat: Infinity,
        repeatDelay: 0.5
      }
    }
  };

  const sparkleVariants = {
    hidden: {
      scale: 0,
      opacity: 0
    },
    visible: (i: number) => ({
      scale: [0, 1, 0],
      opacity: [0, 1, 0],
      transition: {
        delay: 0.8 + (i * 0.1),
        duration: 1.5,
        repeat: Infinity,
        repeatDelay: 2
      }
    })
  };

  // Generate sparkle positions
  const sparkles = Array.from({ length: 8 }).map((_, i) => {
    const angle = (i * Math.PI * 2) / 8;
    const distance = 55;
    return {
      id: i,
      x: 50 + Math.cos(angle) * distance,
      y: 50 + Math.sin(angle) * distance,
      size: Math.random() * 3 + 2
    };
  });

  return (
    <div className="flex justify-center items-center">
      <div className="relative">
        {/* Pulse effect */}
        <motion.div
          className="absolute inset-0 flex items-center justify-center"
          initial="hidden"
          animate={animate ? "visible" : "hidden"}
        >
          <motion.div
            className="absolute w-24 h-24 rounded-full bg-[#22C55E]/20"
            variants={pulseVariants}
          />
          <motion.div
            className="absolute w-28 h-28 rounded-full bg-[#22C55E]/10"
            variants={pulseVariants}
            style={{ animationDelay: "0.2s" }}
          />
          <motion.div
            className="absolute w-32 h-32 rounded-full bg-[#22C55E]/5"
            variants={pulseVariants}
            style={{ animationDelay: "0.4s" }}
          />
        </motion.div>

        {/* Main checkmark */}
        <motion.svg
          className="w-24 h-24 relative z-10"
          viewBox="0 0 100 100"
          initial="hidden"
          animate={animate ? "visible" : "hidden"}
        >
          {/* Gradient definition */}
          <defs>
            <linearGradient id="checkmarkGradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" stopColor="#22C55E" />
              <stop offset="100%" stopColor="#4ADE80" />
            </linearGradient>
            <filter id="glow">
              <feGaussianBlur stdDeviation="2.5" result="coloredBlur" />
              <feMerge>
                <feMergeNode in="coloredBlur" />
                <feMergeNode in="SourceGraphic" />
              </feMerge>
            </filter>
          </defs>

          {/* Background circle with subtle gradient */}
          <motion.circle
            cx="50"
            cy="50"
            r="40"
            fill="rgba(34, 197, 94, 0.1)"
            stroke="url(#checkmarkGradient)"
            strokeWidth="5"
            variants={circleVariants}
            filter="url(#glow)"
          />

          {/* Checkmark with gradient */}
          <motion.path
            d="M30 50 L45 65 L70 35"
            fill="none"
            stroke="url(#checkmarkGradient)"
            strokeWidth="6"
            strokeLinecap="round"
            strokeLinejoin="round"
            variants={checkVariants}
            filter="url(#glow)"
          />

          {/* Sparkles around the checkmark */}
          {sparkles.map((sparkle) => (
            <motion.circle
              key={sparkle.id}
              cx={sparkle.x}
              cy={sparkle.y}
              r={sparkle.size}
              fill="#ffffff"
              custom={sparkle.id}
              variants={sparkleVariants}
            />
          ))}
        </motion.svg>
      </div>
    </div>
  );
}

'use client';

import { useEffect, useState } from 'react';
import ReactConfetti from 'react-confetti';

interface ConfettiProps {
  duration?: number;
  intensity?: 'low' | 'medium' | 'high';
}

export function Confetti({ duration = 3000, intensity = 'medium' }: ConfettiProps) {
  const [dimensions, setDimensions] = useState({ width: 0, height: 0 });
  const [showConfetti, setShowConfetti] = useState(true);
  const [confettiProps, setConfettiProps] = useState({
    numberOfPieces: 200,
    recycle: true,
    tweenDuration: 5000,
  });

  // Set confetti properties based on intensity
  useEffect(() => {
    const intensitySettings = {
      low: {
        numberOfPieces: 100,
        recycle: false,
        tweenDuration: 3000,
      },
      medium: {
        numberOfPieces: 200,
        recycle: true,
        tweenDuration: 5000,
      },
      high: {
        numberOfPieces: 500,
        recycle: true,
        tweenDuration: 7000,
      },
    };

    setConfettiProps(intensitySettings[intensity]);

    // For high intensity, we'll keep the confetti going longer
    if (intensity === 'high') {
      // After initial burst, reduce the number of pieces
      const reduceTimer = setTimeout(() => {
        setConfettiProps(prev => ({
          ...prev,
          numberOfPieces: 200,
        }));
      }, 3000);

      return () => clearTimeout(reduceTimer);
    }
  }, [intensity]);

  useEffect(() => {
    // Set dimensions
    const updateDimensions = () => {
      setDimensions({
        width: window.innerWidth,
        height: window.innerHeight,
      });
    };

    updateDimensions();
    window.addEventListener('resize', updateDimensions);

    // Hide confetti after duration
    const timer = setTimeout(() => {
      // For a smoother fade-out, first stop recycling
      setConfettiProps(prev => ({
        ...prev,
        recycle: false,
      }));

      // Then completely hide after a delay
      const hideTimer = setTimeout(() => setShowConfetti(false), 2000);
      return () => clearTimeout(hideTimer);
    }, duration);

    return () => {
      window.removeEventListener('resize', updateDimensions);
      clearTimeout(timer);
    };
  }, [duration]);

  if (!showConfetti) return null;

  return (
    <ReactConfetti
      width={dimensions.width}
      height={dimensions.height}
      recycle={confettiProps.recycle}
      numberOfPieces={confettiProps.numberOfPieces}
      tweenDuration={confettiProps.tweenDuration}
      gravity={0.15}
      initialVelocityY={intensity === 'high' ? 20 : 10}
      friction={0.97}
      wind={0.01}
      colors={[
        '#22C55E', // Primary green
        '#4ADE80', // Light green
        '#10B981', // Dark green
        '#3b82f6', // Blue
        '#60a5fa', // Light blue
        '#A7F3D0', // Very light green
        '#ECFDF5', // Almost white green
        '#ffffff', // White
        '#fcd34d', // Gold
      ]}
      shapes={['square', 'circle']}
    />
  );
}

'use client';

import { useEffect, useState } from 'react';
import { motion } from 'framer-motion';

interface PasswordStrengthMeterProps {
  password: string;
}

export function PasswordStrengthMeter({ password }: PasswordStrengthMeterProps) {
  const [strength, setStrength] = useState(0);
  const [message, setMessage] = useState('');
  const [color, setColor] = useState('#94a3b8');

  useEffect(() => {
    if (!password) {
      setStrength(0);
      setMessage('');
      setColor('#94a3b8');
      return;
    }

    // Calculate password strength
    let score = 0;

    // Length check
    if (password.length >= 8) score += 1;
    if (password.length >= 12) score += 1;

    // Complexity checks
    if (/[A-Z]/.test(password)) score += 1;
    if (/[a-z]/.test(password)) score += 1;
    if (/[0-9]/.test(password)) score += 1;
    if (/[^A-Za-z0-9]/.test(password)) score += 1;

    // Calculate the raw score (0-6)
    const maxScore = 6;

    // Set message and color based on strength
    if (score < 2) {
      setMessage('Weak');
      setColor('#ef4444'); // red
      // For weak passwords, show proportional strength
      setStrength(Math.floor((score / maxScore) * 100));
    } else if (score < 4) {
      setMessage('Fair');
      setColor('#f59e0b'); // amber
      // For fair passwords, show at least 50% of the bar
      setStrength(Math.max(50, Math.floor((score / maxScore) * 100)));
    } else if (score < 5) {
      setMessage('Good');
      setColor('#3b82f6'); // blue
      // For good passwords, show at least 75% of the bar
      setStrength(Math.max(75, Math.floor((score / maxScore) * 100)));
    } else {
      setMessage('Strong');
      setColor('#22C55E'); // green
      // For strong passwords (score 5-6), always show 100%
      setStrength(100);
    }
  }, [password]);

  return (
    <div className="w-full space-y-1">
      <div className="h-1.5 w-full bg-[#112240] rounded-full overflow-hidden">
        <motion.div
          className="h-full rounded-full"
          style={{ backgroundColor: color }}
          initial={{ width: 0 }}
          animate={{ width: `${strength}%` }}
          transition={{ duration: 0.3 }}
        />
      </div>
      {message && (
        <motion.p
          className="text-xs"
          style={{ color }}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.3 }}
        >
          {message}
        </motion.p>
      )}
    </div>
  );
}

'use client';

import { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { SuccessCheckmark } from './SuccessCheckmark';
import { Confetti } from './Confetti';
import { FloatingBadge } from './FloatingBadge';
import { Gift, Zap, Shield, CheckCircle2 } from 'lucide-react';

interface VerificationSuccessProps {
  message: string;
  subMessage?: string;
  onContinue: () => void;
  isLoading?: boolean;
}

export function VerificationSuccess({
  message,
  subMessage = 'Your account has been successfully created.',
  onContinue,
  isLoading = false
}: VerificationSuccessProps) {
  const [showCredits, setShowCredits] = useState(false);
  const [showButton, setShowButton] = useState(false);
  const [countdown, setCountdown] = useState(5);

  // Auto-redirect after success with a countdown
  useEffect(() => {
    if (isLoading) return;

    // Show credits animation after the checkmark animation
    const creditsTimer = setTimeout(() => setShowCredits(true), 1200);

    // Show button after credits animation
    const buttonTimer = setTimeout(() => setShowButton(true), 2000);

    // Start countdown for auto-redirect
    const countdownInterval = setInterval(() => {
      setCountdown(prev => {
        if (prev <= 1) {
          clearInterval(countdownInterval);
          onContinue();
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => {
      clearTimeout(creditsTimer);
      clearTimeout(buttonTimer);
      clearInterval(countdownInterval);
    };
  }, [onContinue, isLoading]);

  // Particle animation for the background
  const particles = Array.from({ length: 20 }).map((_, i) => ({
    id: i,
    x: Math.random() * 100,
    y: Math.random() * 100,
    size: Math.random() * 10 + 5,
    duration: Math.random() * 20 + 10,
  }));

  return (
    <div className="relative overflow-hidden rounded-xl p-6 space-y-6">
      {/* Animated background particles */}
      <div className="absolute inset-0 overflow-hidden">
        {particles.map((particle) => (
          <motion.div
            key={particle.id}
            className="absolute rounded-full bg-gradient-to-r from-[#22C55E]/10 to-[#3b82f6]/10"
            style={{
              left: `${particle.x}%`,
              top: `${particle.y}%`,
              width: `${particle.size}px`,
              height: `${particle.size}px`,
            }}
            animate={{
              x: [0, Math.random() * 100 - 50],
              y: [0, Math.random() * 100 - 50],
              opacity: [0, 0.5, 0],
            }}
            transition={{
              duration: particle.duration,
              repeat: Infinity,
              repeatType: "reverse",
            }}
          />
        ))}
      </div>

      {/* Main content with glowing border */}
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5 }}
        className="relative rounded-xl bg-[#0a192f]/90 p-8 text-center border-2 border-[#22C55E]/30 shadow-[0_0_15px_rgba(34,197,94,0.3)] backdrop-blur-sm"
      >
        {/* Floating achievement badge */}
        <FloatingBadge delay={1.8} />
        {/* Animated gradient background */}
        <div className="absolute inset-0 bg-gradient-to-r from-[#22C55E]/5 to-[#3b82f6]/5 rounded-xl overflow-hidden">
          <motion.div
            className="absolute inset-0 bg-gradient-to-r from-[#22C55E]/10 to-[#3b82f6]/10"
            animate={{
              backgroundPosition: ['0% 0%', '100% 100%'],
            }}
            transition={{
              duration: 15,
              repeat: Infinity,
              repeatType: "reverse"
            }}
          />
        </div>

        {/* Success checkmark with enhanced animation */}
        <div className="relative mb-6">
          <motion.div
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.5 }}
            className="relative z-10"
          >
            <SuccessCheckmark />
          </motion.div>

          {/* Glowing ring around checkmark */}
          <motion.div
            className="absolute inset-0 rounded-full bg-[#22C55E]/20 blur-md z-0"
            initial={{ scale: 0.5, opacity: 0 }}
            animate={{ scale: 1.2, opacity: 0.7 }}
            transition={{
              duration: 1.5,
              repeat: Infinity,
              repeatType: "reverse"
            }}
          />
        </div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5, duration: 0.5 }}
          className="relative z-10"
        >
          <h3 className="mb-2 text-2xl font-bold text-white">{message}</h3>
          <p className="text-[#94a3b8] text-lg">{subMessage}</p>
        </motion.div>

        {/* Credits animation */}
        <AnimatePresence>
          {showCredits && (
            <motion.div
              initial={{ opacity: 0, scale: 0.8, y: 20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.8, y: -20 }}
              transition={{
                type: "spring",
                stiffness: 300,
                damping: 15
              }}
              className="mt-6 p-4 rounded-lg bg-gradient-to-r from-[#22C55E]/20 to-[#3b82f6]/20 border border-[#22C55E]/30"
            >
              <div className="flex items-center justify-center mb-2">
                <motion.div
                  initial={{ rotate: -10, scale: 0.9 }}
                  animate={{ rotate: 10, scale: 1.1 }}
                  transition={{
                    duration: 0.8,
                    repeat: Infinity,
                    repeatType: "reverse"
                  }}
                >
                  <Gift className="h-8 w-8 text-[#22C55E] mr-2" />
                </motion.div>
                <h4 className="text-xl font-bold text-white">Congratulations!</h4>
              </div>

              <motion.p
                className="text-[#94a3b8] mb-3"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.3 }}
              >
                Your account has been created and verified successfully.
              </motion.p>

              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5 }}
                className="bg-[#112240] p-3 rounded-lg border border-[#1e3a6f] flex items-center justify-center"
              >
                <Zap className="h-5 w-5 text-[#22C55E] mr-2" />
                <span className="text-white font-bold">50 FREE CREDITS</span>
                <Zap className="h-5 w-5 text-[#22C55E] ml-2" />
              </motion.div>

              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.8 }}
                className="mt-4 flex justify-center space-x-6 text-xs text-[#94a3b8]"
              >
                <div className="flex items-center">
                  <Shield className="h-4 w-4 text-[#22C55E] mr-1" />
                  <span>Privacy Protection</span>
                </div>
                <div className="flex items-center">
                  <CheckCircle2 className="h-4 w-4 text-[#22C55E] mr-1" />
                  <span>Ready to Use</span>
                </div>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>

        <motion.p
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 1.5, duration: 0.5 }}
          className="mt-4 text-sm text-[#94a3b8]"
        >
          Redirecting to dashboard in {countdown} seconds...
        </motion.p>
      </motion.div>

      <AnimatePresence>
        {showButton && (
          <motion.button
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ type: "spring", stiffness: 300, damping: 15 }}
            className="relative w-full rounded-md bg-gradient-to-r from-[#22C55E] to-[#22C55E] px-5 py-3 text-center text-sm font-medium text-white focus:outline-none focus:ring-4 focus:ring-[#22C55E]/50 transition-all duration-300 hover:scale-105 shadow-lg shadow-[#22C55E]/20"
            onClick={onContinue}
            disabled={isLoading}
            whileHover={{
              scale: 1.05,
              boxShadow: "0 0 20px rgba(34, 197, 94, 0.5)"
            }}
            whileTap={{ scale: 0.98 }}
          >
            {isLoading ? 'Redirecting...' : 'Go to Dashboard Now'}

            {/* Button shine effect */}
            <motion.div
              className="absolute inset-0 overflow-hidden rounded-md"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.2 }}
            >
              <motion.div
                className="absolute top-0 left-0 w-20 h-full bg-white/20 skew-x-30 transform -translate-x-32"
                animate={{ translateX: ["0%", "250%"] }}
                transition={{
                  duration: 1.5,
                  repeat: Infinity,
                  repeatDelay: 3
                }}
              />
            </motion.div>
          </motion.button>
        )}
      </AnimatePresence>

      {/* Enhanced confetti animation with high intensity */}
      <Confetti duration={8000} intensity="high" />
    </div>
  );
}

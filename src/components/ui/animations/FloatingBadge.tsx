'use client';

import { motion } from 'framer-motion';
import { Shield, Zap } from 'lucide-react';

interface FloatingBadgeProps {
  delay?: number;
}

export function FloatingBadge({ delay = 1.5 }: FloatingBadgeProps) {
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.8, y: 20 }}
      animate={{ opacity: 1, scale: 1, y: 0 }}
      transition={{
        delay,
        type: "spring",
        stiffness: 300,
        damping: 15
      }}
      className="absolute -top-16 right-0 transform rotate-12"
    >
      <div className="relative">
        {/* Badge background with glow */}
        <div className="absolute inset-0 rounded-full bg-[#22C55E]/20 blur-md"></div>
        
        {/* Badge */}
        <motion.div
          className="relative flex items-center justify-center w-24 h-24 rounded-full bg-gradient-to-br from-[#22C55E] to-[#3b82f6] p-1 shadow-lg"
          animate={{ 
            rotate: [0, 5, 0, -5, 0],
            y: [0, -5, 0, -3, 0]
          }}
          transition={{ 
            duration: 5, 
            repeat: Infinity,
            repeatType: "reverse" 
          }}
        >
          <div className="absolute inset-0.5 rounded-full bg-[#0a192f] flex items-center justify-center overflow-hidden">
            {/* Inner gradient background */}
            <div className="absolute inset-0 bg-gradient-to-br from-[#22C55E]/10 to-[#3b82f6]/10"></div>
            
            {/* Badge content */}
            <div className="relative z-10 flex flex-col items-center justify-center">
              <Shield className="h-6 w-6 text-[#22C55E] mb-1" />
              <div className="text-xs font-bold text-white">VERIFIED</div>
              <div className="flex items-center mt-1">
                <Zap className="h-3 w-3 text-[#22C55E]" />
                <span className="text-[10px] text-[#94a3b8] mx-0.5">50</span>
                <Zap className="h-3 w-3 text-[#22C55E]" />
              </div>
            </div>
          </div>
        </motion.div>
        
        {/* Sparkles */}
        <motion.div
          className="absolute -top-1 -right-1 w-3 h-3 rounded-full bg-white"
          animate={{ 
            scale: [1, 1.5, 1],
            opacity: [0.7, 1, 0.7]
          }}
          transition={{ 
            duration: 2, 
            repeat: Infinity,
            repeatType: "reverse" 
          }}
        />
        <motion.div
          className="absolute bottom-1 -left-1 w-2 h-2 rounded-full bg-[#22C55E]"
          animate={{ 
            scale: [1, 1.3, 1],
            opacity: [0.7, 1, 0.7]
          }}
          transition={{ 
            duration: 1.5, 
            repeat: Infinity,
            repeatType: "reverse",
            delay: 0.5
          }}
        />
      </div>
    </motion.div>
  );
}

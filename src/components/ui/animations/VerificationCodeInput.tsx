'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Mail, AlertCircle } from 'lucide-react';

interface VerificationCodeInputProps {
  code: string;
  setCode: (code: string) => void;
  email: string;
  onResendCode: () => void;
  isResending?: boolean;
}

export function VerificationCodeInput({ 
  code, 
  setCode, 
  email, 
  onResendCode,
  isResending = false
}: VerificationCodeInputProps) {
  const [isFocused, setIsFocused] = useState(false);
  const [showAnimation, setShowAnimation] = useState(false);
  
  // Start animation after component mounts
  useEffect(() => {
    const timer = setTimeout(() => setShowAnimation(true), 100);
    return () => clearTimeout(timer);
  }, []);

  // Format code with spaces for better readability
  const formatCode = (value: string) => {
    // Remove any non-alphanumeric characters
    const cleaned = value.replace(/[^a-zA-Z0-9]/g, '');
    
    // Limit to 6 characters
    const limited = cleaned.slice(0, 6);
    
    // Add a space after every 3 characters
    if (limited.length > 3) {
      return `${limited.slice(0, 3)} ${limited.slice(3)}`;
    }
    
    return limited;
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    // Remove spaces before setting the code
    setCode(e.target.value.replace(/\s/g, ''));
  };

  return (
    <div className="space-y-4">
      <AnimatePresence>
        {showAnimation && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="mb-4 rounded-md bg-[#22C55E]/10 p-4 border border-[#22C55E]/30"
          >
            <div className="flex items-center gap-3 mb-2">
              <Mail className="h-5 w-5 text-[#22C55E]" />
              <h3 className="font-medium text-[#22C55E]">Verification Code Sent!</h3>
            </div>
            <p className="text-sm text-[#94a3b8] pl-8">
              We've sent a verification code to <strong className="text-white">{email}</strong>
            </p>
          </motion.div>
        )}
      </AnimatePresence>

      <div className="space-y-3">
        <label htmlFor="verification-code" className="block text-sm font-medium text-white">
          Enter Verification Code
        </label>
        <motion.div
          className="relative"
          initial={{ scale: 0.95 }}
          animate={{ scale: 1 }}
          transition={{ type: "spring", stiffness: 400, damping: 10 }}
        >
          <motion.input
            whileFocus={{ scale: 1.02 }}
            transition={{ type: "spring", stiffness: 400, damping: 10 }}
            id="verification-code"
            type="text"
            value={formatCode(code)}
            onChange={handleChange}
            onFocus={() => setIsFocused(true)}
            onBlur={() => setIsFocused(false)}
            className="block w-full rounded-md border border-[#1e3a6f] bg-[#112240] p-4 text-center text-2xl tracking-widest text-white placeholder-[#94a3b8] focus:border-[#22C55E] focus:outline-none focus:ring-1 focus:ring-[#22C55E] font-mono"
            placeholder="••••••"
            maxLength={7} // 6 digits + 1 space
            required
          />
          
          <motion.div 
            className="absolute bottom-0 left-0 h-1 bg-[#22C55E] rounded-b-md"
            initial={{ width: 0 }}
            animate={{ width: isFocused ? '100%' : '0%' }}
            transition={{ duration: 0.3 }}
          />
        </motion.div>
        
        <motion.p 
          className="text-xs text-[#94a3b8] text-center"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.3, duration: 0.5 }}
        >
          Enter the 6-digit code sent to your email
        </motion.p>
      </div>

      <div className="mt-2 text-center">
        <motion.button
          type="button"
          className="text-sm text-[#22C55E] hover:text-[#4ADE80] transition-colors duration-200 flex items-center justify-center gap-1 mx-auto"
          onClick={onResendCode}
          disabled={isResending}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <AlertCircle className="h-3 w-3" />
          {isResending ? 'Sending...' : "Didn't receive a code? Resend"}
        </motion.button>
      </div>
    </div>
  );
}

'use client';

import { useState, useEffect } from 'react';
import { Check, ArrowUp, ArrowDown, RefreshCw, Crown, Zap, Star } from 'lucide-react';

interface Plan {
  id: string;
  name: string;
  price_monthly: number;
  credits_included: number;
  features: string[];
  popular?: boolean;
}

interface PlanManagerProps {
  currentPlan: string;
  currentCredits: number;
  onPlanChange?: () => void;
}

const PLAN_HIERARCHY = {
  free: 0,
  standard: 1,
  pro: 2,
  premium: 3,
};

export function PlanManager({ currentPlan, currentCredits, onPlanChange }: PlanManagerProps) {
  const [plans, setPlans] = useState<Plan[]>([]);
  const [loading, setLoading] = useState(true);
  const [changingTo, setChangingTo] = useState<string | null>(null);

  useEffect(() => {
    fetchPlans();
  }, []);

  const fetchPlans = async () => {
    try {
      // Mock plans data - in real app, fetch from API
      const mockPlans: Plan[] = [
        {
          id: 'free',
          name: 'Free',
          price_monthly: 0,
          credits_included: 50,
          features: ['50 credits/month', 'Basic processing', 'Standard support'],
        },
        {
          id: 'standard',
          name: 'Standard',
          price_monthly: 12,
          credits_included: 700,
          features: ['700 credits/month', 'Fast processing', 'Email support'],
          popular: true,
        },
        {
          id: 'pro',
          name: 'Pro',
          price_monthly: 192,
          credits_included: 3500,
          features: ['3,500 credits/month', 'Priority processing', 'Priority support', 'API access'],
        },
        {
          id: 'premium',
          name: 'Premium',
          price_monthly: 561.60,
          credits_included: 14500,
          features: ['14,500 credits/month', 'Fastest processing', 'Dedicated support', 'Custom integrations'],
        },
      ];
      
      setPlans(mockPlans);
      setLoading(false);
    } catch (error) {
      console.error('Error fetching plans:', error);
      setLoading(false);
    }
  };

  const getChangeType = (targetPlan: string): 'upgrade' | 'downgrade' | 'switch' => {
    const currentLevel = PLAN_HIERARCHY[currentPlan.toLowerCase() as keyof typeof PLAN_HIERARCHY] || 0;
    const targetLevel = PLAN_HIERARCHY[targetPlan as keyof typeof PLAN_HIERARCHY] || 0;
    
    if (targetLevel > currentLevel) return 'upgrade';
    if (targetLevel < currentLevel) return 'downgrade';
    return 'switch';
  };

  const handlePlanChange = async (targetPlan: string) => {
    if (targetPlan === currentPlan.toLowerCase()) return;

    setChangingTo(targetPlan);
    
    try {
      const changeType = getChangeType(targetPlan);
      console.log(`🔄 Changing plan: ${currentPlan} → ${targetPlan} (${changeType})`);

      const response = await fetch('/api/change-plan', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          targetPlan,
          changeType,
        }),
      });

      const result = await response.json();

      if (result.success) {
        if (result.requiresPayment && result.checkoutUrl) {
          console.log('💳 Redirecting to payment:', result.checkoutUrl);
          window.location.href = result.checkoutUrl;
        } else {
          console.log('✅ Plan changed successfully:', result);
          alert(`✅ Successfully ${changeType}d to ${targetPlan} plan!`);
          onPlanChange?.();
          window.location.reload();
        }
      } else {
        console.error('❌ Plan change failed:', result.error);
        alert(`❌ Failed to change plan: ${result.error}`);
      }
    } catch (error) {
      console.error('❌ Error changing plan:', error);
      alert(`❌ Error: ${error instanceof Error ? error.message : 'Something went wrong'}`);
    } finally {
      setChangingTo(null);
    }
  };

  const getPlanIcon = (planId: string) => {
    switch (planId) {
      case 'free': return <Star className="h-5 w-5" />;
      case 'standard': return <Zap className="h-5 w-5" />;
      case 'pro': return <Crown className="h-5 w-5" />;
      case 'premium': return <Crown className="h-5 w-5 text-yellow-400" />;
      default: return <Star className="h-5 w-5" />;
    }
  };

  const getChangeIcon = (targetPlan: string) => {
    const changeType = getChangeType(targetPlan);
    switch (changeType) {
      case 'upgrade': return <ArrowUp className="h-4 w-4" />;
      case 'downgrade': return <ArrowDown className="h-4 w-4" />;
      default: return <RefreshCw className="h-4 w-4" />;
    }
  };

  const getButtonText = (targetPlan: string) => {
    const changeType = getChangeType(targetPlan);
    const plan = plans.find(p => p.id === targetPlan);
    
    if (targetPlan === currentPlan.toLowerCase()) {
      return 'Current Plan';
    }
    
    switch (changeType) {
      case 'upgrade':
        return `Upgrade to ${plan?.name}`;
      case 'downgrade':
        return `Downgrade to ${plan?.name}`;
      default:
        return `Switch to ${plan?.name}`;
    }
  };

  const getButtonColor = (targetPlan: string) => {
    const changeType = getChangeType(targetPlan);
    
    if (targetPlan === currentPlan.toLowerCase()) {
      return 'bg-gray-600 cursor-not-allowed';
    }
    
    switch (changeType) {
      case 'upgrade':
        return 'bg-green-600 hover:bg-green-700';
      case 'downgrade':
        return 'bg-orange-600 hover:bg-orange-700';
      default:
        return 'bg-blue-600 hover:bg-blue-700';
    }
  };

  if (loading) {
    return (
      <div className="bg-gray-900 rounded-lg p-6 border border-gray-700">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-700 rounded mb-4"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {[1, 2, 3, 4].map(i => (
              <div key={i} className="h-64 bg-gray-700 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-gray-900 rounded-lg p-6 border border-gray-700">
      <div className="text-center mb-6">
        <h2 className="text-2xl font-bold text-white mb-2">Manage Your Plan</h2>
        <p className="text-gray-400">
          Current plan: <span className="text-white font-semibold">{currentPlan}</span> • 
          Credits: <span className="text-white font-semibold">{currentCredits.toLocaleString()}</span>
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {plans.map((plan) => {
          const isCurrentPlan = plan.id === currentPlan.toLowerCase();
          const changeType = getChangeType(plan.id);
          const isChanging = changingTo === plan.id;

          return (
            <div
              key={plan.id}
              className={`relative bg-gray-800 rounded-lg p-4 border transition-all duration-200 ${
                isCurrentPlan
                  ? 'border-purple-500 ring-2 ring-purple-500/20'
                  : plan.popular
                  ? 'border-blue-500 ring-2 ring-blue-500/20'
                  : 'border-gray-600 hover:border-gray-500'
              }`}
            >
              {plan.popular && !isCurrentPlan && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <div className="bg-blue-500 text-white px-3 py-1 rounded-full text-xs font-semibold">
                    POPULAR
                  </div>
                </div>
              )}

              {isCurrentPlan && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <div className="bg-purple-500 text-white px-3 py-1 rounded-full text-xs font-semibold">
                    CURRENT
                  </div>
                </div>
              )}

              <div className="text-center">
                <div className="flex items-center justify-center mb-2">
                  {getPlanIcon(plan.id)}
                  <h3 className="text-lg font-bold text-white ml-2">{plan.name}</h3>
                </div>

                <div className="mb-4">
                  <div className="text-3xl font-bold text-white">
                    ${plan.price_monthly}
                    {plan.price_monthly > 0 && <span className="text-sm text-gray-400">/month</span>}
                  </div>
                  <div className="text-sm text-gray-400">
                    {plan.credits_included.toLocaleString()} credits
                  </div>
                </div>

                <div className="space-y-2 mb-4">
                  {plan.features.map((feature, index) => (
                    <div key={index} className="flex items-center text-sm text-gray-300">
                      <Check className="h-4 w-4 text-green-400 mr-2" />
                      <span>{feature}</span>
                    </div>
                  ))}
                </div>

                <button
                  onClick={() => handlePlanChange(plan.id)}
                  disabled={isCurrentPlan || isChanging}
                  className={`w-full py-2 px-4 rounded-lg font-semibold transition-all duration-200 ${getButtonColor(plan.id)} ${
                    isChanging ? 'opacity-50 cursor-not-allowed' : ''
                  }`}
                >
                  {isChanging ? (
                    <div className="flex items-center justify-center">
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                      Processing...
                    </div>
                  ) : (
                    <div className="flex items-center justify-center">
                      {!isCurrentPlan && getChangeIcon(plan.id)}
                      <span className={!isCurrentPlan ? 'ml-2' : ''}>{getButtonText(plan.id)}</span>
                    </div>
                  )}
                </button>
              </div>
            </div>
          );
        })}
      </div>

      <div className="mt-6 p-4 bg-gray-800 rounded-lg">
        <h4 className="text-sm font-semibold text-white mb-2">💡 Plan Change Rules</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-xs text-gray-400">
          <div>• Upgrades require payment</div>
          <div>• Downgrades are immediate</div>
          <div>• Credits are preserved</div>
          <div>• New plan credits are added</div>
        </div>
      </div>
    </div>
  );
}

'use client';

import { useEffect, useState } from 'react';
import { useAuth } from '@clerk/nextjs';

export function ClerkSubscriptionSync() {
  const { isLoaded, userId } = useAuth();
  const [syncStatus, setSyncStatus] = useState<'idle' | 'syncing' | 'success' | 'error'>('idle');
  const [lastSync, setLastSync] = useState<string | null>(null);

  useEffect(() => {
    if (isLoaded && userId) {
      console.log('🔄 ClerkSubscriptionSync: Component loaded for user', userId);

      // Always sync on page load to catch plan changes
      const sessionKey = `clerk_sync_${userId}`;
      const lastSyncTime = sessionStorage.getItem(sessionKey);
      const now = Date.now();
      const thirtySecondsAgo = now - 30 * 1000; // Reduced to 30 seconds

      console.log('🔄 ClerkSubscriptionSync: Last sync time:', lastSyncTime ? new Date(parseInt(lastSyncTime)).toLocaleString() : 'Never');

      // Sync if we haven't synced in the last 30 seconds OR if it's a fresh page load
      const shouldSync = !lastSyncTime || parseInt(lastSyncTime) < thirtySecondsAgo || !document.referrer.includes(window.location.origin);

      if (shouldSync) {
        console.log('🔄 ClerkSubscriptionSync: Starting sync...');
        syncSubscription();
      } else {
        console.log('🔄 ClerkSubscriptionSync: Sync not needed, last sync was recent');
        setLastSync(new Date(parseInt(lastSyncTime)).toLocaleTimeString());
      }
    }
  }, [isLoaded, userId]);

  const syncSubscription = async () => {
    if (!userId) return;

    setSyncStatus('syncing');

    try {
      console.log('🔄 Syncing Clerk subscription...');

      const response = await fetch('/api/sync-clerk-subscription-v2', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const result = await response.json();

      if (result.success) {
        console.log('✅ Subscription synced successfully:', result);
        setSyncStatus('success');

        // Store sync time in session storage
        const sessionKey = `clerk_sync_${userId}`;
        sessionStorage.setItem(sessionKey, Date.now().toString());
        setLastSync(new Date().toLocaleTimeString());

        // Refresh the page if subscription was updated
        if (!result.noChangeNeeded) {
          console.log('🔄 Subscription updated, refreshing page...');
          setTimeout(() => {
            window.location.reload();
          }, 1000);
        }
      } else {
        console.error('❌ Subscription sync failed:', result.error);
        setSyncStatus('error');
      }
    } catch (error) {
      console.error('❌ Error syncing subscription:', error);
      setSyncStatus('error');
    }
  };

  // Always show sync status for debugging
  // if (process.env.NODE_ENV === 'production') {
  //   return null;
  // }

  // Show sync status in development
  return (
    <div className="fixed bottom-4 right-4 bg-navy-light border border-navy rounded-lg p-3 text-sm z-50">
      <div className="flex items-center gap-2">
        {syncStatus === 'syncing' && (
          <>
            <div className="w-3 h-3 border-2 border-blue-400 border-t-transparent rounded-full animate-spin"></div>
            <span className="text-blue-400">Syncing subscription...</span>
          </>
        )}
        {syncStatus === 'success' && (
          <>
            <div className="w-3 h-3 bg-green-400 rounded-full"></div>
            <span className="text-green-400">Subscription synced</span>
            {lastSync && <span className="text-gray-400">({lastSync})</span>}
          </>
        )}
        {syncStatus === 'error' && (
          <>
            <div className="w-3 h-3 bg-red-400 rounded-full"></div>
            <span className="text-red-400">Sync failed</span>
            <button
              onClick={syncSubscription}
              className="text-blue-400 hover:text-blue-300 underline ml-2"
            >
              Retry
            </button>
          </>
        )}
        {syncStatus === 'idle' && (
          <>
            <div className="w-3 h-3 bg-gray-400 rounded-full"></div>
            <span className="text-gray-400">
              {lastSync ? `Last sync: ${lastSync}` : 'Ready to sync'}
            </span>
            <button
              onClick={() => {
                // Clear session storage and force sync
                const sessionKey = `clerk_sync_${userId}`;
                sessionStorage.removeItem(sessionKey);
                syncSubscription();
              }}
              className="text-blue-400 hover:text-blue-300 underline ml-2 text-xs"
            >
              Force Sync
            </button>
          </>
        )}
      </div>
    </div>
  );
}

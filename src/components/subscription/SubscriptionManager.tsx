'use client';

import { useState, useEffect } from 'react';
import { useUser } from '@clerk/nextjs';
import { createClient } from '@/utils/supabase/client';

interface SubscriptionData {
  subscription_type: string;
  subscription_status: string;
  credits: number;
  credits_expire_at: string | null;
  next_billing_date: string | null;
  stripe_subscription_id: string | null;
  trial_end: string | null;
}

interface PaymentHistory {
  id: string;
  amount: number;
  currency: string;
  status: string;
  description: string;
  created_at: string;
}

export function SubscriptionManager() {
  const { user } = useUser();
  const [subscriptionData, setSubscriptionData] = useState<SubscriptionData | null>(null);
  const [paymentHistory, setPaymentHistory] = useState<PaymentHistory[]>([]);
  const [loading, setLoading] = useState(true);
  const [canceling, setCanceling] = useState(false);

  useEffect(() => {
    if (user) {
      fetchSubscriptionData();
      fetchPaymentHistory();
    }
  }, [user]);

  const fetchSubscriptionData = async () => {
    try {
      const supabase = createClient();
      const { data, error } = await supabase
        .from('users')
        .select(`
          subscription_type,
          subscription_status,
          credits,
          credits_expire_at,
          next_billing_date,
          stripe_subscription_id,
          trial_end
        `)
        .eq('id', user?.id)
        .single();

      if (error) {
        console.error('Error fetching subscription data:', error);
        return;
      }

      setSubscriptionData(data);
    } catch (error) {
      console.error('Error:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchPaymentHistory = async () => {
    try {
      const supabase = createClient();
      const { data, error } = await supabase
        .from('payment_history')
        .select('id, amount, currency, status, description, created_at')
        .eq('user_id', user?.id)
        .order('created_at', { ascending: false })
        .limit(10);

      if (error) {
        console.error('Error fetching payment history:', error);
        return;
      }

      setPaymentHistory(data || []);
    } catch (error) {
      console.error('Error:', error);
    }
  };

  const handleCancelSubscription = async () => {
    if (!subscriptionData?.stripe_subscription_id) return;

    setCanceling(true);
    try {
      const response = await fetch('/api/subscription/cancel', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          subscriptionId: subscriptionData.stripe_subscription_id,
        }),
      });

      if (response.ok) {
        await fetchSubscriptionData();
        alert('Subscription cancelled successfully. You will retain access until the end of your billing period.');
      } else {
        alert('Failed to cancel subscription. Please try again.');
      }
    } catch (error) {
      console.error('Error canceling subscription:', error);
      alert('Failed to cancel subscription. Please try again.');
    } finally {
      setCanceling(false);
    }
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString();
  };

  const formatAmount = (amount: number, currency: string = 'usd') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency.toUpperCase(),
    }).format(amount / 100);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-green-400';
      case 'trialing': return 'text-blue-400';
      case 'past_due': return 'text-yellow-400';
      case 'canceled': return 'text-red-400';
      case 'unpaid': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return '✅';
      case 'trialing': return '🆓';
      case 'past_due': return '⚠️';
      case 'canceled': return '❌';
      case 'unpaid': return '🚫';
      default: return '❓';
    }
  };

  if (loading) {
    return (
      <div className="bg-navy-light rounded-lg p-6 border border-navy-light">
        <div className="animate-pulse">
          <div className="h-6 bg-navy rounded w-1/3 mb-4"></div>
          <div className="space-y-3">
            <div className="h-4 bg-navy rounded w-full"></div>
            <div className="h-4 bg-navy rounded w-2/3"></div>
            <div className="h-4 bg-navy rounded w-1/2"></div>
          </div>
        </div>
      </div>
    );
  }

  if (!subscriptionData) {
    return (
      <div className="bg-navy-light rounded-lg p-6 border border-navy-light">
        <h3 className="text-xl font-bold text-white mb-4">❌ Subscription Information</h3>
        <p className="text-gray-300">Unable to load subscription data.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Current Subscription */}
      <div className="bg-navy-light rounded-lg p-6 border border-navy-light">
        <h3 className="text-xl font-bold text-white mb-6">💳 Current Subscription</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <div>
              <label className="text-gray-400 text-sm">Plan</label>
              <p className="text-white font-semibold text-lg">{subscriptionData.subscription_type}</p>
            </div>
            
            <div>
              <label className="text-gray-400 text-sm">Status</label>
              <p className={`font-semibold text-lg ${getStatusColor(subscriptionData.subscription_status)}`}>
                {getStatusIcon(subscriptionData.subscription_status)} {subscriptionData.subscription_status}
              </p>
            </div>

            <div>
              <label className="text-gray-400 text-sm">Credits</label>
              <p className="text-white font-semibold text-lg">
                {subscriptionData.credits.toLocaleString()}
                {subscriptionData.credits_expire_at && (
                  <span className="text-gray-400 text-sm ml-2">
                    (expires {formatDate(subscriptionData.credits_expire_at)})
                  </span>
                )}
              </p>
            </div>
          </div>

          <div className="space-y-4">
            {subscriptionData.trial_end && (
              <div>
                <label className="text-gray-400 text-sm">Trial Ends</label>
                <p className="text-blue-400 font-semibold">{formatDate(subscriptionData.trial_end)}</p>
              </div>
            )}

            {subscriptionData.next_billing_date && (
              <div>
                <label className="text-gray-400 text-sm">Next Billing</label>
                <p className="text-white font-semibold">{formatDate(subscriptionData.next_billing_date)}</p>
              </div>
            )}

            {subscriptionData.stripe_subscription_id && subscriptionData.subscription_status === 'active' && (
              <div>
                <button
                  onClick={handleCancelSubscription}
                  disabled={canceling}
                  className="bg-red-500 hover:bg-red-600 disabled:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors"
                >
                  {canceling ? 'Canceling...' : 'Cancel Subscription'}
                </button>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Payment History */}
      <div className="bg-navy-light rounded-lg p-6 border border-navy-light">
        <h3 className="text-xl font-bold text-white mb-6">📋 Payment History</h3>
        
        {paymentHistory.length === 0 ? (
          <p className="text-gray-400">No payment history available.</p>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-navy">
                  <th className="text-left text-gray-400 pb-3">Date</th>
                  <th className="text-left text-gray-400 pb-3">Description</th>
                  <th className="text-left text-gray-400 pb-3">Amount</th>
                  <th className="text-left text-gray-400 pb-3">Status</th>
                </tr>
              </thead>
              <tbody>
                {paymentHistory.map((payment) => (
                  <tr key={payment.id} className="border-b border-navy/50">
                    <td className="py-3 text-gray-300">
                      {formatDate(payment.created_at)}
                    </td>
                    <td className="py-3 text-white">
                      {payment.description}
                    </td>
                    <td className="py-3 text-white font-semibold">
                      {formatAmount(payment.amount, payment.currency)}
                    </td>
                    <td className="py-3">
                      <span className={`px-2 py-1 rounded text-xs font-semibold ${
                        payment.status === 'succeeded' 
                          ? 'bg-green-500/20 text-green-400' 
                          : payment.status === 'failed'
                          ? 'bg-red-500/20 text-red-400'
                          : 'bg-yellow-500/20 text-yellow-400'
                      }`}>
                        {payment.status}
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
}

'use client';

import { useEffect } from 'react';

export function ScrollAnimations() {
  useEffect(() => {
    const animateOnScroll = () => {
      const elements = document.querySelectorAll('.scroll-animate');

      elements.forEach(element => {
        const elementPosition = element.getBoundingClientRect().top;
        const windowHeight = window.innerHeight;

        // Trigger animation when element is 98% of the way up the screen
        // This makes elements start to appear much earlier as you scroll
        if (elementPosition < windowHeight * 0.98) {
          element.classList.add('animate-in');
        }
      });
    };

    // Run once on initial load with a slight delay to ensure proper rendering
    setTimeout(animateOnScroll, 300);

    // Add scroll event listener with throttling for better performance
    let scrollTimeout: NodeJS.Timeout | null = null;
    const handleScroll = () => {
      if (!scrollTimeout) {
        scrollTimeout = setTimeout(() => {
          animateOnScroll();
          scrollTimeout = null;
        }, 10); // Small throttle for smoother performance
      }
    };

    window.addEventListener('scroll', handleScroll, { passive: true });

    // Clean up
    return () => {
      window.removeEventListener('scroll', handleScroll);
      if (scrollTimeout) clearTimeout(scrollTimeout);
    };
  }, []);

  return null; // This component doesn't render anything
}

'use client';

import { useRouter } from 'next/navigation';
import { useState, useRef, useCallback } from 'react';
import { Upload } from 'lucide-react';

interface UploadRedirectProps {
  signUpUrl: string;
}

export function UploadRedirect({ signUpUrl }: UploadRedirectProps) {
  const router = useRouter();
  const [isDragging, setIsDragging] = useState(false);
  const [blurPrompt, setBlurPrompt] = useState('');
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Redirect to signup page
  const redirectToSignup = useCallback(() => {
    router.push(signUpUrl);
  }, [router, signUpUrl]);

  // Handle drag events
  const handleDragEnter = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
  }, []);

  const handleDragOver = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    if (!isDragging) {
      setIsDragging(true);
    }
  }, [isDragging]);

  const handleDrop = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
    redirectToSignup();
  }, [redirectToSignup]);

  // Handle click to upload
  const handleUploadClick = useCallback(() => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  }, []);

  // Handle file input change
  const handleFileInputChange = useCallback(() => {
    redirectToSignup();
  }, [redirectToSignup]);

  return (
    <div className="w-full max-w-3xl mx-auto my-12">
      <div
        className={`relative border-2 border-dashed rounded-xl p-10 transition-all duration-200 ${
          isDragging
            ? 'border-[#22C55E] bg-[#22C55E]/10'
            : 'border-[#1e3a6f] bg-[#0a192f]/80'
        }`}
        onDragEnter={handleDragEnter}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <div className="flex flex-col items-center justify-center text-center">
          <div className="mb-4 bg-[#112240] p-4 rounded-full shadow-lg shadow-blue-500/20">
            <Upload className="h-10 w-10 text-[#22C55E]" />
          </div>

          <h3 className="text-xl md:text-2xl font-bold text-white mb-2">
            Upload original video/photo/GIF
          </h3>

          <div className="text-[#94a3b8] text-sm mb-4 space-y-1">
            <p>Video: Max 30 minutes, 1 GB</p>
            <p>Photo: Max 20 MB</p>
            <p>GIF: Max 20 MB</p>
          </div>

          {/* Text input for blur prompt */}
          <div className="w-full max-w-md mb-6">
            <label htmlFor="blur-prompt" className="block text-sm font-medium text-[#e2e8f0] mb-2">
              What would you like to blur?
            </label>
            <input
              type="text"
              id="blur-prompt"
              value={blurPrompt}
              onChange={(e) => setBlurPrompt(e.target.value)}
              placeholder="E.g., blur the lady with red dress, license plates, text on screen..."
              className="w-full px-4 py-3 bg-[#112240] border border-[#1e3a6f] rounded-md text-white placeholder-[#94a3b8] focus:outline-none focus:ring-2 focus:ring-[#3b82f6] focus:border-transparent transition-colors"
            />
          </div>

          <div className="group relative">
            <div className="absolute -inset-1 rounded-md bg-gradient-to-r from-blue-400 to-blue-600 opacity-70 blur-sm transition-opacity duration-300 group-hover:opacity-100"></div>
            <button
              onClick={handleUploadClick}
              className="relative z-10 bg-[#3b82f6] hover:bg-[#2563eb] text-white font-medium py-3 px-6 rounded-md transition-all duration-200 flex items-center shadow-lg hover:shadow-blue-500/50"
            >
              <Upload className="h-5 w-5 mr-2" />
              Upload File
            </button>
          </div>

          <p className="mt-3 text-[#94a3b8] text-sm">
            or drag and drop files
          </p>

          <p className="mt-4 text-[#94a3b8] text-xs">
            Up to 5 files at a time
          </p>
        </div>

        {/* Hidden file input */}
        <input
          type="file"
          ref={fileInputRef}
          onChange={handleFileInputChange}
          className="hidden"
          multiple
          accept="image/*,video/*"
        />
      </div>
    </div>
  );
}

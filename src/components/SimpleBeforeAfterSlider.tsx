"use client"

import { useState, useRef, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/Card"
import { Slider } from "@/components/ui/Slider"

interface SimpleBeforeAfterSliderProps {
  originalImage: string
  blurredImage: string
}

export function SimpleBeforeAfterSlider({
  originalImage,
  blurredImage
}: SimpleBeforeAfterSliderProps) {
  const [sliderValue, setSliderValue] = useState(50)
  const [isDragging, setIsDragging] = useState(false)
  const containerRef = useRef<HTMLDivElement>(null)

  // Handle mouse events
  const handleMouseDown = () => {
    setIsDragging(true)
  }

  const handleMouseUp = () => {
    setIsDragging(false)
  }

  const updateSliderPosition = (clientX: number) => {
    if (!containerRef.current) return

    const rect = containerRef.current.getBoundingClientRect()
    const position = ((clientX - rect.left) / rect.width) * 100
    setSliderValue(Math.max(0, Math.min(100, position)))
  }

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!isDragging) return
    updateSliderPosition(e.clientX)
  }

  // Handle touch events
  const handleTouchMove = (e: React.TouchEvent) => {
    if (!isDragging) return
    // Safely access touch data
    const touch = e.touches?.[0]
    if (!touch) return
    updateSliderPosition(touch.clientX)
  }

  // Global event listeners
  useEffect(() => {
    const handleGlobalMouseUp = () => {
      setIsDragging(false)
    }

    const handleGlobalMouseMove = (e: MouseEvent) => {
      if (isDragging) {
        updateSliderPosition(e.clientX)
      }
    }

    window.addEventListener('mouseup', handleGlobalMouseUp)
    window.addEventListener('mousemove', handleGlobalMouseMove)

    return () => {
      window.removeEventListener('mouseup', handleGlobalMouseUp)
      window.removeEventListener('mousemove', handleGlobalMouseMove)
    }
  }, [isDragging])

  return (
    <div className="mx-auto mt-16 max-w-4xl">
      <Card className="overflow-hidden border-navy-light bg-navy shadow-xl">
        <CardContent className="p-0">
          {/* Image container */}
          <div
            ref={containerRef}
            className="relative aspect-video w-full overflow-hidden cursor-ew-resize"
            onMouseDown={handleMouseDown}
            onMouseUp={handleMouseUp}
            onMouseMove={handleMouseMove}
            onTouchStart={handleMouseDown}
            onTouchEnd={handleMouseUp}
            onTouchMove={handleTouchMove}
          >
            {/* Original image (background) */}
            <img
              src={originalImage}
              alt="Original content"
              className="absolute inset-0 h-full w-full object-cover"
            />

            {/* Blurred image (foreground with clip) */}
            <div
              className="absolute inset-0 h-full"
              style={{
                width: `${sliderValue}%`,
                overflow: 'hidden'
              }}
            >
              <img
                src={blurredImage}
                alt="Blurred content"
                className="h-full object-cover"
                style={{
                  width: `${100 / (sliderValue / 100)}%`,
                  maxWidth: 'none'
                }}
              />
            </div>

            {/* Divider line */}
            <div
              className="absolute top-0 bottom-0 w-0.5 bg-white"
              style={{
                left: `${sliderValue}%`,
                boxShadow: '0 0 10px rgba(0, 0, 0, 0.3)'
              }}
            >
              {/* Handle */}
              <div
                className="absolute top-1/2 left-1/2 h-10 w-10 -translate-x-1/2 -translate-y-1/2 rounded-full border-4 border-white bg-green-400 shadow-lg flex items-center justify-center"
                onMouseDown={(e) => {
                  e.stopPropagation()
                  handleMouseDown()
                }}
                onTouchStart={(e) => {
                  e.stopPropagation()
                  handleMouseDown()
                }}
              >
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M8 5L3 10L8 15M16 5L21 10L16 15"
                    stroke="#0a192f"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              </div>
            </div>
          </div>

          {/* Slider control */}
          <div className="bg-navy-light p-6">
            <Slider
              value={[sliderValue]}
              onValueChange={(value) => {
                if (value && value.length > 0 && typeof value[0] === 'number') {
                  setSliderValue(value[0])
                }
              }}
              min={0}
              max={100}
              step={1}
              className="py-4"
            />
            <div className="mt-2 flex justify-between text-sm text-gray-300">
              <span>Protected Content</span>
              <span>Original Content</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Stats */}
      <div className="mt-8 flex flex-wrap justify-center gap-4 text-center">
        <div className="min-w-[180px] rounded-lg bg-navy-light p-4">
          <div className="text-2xl font-bold text-green-400">99.8%</div>
          <div className="text-sm text-gray-300">Detection Accuracy</div>
        </div>
        <div className="min-w-[180px] rounded-lg bg-navy-light p-4">
          <div className="text-2xl font-bold text-green-400">&lt;50ms</div>
          <div className="text-sm text-gray-300">Processing Time</div>
        </div>
        <div className="min-w-[180px] rounded-lg bg-navy-light p-4">
          <div className="text-2xl font-bold text-green-400">GDPR</div>
          <div className="text-sm text-gray-300">Compliant</div>
        </div>
      </div>
    </div>
  )
}

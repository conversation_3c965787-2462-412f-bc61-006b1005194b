"use client"

import { useState } from "react"
import { Card, CardContent } from "./ui/card"
import { YouTubeVideo } from "@/components/YouTubeVideo"

export function Demo() {
  const [sliderValue, setSliderValue] = useState(50)

  return (
    <section id="demo" className="bg-navy-dark py-24">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mx-auto max-w-3xl text-center">
          <h2 className="text-3xl font-bold tracking-tight text-white sm:text-4xl">See Guardiavision in Action</h2>
          <p className="mt-4 text-lg text-gray-300">
            Drag the slider to reveal how our AI precisely blurs sensitive content while preserving the integrity of
            your media.
          </p>
        </div>

        {/* YouTube Video Demo */}
        <div className="mt-12 mb-16">
          <Card className="mx-auto max-w-4xl overflow-hidden border-navy-light bg-navy">
            <CardContent className="p-4">
              <h3 className="text-xl font-bold text-white mb-4">Watch Our Demo Video</h3>
              <YouTubeVideo videoId="mvF6Vd01yks" title="Guardiavision Demo" />
            </CardContent>
          </Card>
        </div>

        {/* Interactive Slider Demo */}
        <div className="mt-12">
          <Card className="mx-auto max-w-4xl overflow-hidden border-navy-light bg-navy">
            <CardContent className="p-0">
              <div className="relative">
                <div className="relative h-[400px] w-full overflow-hidden">
                  {/* Original image */}
                  <img
                    src="/images/demo-original.jpg"
                    alt="Original image"
                    className="absolute inset-0 h-full w-full object-cover"
                  />

                  {/* Blurred image with clip-path controlled by slider */}
                  <div
                    className="absolute inset-0 h-full w-full"
                    style={{
                      clipPath: `inset(0 ${100 - sliderValue}% 0 0)`,
                    }}
                  >
                    <img
                      src="/images/demo-blurred.jpg"
                      alt="Blurred image"
                      className="h-full w-full object-cover"
                    />
                  </div>

                  {/* Slider control line */}
                  <div
                    className="absolute bottom-0 top-0 w-1 cursor-ew-resize bg-green-400"
                    style={{ left: `${sliderValue}%` }}
                    onMouseDown={(e) => {
                      e.preventDefault()
                      const handleMouseMove = (moveEvent: MouseEvent) => {
                        const rect = e.currentTarget.parentElement?.getBoundingClientRect()
                        if (rect) {
                          const x = moveEvent.clientX - rect.left
                          const newValue = Math.max(0, Math.min(100, (x / rect.width) * 100))
                          setSliderValue(newValue)
                        }
                      }

                      const handleMouseUp = () => {
                        document.removeEventListener("mousemove", handleMouseMove)
                        document.removeEventListener("mouseup", handleMouseUp)
                      }

                      document.addEventListener("mousemove", handleMouseMove)
                      document.addEventListener("mouseup", handleMouseUp)
                    }}
                  >
                    <div className="absolute left-1/2 top-1/2 h-8 w-8 -translate-x-1/2 -translate-y-1/2 rounded-full bg-green-400"></div>
                  </div>

                  {/* Labels */}
                  <div className="absolute bottom-4 left-4 rounded-md bg-navy-dark bg-opacity-75 px-3 py-1 text-sm text-white">
                    Original
                  </div>
                  <div className="absolute bottom-4 right-4 rounded-md bg-navy-dark bg-opacity-75 px-3 py-1 text-sm text-white">
                    Protected
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  )
}

"use client"

import { useState } from "react"
import Link from "next/link"
import Image from "next/image"
import { useParams } from "next/navigation"
import { Button } from "./ui/button"
import { Menu, X } from "lucide-react"

export function Navbar() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const params = useParams()
  // Handle the case where params might be null or locale might not exist
  const locale = params && typeof params.locale === 'string' ? params.locale : 'en'

  return (
    <nav className="sticky top-0 z-50 border-b border-navy-light bg-navy/90 backdrop-blur-md">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex h-16 items-center justify-between">
          <div className="flex items-center">
            <Link href="/" className="flex items-center">
              <Image src="/images/logo.png" alt="Guardiavision Logo" width={40} height={40} className="h-10 w-10" />
              <span className="ml-3 text-xl font-bold text-white">Guardiavision</span>
            </Link>
          </div>

          <div className="hidden md:block">
            <div className="ml-10 flex items-center space-x-4">
              <Link href="#features" className="text-gray-300 hover:text-white">
                Features
              </Link>
              <Link href="#demo" className="text-gray-300 hover:text-white">
                Demo
              </Link>
              <Link href="#pricing" className="text-gray-300 hover:text-white">
                Pricing
              </Link>
              <Link href="#testimonials" className="text-gray-300 hover:text-white">
                Testimonials
              </Link>
              <Link href={`/${locale}/sign-in`} className="text-gray-300 hover:text-white">
                Sign In
              </Link>
              <Link href={`/${locale}/sign-up`}>
                <Button className="bg-green-400 text-navy hover:bg-green-500">Get Started</Button>
              </Link>
            </div>
          </div>

          <div className="md:hidden">
            <button
              type="button"
              className="text-gray-300 hover:text-white"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
            >
              {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile menu */}
      {isMenuOpen && (
        <div className="md:hidden">
          <div className="space-y-1 px-2 pb-3 pt-2">
            <Link
              href="#features"
              className="block rounded-md px-3 py-2 text-base font-medium text-gray-300 hover:bg-navy-light hover:text-white"
              onClick={() => setIsMenuOpen(false)}
            >
              Features
            </Link>
            <Link
              href="#demo"
              className="block rounded-md px-3 py-2 text-base font-medium text-gray-300 hover:bg-navy-light hover:text-white"
              onClick={() => setIsMenuOpen(false)}
            >
              Demo
            </Link>
            <Link
              href="#pricing"
              className="block rounded-md px-3 py-2 text-base font-medium text-gray-300 hover:bg-navy-light hover:text-white"
              onClick={() => setIsMenuOpen(false)}
            >
              Pricing
            </Link>
            <Link
              href="#testimonials"
              className="block rounded-md px-3 py-2 text-base font-medium text-gray-300 hover:bg-navy-light hover:text-white"
              onClick={() => setIsMenuOpen(false)}
            >
              Testimonials
            </Link>
            <Link
              href={`/${locale}/sign-in`}
              className="block rounded-md px-3 py-2 text-base font-medium text-gray-300 hover:bg-navy-light hover:text-white"
              onClick={() => setIsMenuOpen(false)}
            >
              Sign In
            </Link>
            <Link
              href={`/${locale}/sign-up`}
              className="block rounded-md px-3 py-2 text-base font-medium text-gray-300 hover:bg-navy-light hover:text-white"
              onClick={() => setIsMenuOpen(false)}
            >
              <Button className="w-full bg-green-400 text-navy hover:bg-green-500">Get Started</Button>
            </Link>
          </div>
        </div>
      )}
    </nav>
  )
}

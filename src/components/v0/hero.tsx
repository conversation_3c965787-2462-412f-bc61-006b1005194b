'use client';

import { Lock, Play, Shield } from 'lucide-react';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import React from 'react';

import { Button } from './ui/button';

export function Hero() {
  const params = useParams();
  // Handle the case where params might be null or locale might not exist
  const locale = params && typeof params.locale === 'string' ? params.locale : 'en';
  return (
    <div className="relative overflow-hidden bg-navy">
      {/* Pixelated background pattern inspired by logo */}
      <div className="absolute inset-0 opacity-10">
        <div className="size-full">
          {Array.from({ length: 20 }).map((_, rowIndex) => (
            <div key={`row-${rowIndex}`} className="flex">
              {Array.from({ length: 30 }).map((_, colIndex) => {
                const opacity = Math.random();
                const size = Math.floor(Math.random() * 3) + 1;
                const isVisible = Math.random() > 0.6;
                return isVisible
                  ? (
                      <div
                        key={`pixel-${rowIndex}-${colIndex}`}
                        className={`h-${size} w-${size} m-1 rounded-sm`}
                        style={{
                          backgroundColor: Math.random() > 0.5 ? '#4ADE80' : '#60A5FA',
                          opacity: opacity * 0.8 + 0.2,
                        }}
                      />
                    )
                  : null;
              })}
            </div>
          ))}
        </div>
      </div>

      <div className="container relative mx-auto px-4 py-24 sm:px-6 lg:px-8 lg:py-32">
        <div className="grid grid-cols-1 gap-12 lg:grid-cols-2 lg:gap-8">
          <div className="flex flex-col justify-center">
            <div className="mb-6 inline-flex items-center rounded-full border border-navy-light bg-navy-dark px-3 py-1 text-sm text-gray-300">
              <Shield className="mr-1 size-3.5 text-green-400" />
              <span>Privacy-first content protection</span>
            </div>

            <h1 className="mb-6 text-4xl font-extrabold tracking-tight text-white sm:text-5xl md:text-6xl">
              <span className="block">AI-Powered Privacy</span>
              <span className="block bg-gradient-to-r from-blue-400 to-green-400 bg-clip-text text-transparent">
                For Your Visual Content
              </span>
            </h1>

            <p className="mb-8 max-w-2xl text-xl text-gray-300">
              Automatically blur or remove sensitive content in images and videos. Protect identities, secure
              information, and maintain compliance with ease.
            </p>

            <div className="flex flex-col space-y-4 sm:flex-row sm:space-x-4 sm:space-y-0">
              <Link href={`/${locale}/sign-up`}>
                <Button size="lg" className="bg-green-400 text-navy hover:bg-green-500">
                  Start Blurring Now
                </Button>
              </Link>
              <a
                href="https://youtube.com/shorts/mvF6Vd01yks"
                target="_blank"
                rel="noopener noreferrer"
              >
                <Button
                  size="lg"
                  variant="outline"
                  className="border-blue-400 text-blue-400 hover:bg-blue-400 hover:text-navy"
                >
                  <Play className="mr-2 size-4" />
                  Watch Demo
                </Button>
              </a>
            </div>

            <div className="mt-8 flex items-center text-sm text-gray-400">
              <Lock className="mr-2 size-4 text-green-400" />
              <span>No credit card required • Cancel anytime</span>
            </div>
          </div>

          <div className="flex items-center justify-center">
            <div className="relative">
              <div className="absolute -inset-1 rounded-xl bg-gradient-to-r from-blue-400 to-green-400 opacity-70 blur-lg"></div>
              <div className="relative overflow-hidden rounded-lg border border-navy-light bg-navy-dark shadow-xl">
                <div className="flex items-center justify-between border-b border-navy-light bg-navy p-2">
                  <div className="flex space-x-1.5">
                    <div className="size-3 rounded-full bg-red-500"></div>
                    <div className="size-3 rounded-full bg-yellow-500"></div>
                    <div className="size-3 rounded-full bg-green-500"></div>
                  </div>
                  <div className="text-xs text-gray-400">Guardiavision Demo</div>
                  <div className="w-16"></div>
                </div>
                <div className="p-4">
                  <a
                    href="https://youtube.com/shorts/mvF6Vd01yks"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="group relative block cursor-pointer"
                  >
                    <div className="absolute inset-0 z-10 flex items-center justify-center">
                      <div className="rounded-full bg-blue-500 bg-opacity-80 p-4 shadow-lg transition-transform group-hover:scale-110">
                        <Play className="size-8 text-white" />
                      </div>
                    </div>
                    <div className="absolute inset-0 rounded-md bg-black bg-opacity-30 transition-all duration-300 group-hover:bg-opacity-20"></div>
                    <img
                      src="https://img.youtube.com/vi/mvF6Vd01yks/hqdefault.jpg"
                      alt="Demo Video Preview"
                      className="w-full rounded-md shadow-lg"
                      width={500}
                      height={300}
                    />
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

    </div>
  );
}

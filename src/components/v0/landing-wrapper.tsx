"use client"

// Commented out unused import
// import { useParams } from 'next/navigation'
import { <PERSON> } from './hero'
import { Demo } from './demo'
import { Features } from './features'
import { Navbar } from './navbar'
import { Footer } from './footer'
// Commented out unused import
// import { FAQ } from '@/templates/FAQ'
// Commented out unused import
// import { CTA } from '@/templates/CTA'
// Commented out unused import
// import { Pricing } from '@/templates/Pricing'

export function LandingWrapper() {
  // Commented out unused variables
  // const params = useParams()
  // const locale = params.locale as string || 'en'
  return (
    <div className="min-h-screen bg-navy text-white">
      <Navbar />
      <Hero />
      <Demo />
      <Features />
      <div className="bg-navy text-white">
        {/* We'll add the other components back once we've fixed the hydration issues */}
      </div>
      <Footer />
    </div>
  )
}

"use client"

import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardTitle } from "./ui/card"
import { Shield, Zap, Code, Layers, Settings, Lock } from "lucide-react"

export function Features() {
  const features = [
    {
      icon: <Settings className="h-6 w-6 text-green-400" />,
      title: "Customizable Blur",
      description: "Specify exactly what you want to blur or remove: faces, license plates, text, or entire people.",
    },
    {
      icon: <Zap className="h-6 w-6 text-green-400" />,
      title: "Real-Time Processing",
      description: "Process videos and images in real-time with minimal latency, perfect for live streaming.",
    },
    {
      icon: <Shield className="h-6 w-6 text-green-400" />,
      title: "AI Precision",
      description: "Our advanced AI ensures accurate detection and blurring of sensitive content with 99.8% accuracy.",
    },
    {
      icon: <Code className="h-6 w-6 text-green-400" />,
      title: "Developer API",
      description: "Integrate our privacy protection directly into your applications with our simple REST API.",
    },
    {
      icon: <Layers className="h-6 w-6 text-green-400" />,
      title: "Batch Processing",
      description: "Upload and process thousands of files at once with our efficient batch processing system.",
    },
    {
      icon: <Lock className="h-6 w-6 text-green-400" />,
      title: "Privacy Guaranteed",
      description: "Your content is processed securely and never stored or used for training our AI models.",
    },
  ]

  return (
    <section id="features" className="py-24">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mx-auto max-w-3xl text-center">
          <h2 className="text-3xl font-bold tracking-tight text-white sm:text-4xl">
            Advanced Privacy Protection Features
          </h2>
          <p className="mt-4 text-lg text-gray-300">
            Our AI-powered platform offers comprehensive tools to protect sensitive information in your visual content.
          </p>
        </div>

        <div className="mt-16 grid gap-8 md:grid-cols-2 lg:grid-cols-3">
          {features.map((feature, index) => (
            <Card key={index} className="border-navy-light bg-navy-dark text-white">
              <CardHeader>
                <div className="mb-2 inline-flex h-12 w-12 items-center justify-center rounded-lg bg-navy">
                  {feature.icon}
                </div>
                <CardTitle className="text-xl">{feature.title}</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-gray-300">{feature.description}</CardDescription>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  )
}

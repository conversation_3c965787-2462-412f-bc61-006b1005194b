'use client'

import { Check, HelpCircle } from 'lucide-react'
import Link from 'next/link'
import { motion } from 'framer-motion'
import { useState, useEffect } from 'react'

interface PricingFeature {
  text: string
  included: boolean
}

interface SafePricingCardProps {
  name: string
  description: string
  price: {
    monthly: number
    yearly: number
  }
  credits: number
  features: PricingFeature[]
  isPopular?: boolean
  buttonText: string
  buttonLink: string
  buttonVariant: 'blue' | 'green' | 'purple' | 'teal'
  isYearly: boolean
  clerkPlanId?: string
  stripePriceId?: string
}

export function SafePricingCard({
  name,
  description,
  price,
  credits,
  features,
  isPopular = false,
  buttonText,
  buttonLink,
  buttonVariant,
  isYearly,
  clerkPlanId,
  stripePriceId
}: SafePricingCardProps) {
  const [showTooltip, setShowTooltip] = useState(false)
  const [loading, setLoading] = useState(false)
  const [isAuthenticated, setIsAuthenticated] = useState<boolean | null>(null)

  // Check authentication status safely
  useEffect(() => {
    const checkAuth = async () => {
      try {
        const response = await fetch('/api/auth/check', {
          method: 'GET',
          credentials: 'include'
        })
        setIsAuthenticated(response.ok)
      } catch (error) {
        setIsAuthenticated(false)
      }
    }

    checkAuth()
  }, [])

  const handleSubscribe = async () => {
    if (!stripePriceId) {
      // Free plan - redirect to sign up
      window.location.href = '/sign-up'
      return
    }

    // Check if user is authenticated
    if (isAuthenticated === false) {
      // Not authenticated - redirect to sign up with return URL to billing page
      const signUpUrl = new URL('/sign-up', window.location.origin)
      signUpUrl.searchParams.set('redirect_url', '/dashboard/billing')
      window.location.href = signUpUrl.toString()
      return
    }

    if (isAuthenticated === true) {
      // Authenticated - redirect to dashboard billing page with Clerk's PricingTable
      window.location.href = '/dashboard/billing'
      return
    }

    // Still checking authentication - show loading
    if (isAuthenticated === null) {
      return
    }
  }

  // Calculate video minutes based on credits (approximately 1 minute = 60 credits)
  const videoMinutes = Math.round(credits / 60)

  const getCreditsTooltip = () => {
    if (credits === 5) {
      return "Process up to 5 images or a few seconds of video"
    } else {
      return `Process up to ${credits.toLocaleString()} images or ${videoMinutes} minutes of video`
    }
  }

  const buttonClasses = {
    blue: "blue-button-glow block w-full h-12 rounded-lg border border-blue-400 px-4 py-3 text-center font-medium text-blue-400 hover:text-white hover:border-transparent hover:bg-blue-500 transition-all duration-300 text-lg flex items-center justify-center",
    green: "green-button-glow block w-full h-12 rounded-lg bg-green-400 px-4 py-3 text-center font-medium text-navy hover:bg-green-500 transition-all duration-300 shadow-lg text-lg flex items-center justify-center",
    purple: "purple-button-glow block w-full h-12 rounded-lg border border-purple-400 px-4 py-3 text-center font-medium text-purple-400 hover:text-white hover:border-transparent hover:bg-purple-500 transition-all duration-300 text-lg flex items-center justify-center",
    teal: "teal-button-glow block w-full h-12 rounded-lg border border-teal-400 px-4 py-3 text-center font-medium text-teal-400 hover:text-white hover:border-transparent hover:bg-teal-500 transition-all duration-300 text-lg flex items-center justify-center"
  }

  const checkColor = {
    blue: "bg-blue-400",
    green: "bg-green-400",
    purple: "bg-purple-400",
    teal: "bg-teal-400"
  }

  const hoverEffects = {
    blue: "hover:border-blue-400/30 hover:shadow-blue-400/10",
    green: "hover:shadow-green-400/20",
    purple: "hover:border-purple-400/30 hover:shadow-purple-400/10",
    teal: "hover:border-teal-400/30 hover:shadow-teal-400/10"
  }

  return (
    <div
      className={`scroll-animate delay-${buttonVariant === 'blue' ? '300' : buttonVariant === 'teal' ? '400' : buttonVariant === 'green' ? '500' : '600'} rounded-xl h-full flex flex-col ${
        isPopular ? 'border-2 border-green-400' : 'border border-navy-light'
      } bg-navy p-6 shadow-xl transition-all duration-300 ${hoverEffects[buttonVariant]}`}
    >
      {isPopular && (
        <div className="absolute -top-4 left-0 right-0 mx-auto w-44 rounded-md bg-green-400 py-1.5 text-center text-sm font-bold text-navy shadow-lg">
          Most Popular Plan
        </div>
      )}

      <h3 className="text-xl font-bold text-white">{name}</h3>
      <p className="mt-1 text-sm text-gray-400">{description}</p>

      <div className="mt-4">
        <div className="flex items-baseline">
          <span className="text-sm text-gray-400">€</span>
          <span className="text-4xl font-extrabold text-white">
            {isYearly ? Math.round(price.yearly / 12) : price.monthly}
          </span>
          <span className="ml-1 text-sm text-gray-400">
            / Month
          </span>
        </div>

        {isYearly && (
          <div className="mt-1 text-xs text-gray-400">
            €{price.yearly} per year
          </div>
        )}
      </div>

      <div className="mt-6">
        <button
          onClick={handleSubscribe}
          disabled={loading || isAuthenticated === null}
          className={`${buttonClasses[buttonVariant]} ${loading || isAuthenticated === null ? 'opacity-50 cursor-not-allowed' : ''}`}
        >
          {loading ? (
            <span className="flex items-center justify-center">
              <svg className="animate-spin -ml-1 mr-3 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Processing...
            </span>
          ) : isAuthenticated === null ? (
            'Loading...'
          ) : isAuthenticated && stripePriceId ? (
            `Subscribe to ${name}`
          ) : (
            buttonText
          )}
        </button>
      </div>

      <ul className="mt-6 space-y-3 flex-grow">
        {features.map((feature, index) => (
          <li key={index} className="flex items-start">
            <div className={`flex-shrink-0 rounded-full ${feature.included ? checkColor[buttonVariant] : 'bg-gray-600'} p-1 mt-0.5`}>
              <Check className="h-3 w-3 text-navy" />
            </div>
            <span className={`ml-3 text-sm ${feature.included ? 'text-gray-300' : 'text-gray-500 line-through'}`}>
              {feature.text}
              {index === 0 && feature.text.includes('credits') && (
                <span
                  className="relative inline-block ml-1"
                  onMouseEnter={() => setShowTooltip(true)}
                  onMouseLeave={() => setShowTooltip(false)}
                >
                  <HelpCircle className="h-3.5 w-3.5 inline-block text-gray-400" />
                  {showTooltip && (
                    <div className="absolute z-10 w-60 px-3 py-2 text-xs bg-navy-dark border border-navy-light rounded-md shadow-lg -left-28 -top-20">
                      {getCreditsTooltip()}
                    </div>
                  )}
                </span>
              )}
            </span>
          </li>
        ))}
      </ul>
    </div>
  )
}

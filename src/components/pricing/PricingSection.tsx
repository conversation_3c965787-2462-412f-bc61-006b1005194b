"use client"

import { useState } from 'react'
import { PricingToggle } from './PricingToggle'
import { SafePricingCard } from './SafePricingCard'
import { TrustedBySection } from './TrustedBySection'
import { FeaturesComparison } from './FeaturesComparison'
import { PricingFAQ } from './PricingFAQ'
import { motion } from 'framer-motion'

export function PricingSection() {
  const [isYearly, setIsYearly] = useState(true)

  const handleToggle = (yearly: boolean) => {
    setIsYearly(yearly)
  }

  return (
    <section id="pricing" className="py-24 bg-navy-dark">
      <div className="max-w-7xl mx-auto px-6 sm:px-8 lg:px-12">
        <div className="mx-auto max-w-3xl text-center">
          <h2 className="scroll-animate text-4xl font-bold tracking-tight text-white sm:text-5xl">
            Pricing plans
          </h2>

          <p className="scroll-animate delay-100 mt-6 text-xl text-gray-300">
            Choose the right plan, reduce work time by ~95%.
          </p>
        </div>

        <PricingToggle onToggle={handleToggle} />

        <div className="mt-12 grid gap-6 md:grid-cols-4">
          <SafePricingCard
            name="Free"
            description="Free trial user"
            price={{ monthly: 0, yearly: 0 }}
            credits={5}
            features={[
              { text: "One-time 5 credits offer", included: true },
              { text: "Basic AI anonymization features", included: true },
              { text: "Basic editor tools", included: true },
              { text: "Blur preview", included: true },
              { text: "Create your workspace", included: true }
            ]}
            buttonText="Start for free"
            buttonLink="/sign-up"
            buttonVariant="blue"
            isYearly={isYearly}
          />

          <SafePricingCard
            name="Standard"
            description="For bloggers or casual users"
            price={{ monthly: 1, yearly: 12 }}
            credits={700}
            features={[
              { text: "700 credits per month", included: true },
              { text: "Full anonymization for people and vehicles", included: true },
              { text: "Save work files", included: true },
              { text: "Batch upload, anonymization, and download", included: true },
              { text: "1080p (Full HD) support", included: true }
            ]}
            buttonText="Go Standard plan"
            buttonLink="/sign-up"
            buttonVariant="teal"
            isYearly={isYearly}
            stripePriceId="price_1RSIGpR6OeqomohOPQNu7awg"
          />

          <SafePricingCard
            name="Pro"
            description="For Business/Professional users"
            price={{ monthly: 26, yearly: 312 }}
            credits={3500}
            features={[
              { text: "3,500 credits per month", included: true },
              { text: "Full anonymization for people and vehicles", included: true },
              { text: "Save work files", included: true },
              { text: "Batch upload, anonymization, and download", included: true },
              { text: "4K support", included: true },
              { text: "Unlimited blur/pixel subjects", included: true }
            ]}
            isPopular={true}
            buttonText="Go Pro plan"
            buttonLink="/sign-up"
            buttonVariant="green"
            isYearly={isYearly}
            stripePriceId="price_1RSIHaR6OeqomohOzOEwFOgZ"
          />

          <SafePricingCard
            name="Premium"
            description="For video production businesses"
            price={{ monthly: 78, yearly: 936 }}
            credits={14500}
            features={[
              { text: "14,500 credits per month", included: true },
              { text: "Full anonymization for people and vehicles", included: true },
              { text: "Save work files", included: true },
              { text: "Batch upload, anonymization, and download", included: true },
              { text: "4K support", included: true },
              { text: "Unlimited blur/pixel subjects", included: true },
              { text: "Premium concierge support", included: true }
            ]}
            buttonText="Go Premium plan"
            buttonLink="/sign-up"
            buttonVariant="purple"
            isYearly={isYearly}
            stripePriceId="price_1RSIICR6OeqomohOLsmbhNj8"
          />
        </div>

        <div className="mt-24">
          <TrustedBySection />
        </div>

        <div className="mt-24">
          <FeaturesComparison />
        </div>

        <div className="mt-24">
          <PricingFAQ />
        </div>
      </div>
    </section>
  )
}

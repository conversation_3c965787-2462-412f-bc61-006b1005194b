"use client"

import { useState } from 'react'
import { PricingToggle } from './PricingToggle'
import { PricingCard } from './PricingCard'
import { TrustedBySection } from './TrustedBySection'
import { FeaturesComparison } from './FeaturesComparison'
import { motion } from 'framer-motion'

export function DashboardPricingSection() {
  const [isYearly, setIsYearly] = useState(true)

  const handleToggle = (yearly: boolean) => {
    setIsYearly(yearly)
  }

  return (
    <div className="py-8 bg-navy">
      <div className="max-w-7xl mx-auto px-6 sm:px-8 lg:px-12">
        <div className="mx-auto max-w-3xl text-center">
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-3xl font-bold tracking-tight text-white sm:text-4xl"
          >
            Choose Your Plan
          </motion.h2>

          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="mt-4 text-lg text-gray-400"
          >
            Select the plan that best fits your needs
          </motion.p>
        </div>

        <PricingToggle onToggle={handleToggle} />

        <div className="mt-8 grid gap-6 md:grid-cols-4">
          <PricingCard
            name="Free"
            description="Free trial user"
            price={{ monthly: 0, yearly: 0 }}
            credits={5}
            features={[
              { text: "One-time 5 credits offer", included: true },
              { text: "Basic AI anonymization features", included: true },
              { text: "Basic editor tools", included: true },
              { text: "Blur preview", included: true },
              { text: "Create your workspace", included: true }
            ]}
            buttonText="Current Plan"
            buttonLink="#"
            buttonVariant="blue"
            isYearly={isYearly}
          />

          <PricingCard
            name="Standard"
            description="For bloggers or casual users"
            price={{ monthly: 7, yearly: 60 }}
            credits={700}
            features={[
              { text: "700 credits per month", included: true },
              { text: "Full anonymization for people and vehicles", included: true },
              { text: "Save work files", included: true },
              { text: "Batch upload, anonymization, and download", included: true },
              { text: "1080p (Full HD) support", included: true }
            ]}
            buttonText="Upgrade"
            buttonLink="#"
            buttonVariant="teal"
            isYearly={isYearly}
            clerkPlanId="standard"
          />

          <PricingCard
            name="Pro"
            description="For Business/Professional users"
            price={{ monthly: 26, yearly: 192 }}
            credits={999999}
            features={[
              { text: "Unlimited credits", included: true },
              { text: "Fast processing (2x speed)", included: true },
              { text: "Priority email support", included: true },
              { text: "API access", included: true },
              { text: "4K quality exports", included: true },
              { text: "Batch processing", included: true }
            ]}
            isPopular={true}
            buttonText="Upgrade"
            buttonLink="#"
            buttonVariant="green"
            isYearly={isYearly}
            clerkPlanId="pro"
          />

          <PricingCard
            name="Premium"
            description="For video production businesses"
            price={{ monthly: 78, yearly: 561.60 }}
            credits={999999}
            features={[
              { text: "Unlimited credits", included: true },
              { text: "Fastest processing (3x speed)", included: true },
              { text: "24/7 priority support", included: true },
              { text: "API access", included: true },
              { text: "8K quality exports", included: true },
              { text: "Custom integrations", included: true },
              { text: "Dedicated account manager", included: true }
            ]}
            buttonText="Upgrade"
            buttonLink="#"
            buttonVariant="purple"
            isYearly={isYearly}
            clerkPlanId="premium"
          />
        </div>

        <div className="mt-16">
          <TrustedBySection />
        </div>

        <div className="mt-16">
          <FeaturesComparison />
        </div>
      </div>
    </div>
  )
}

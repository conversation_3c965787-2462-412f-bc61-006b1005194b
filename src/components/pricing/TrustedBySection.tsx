"use client"

import { motion } from 'framer-motion'
// Commented out unused import
// import Image from 'next/image'

export function TrustedBySection() {
  return (
    <div className="py-16 bg-navy-dark/50 rounded-2xl">
      <div className="max-w-5xl mx-auto px-6">
        <div className="scroll-animate text-center mb-16">
          <h2 className="text-3xl font-bold text-white mb-12">
            "Trusted by users around the world."
          </h2>

          <div className="scroll-animate delay-100 grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="bg-navy-light/30 rounded-xl p-8 flex flex-col items-center">
              <div className="text-5xl font-bold text-blue-400 mb-2">88+</div>
              <div className="text-gray-300">Daily active countries</div>
            </div>

            <div className="bg-navy-light/30 rounded-xl p-8 flex flex-col items-center">
              <div className="text-5xl font-bold text-green-400 mb-2">450,000+</div>
              <div className="text-gray-300">Overall files handled</div>
            </div>
          </div>
        </div>

        <div className="scroll-animate delay-200 text-center">
          <h3 className="text-xl font-medium text-white mb-8">Trusted by</h3>

          <div className="flex flex-wrap justify-center items-center gap-8">
            <div className="w-32 h-12 bg-navy-light/30 rounded-lg flex items-center justify-center">
              <div className="text-gray-400 text-sm">Company Logo</div>
            </div>
            <div className="w-32 h-12 bg-navy-light/30 rounded-lg flex items-center justify-center">
              <div className="text-gray-400 text-sm">Company Logo</div>
            </div>
            <div className="w-32 h-12 bg-navy-light/30 rounded-lg flex items-center justify-center">
              <div className="text-gray-400 text-sm">Company Logo</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

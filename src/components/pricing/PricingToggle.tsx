"use client"

import { useState } from 'react'
// Commented out unused import
// import { motion } from 'framer-motion'

interface PricingToggleProps {
  onToggle: (isYearly: boolean) => void
}

export function PricingToggle({ onToggle }: PricingToggleProps) {
  const [isYearly, setIsYearly] = useState(true)

  const handleToggle = () => {
    const newValue = !isYearly
    setIsYearly(newValue)
    onToggle(newValue)
  }

  return (
    <div className="scroll-animate delay-200 flex flex-col items-center justify-center mb-12">
      <div className="flex items-center space-x-4 bg-navy-light/30 p-1.5 rounded-full">
        <button
          onClick={() => {
            if (!isYearly) handleToggle()
          }}
          className={`flex flex-col items-center justify-center h-11 px-8 rounded-full text-base font-medium transition-all duration-200 ${
            isYearly ? 'bg-blue-500 text-white shadow-lg' : 'text-gray-300'
          }`}
        >
          <div className="flex flex-col h-full justify-center">
            <span>1 Year</span>
            <span className={`text-xs font-normal transition-opacity duration-200 ${isYearly ? 'opacity-100' : 'opacity-0 absolute'}`}>
              save up to 40%
            </span>
          </div>
        </button>
        <button
          onClick={() => {
            if (isYearly) handleToggle()
          }}
          className={`flex flex-col items-center justify-center h-11 px-8 rounded-full text-base font-medium transition-all duration-200 ${
            !isYearly ? 'bg-blue-500 text-white shadow-lg' : 'text-gray-300'
          }`}
        >
          <span>Monthly</span>
        </button>
      </div>
    </div>
  )
}

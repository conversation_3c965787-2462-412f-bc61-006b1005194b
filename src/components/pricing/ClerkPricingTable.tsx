'use client';

import { useState } from 'react';
import { useUser } from '@clerk/nextjs';
import { Check, Zap, Crown, Sparkles } from 'lucide-react';

interface PricingPlan {
  id: string;
  name: string;
  description: string;
  monthlyPrice: number;
  yearlyPrice: number;
  credits: string;
  features: string[];
  icon: React.ReactNode;
  popular?: boolean;
  clerkPlanId: {
    monthly: string;
    yearly: string;
  };
}

const plans: PricingPlan[] = [
  {
    id: 'free',
    name: 'Free',
    description: 'Perfect for trying out GuardiaVision',
    monthlyPrice: 0,
    yearlyPrice: 0,
    credits: '50 credits',
    features: [
      '50 credits per month',
      'Standard processing speed',
      'Community support',
      'Basic blur effects'
    ],
    icon: <Zap className="h-6 w-6" />,
    clerkPlanId: {
      monthly: '',
      yearly: ''
    }
  },
  {
    id: 'standard',
    name: 'Standard',
    description: '700 credits • Basic processing • Email support',
    monthlyPrice: 7,
    yearlyPrice: 60,
    credits: '700 credits',
    features: [
      '700 credits per month',
      'Standard processing speed',
      'Email support',
      'All blur effects',
      'HD quality exports'
    ],
    icon: <Check className="h-6 w-6" />,
    clerkPlanId: {
      monthly: 'standard',
      yearly: 'standard'
    }
  },
  {
    id: 'pro',
    name: 'Pro',
    description: 'Unlimited credits • Fast processing • Priority support • API access',
    monthlyPrice: 26,
    yearlyPrice: 192,
    credits: 'Unlimited credits',
    features: [
      'Unlimited credits',
      'Fast processing (2x speed)',
      'Priority email support',
      'API access',
      '4K quality exports',
      'Batch processing'
    ],
    icon: <Crown className="h-6 w-6" />,
    popular: true,
    clerkPlanId: {
      monthly: 'pro',
      yearly: 'pro'
    }
  },
  {
    id: 'premium',
    name: 'Premium',
    description: 'Unlimited credits • Fastest processing • 24/7 priority support • API access • Custom integrations',
    monthlyPrice: 78,
    yearlyPrice: 561.60,
    credits: 'Unlimited credits',
    features: [
      'Unlimited credits',
      'Fastest processing (3x speed)',
      '24/7 priority support',
      'API access',
      '8K quality exports',
      'Custom integrations',
      'Dedicated account manager'
    ],
    icon: <Sparkles className="h-6 w-6" />,
    clerkPlanId: {
      monthly: 'premium',
      yearly: 'premium'
    }
  }
];

export function ClerkPricingTable() {
  const [isYearly, setIsYearly] = useState(false);
  const { user } = useUser();

  const handleSubscribe = async (plan: PricingPlan) => {
    if (!user) {
      // Redirect to sign up
      window.location.href = '/sign-up';
      return;
    }

    if (plan.id === 'free') {
      // Free plan doesn't require payment
      return;
    }

    try {
      // Use Clerk's subscription method
      const planId = isYearly ? plan.clerkPlanId.yearly : plan.clerkPlanId.monthly;
      
      // Redirect to Clerk's subscription flow
      window.location.href = `/api/clerk/subscribe?plan=${planId}`;
    } catch (error) {
      console.error('Error subscribing:', error);
    }
  };

  const calculateSavings = (monthly: number, yearly: number) => {
    const monthlyCost = monthly * 12;
    const savings = monthlyCost - yearly;
    const percentage = Math.round((savings / monthlyCost) * 100);
    return { savings, percentage };
  };

  return (
    <div className="py-12">
      {/* Billing Toggle */}
      <div className="flex justify-center mb-8">
        <div className="flex items-center bg-gray-800 rounded-lg p-1">
          <button
            onClick={() => setIsYearly(false)}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-all ${
              !isYearly
                ? 'bg-purple-600 text-white'
                : 'text-gray-400 hover:text-white'
            }`}
          >
            Monthly
          </button>
          <button
            onClick={() => setIsYearly(true)}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-all ${
              isYearly
                ? 'bg-purple-600 text-white'
                : 'text-gray-400 hover:text-white'
            }`}
          >
            Yearly
            <span className="ml-2 text-xs bg-green-500 text-white px-2 py-1 rounded-full">
              Save up to 40%
            </span>
          </button>
        </div>
      </div>

      {/* Pricing Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 max-w-7xl mx-auto px-4">
        {plans.map((plan) => {
          const price = isYearly ? plan.yearlyPrice : plan.monthlyPrice;
          const savings = calculateSavings(plan.monthlyPrice, plan.yearlyPrice);
          
          return (
            <div
              key={plan.id}
              className={`relative rounded-2xl p-6 transition-all duration-300 ${
                plan.popular
                  ? 'bg-gradient-to-b from-purple-900/50 to-indigo-900/50 border-2 border-purple-500 scale-105'
                  : 'bg-gray-800/50 border border-gray-700 hover:border-gray-600'
              }`}
            >
              {plan.popular && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <span className="bg-gradient-to-r from-purple-500 to-indigo-500 text-white px-4 py-1 rounded-full text-sm font-medium">
                    Most Popular
                  </span>
                </div>
              )}

              <div className="text-center">
                <div className={`inline-flex p-3 rounded-full mb-4 ${
                  plan.popular ? 'bg-purple-500/20' : 'bg-gray-700/50'
                }`}>
                  {plan.icon}
                </div>
                
                <h3 className="text-xl font-bold text-white mb-2">{plan.name}</h3>
                
                <div className="mb-4">
                  <span className="text-3xl font-bold text-white">
                    ${price}
                  </span>
                  <span className="text-gray-400 ml-1">
                    /{isYearly ? 'year' : 'month'}
                  </span>
                  
                  {isYearly && plan.monthlyPrice > 0 && (
                    <div className="text-sm text-green-400 mt-1">
                      Save ${savings.savings.toFixed(0)}/year ({savings.percentage}% off)
                    </div>
                  )}
                </div>

                <p className="text-purple-300 font-medium mb-6">{plan.credits}</p>

                <button
                  onClick={() => handleSubscribe(plan)}
                  className={`w-full py-3 px-4 rounded-lg font-medium transition-all duration-300 ${
                    plan.popular
                      ? 'bg-gradient-to-r from-purple-500 to-indigo-500 text-white hover:from-purple-600 hover:to-indigo-600'
                      : plan.id === 'free'
                      ? 'bg-gray-700 text-white hover:bg-gray-600'
                      : 'bg-purple-600 text-white hover:bg-purple-700'
                  }`}
                >
                  {plan.id === 'free' ? 'Get Started' : 'Subscribe Now'}
                </button>
              </div>

              <div className="mt-6 space-y-3">
                {plan.features.map((feature, index) => (
                  <div key={index} className="flex items-center text-sm">
                    <Check className="h-4 w-4 text-green-400 mr-3 flex-shrink-0" />
                    <span className="text-gray-300">{feature}</span>
                  </div>
                ))}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}

"use client"

import React from 'react'
import { motion } from 'framer-motion'
import { Check, Minus } from 'lucide-react'
import Link from 'next/link'
import { ShieldCheck } from '@/components/icons/ShieldCheck'

// Feature category with its features
interface FeatureCategory {
  name: string
  features: Feature[]
}

// Individual feature with its availability across plans
interface Feature {
  name: string
  free: boolean | string
  standard: boolean | string
  pro: boolean | string
  premium: boolean | string
}

// Define all feature categories and their features
const featureCategories: FeatureCategory[] = [
  {
    name: "AI anonymization features",
    features: [
      { name: "Blur", free: true, standard: true, pro: true, premium: true },
      { name: "Pixelation", free: true, standard: true, pro: true, premium: true },
      { name: "Masking level", free: true, standard: true, pro: true, premium: true },
      { name: "Masking size", free: true, standard: true, pro: true, premium: true },
      { name: "Face", free: true, standard: true, pro: true, premium: true },
      { name: "Body", free: true, standard: true, pro: true, premium: true },
      { name: "License plate", free: true, standard: true, pro: true, premium: true },
      { name: "Car", free: true, standard: true, pro: true, premium: true },
      { name: "Smooth feathering", free: false, standard: true, pro: true, premium: true },
      { name: "Dual engine", free: false, standard: true, pro: true, premium: true }
    ]
  },
  {
    name: "High-speed processing for large files",
    features: [
      { name: "Large file priority", free: "Low", standard: "Medium", pro: "High", premium: "Highest" },
      { name: "Fast track", free: false, standard: false, pro: true, premium: true }
    ]
  },
  {
    name: "Media support",
    features: [
      { name: "Images (JPG, PNG)", free: true, standard: true, pro: true, premium: true },
      { name: "Videos (AVI, MP4, MOV)", free: true, standard: true, pro: true, premium: true },
      { name: "H.264, H.265 support", free: true, standard: true, pro: true, premium: true },
      { name: "HEIC support", free: false, standard: true, pro: true, premium: true },
      { name: "HDR support", free: false, standard: false, pro: true, premium: true },
      { name: "RAW support", free: false, standard: false, pro: true, premium: true },
      { name: "720p", free: true, standard: true, pro: true, premium: true },
      { name: "1080p", free: false, standard: true, pro: true, premium: true },
      { name: "4K", free: false, standard: false, pro: true, premium: true }
    ]
  },
  {
    name: "Workspace",
    features: [
      { name: "File storage", free: true, standard: true, pro: true, premium: true },
      { name: "Credit usage history", free: false, standard: true, pro: true, premium: true },
      { name: "File filtering", free: false, standard: true, pro: true, premium: true },
      { name: "Batch upload", free: false, standard: true, pro: true, premium: true },
      { name: "Batch anonymization", free: false, standard: true, pro: true, premium: true },
      { name: "Batch export", free: false, standard: true, pro: true, premium: true },
      { name: "Batch download", free: false, standard: true, pro: true, premium: true },
      { name: "Batch processing limit", free: "Limited", standard: "Limited", pro: "Unlimited", premium: "Unlimited" }
    ]
  },
  {
    name: "Editor",
    features: [
      { name: "Preview", free: true, standard: true, pro: true, premium: true },
      { name: "Video speed adjustment", free: false, standard: true, pro: true, premium: true },
      { name: "Real-time editing", free: false, standard: true, pro: true, premium: true },
      { name: "Thumbnail navigation", free: false, standard: true, pro: true, premium: true },
      { name: "Save work files", free: false, standard: true, pro: true, premium: true },
      { name: "Autosave", free: false, standard: true, pro: true, premium: true },
      { name: "Exclusion targets", free: "Limited", standard: "Limited", pro: "Unlimited", premium: "Unlimited" },
      { name: "Tracking specific targets", free: "Limited", standard: "Limited", pro: "Unlimited", premium: "Unlimited" },
      { name: "Custom blur", free: "Limited", standard: "Limited", pro: "Unlimited", premium: "Unlimited" }
    ]
  },
  {
    name: "Customer Support",
    features: [
      { name: "Email/Messenger Support", free: true, standard: true, pro: true, premium: true },
      { name: "Premium Concierge Support", free: false, standard: false, pro: false, premium: true }
    ]
  }
]

export function FeaturesComparison() {
  return (
    <div className="py-16">
      <h2 className="scroll-animate text-3xl font-bold text-center text-white mb-16">
        Compare all features
      </h2>

      <div className="scroll-animate delay-300 overflow-x-auto">
        <table className="w-full min-w-[800px]">
          <thead>
            <tr className="border-b border-navy-light">
              <th className="py-4 px-4 text-left text-gray-300 font-medium">Features</th>
              <th className="py-4 px-4 text-center">
                <div className="flex flex-col items-center">
                  <span className="text-lg font-bold text-white">Free</span>
                  <div className="flex items-baseline mt-1">
                    <span className="text-sm text-gray-400">$</span>
                    <span className="text-xl font-bold text-white">0</span>
                    <span className="text-xs text-gray-400 ml-1">/ Month</span>
                  </div>
                  <div className="text-xs text-gray-400">$0 per year</div>
                  <div className="mt-2">
                    <Link href="/sign-up" className="inline-flex items-center justify-center px-3 py-1 text-xs font-medium text-blue-400 border border-blue-400 rounded-md hover:bg-blue-400/10 transition-colors">
                      <ShieldCheck className="h-3 w-3 mr-1" />
                      Start for free
                    </Link>
                  </div>
                </div>
              </th>
              <th className="py-4 px-4 text-center">
                <div className="flex flex-col items-center">
                  <span className="text-lg font-bold text-white">Standard</span>
                  <div className="flex items-baseline mt-1">
                    <span className="text-sm text-gray-400">$</span>
                    <span className="text-xl font-bold text-white">7</span>
                    <span className="text-xs text-gray-400 ml-1">/ Month</span>
                  </div>
                  <div className="text-xs text-gray-400">$84 per year</div>
                  <div className="mt-2">
                    <Link href="/sign-up" className="inline-flex items-center justify-center px-3 py-1 text-xs font-medium text-teal-400 border border-teal-400 rounded-md hover:bg-teal-400/10 transition-colors">
                      Go Standard plan
                    </Link>
                  </div>
                </div>
              </th>
              <th className="py-4 px-4 text-center">
                <div className="flex flex-col items-center">
                  <span className="text-lg font-bold text-white">Pro</span>
                  <div className="flex items-baseline mt-1">
                    <span className="text-sm text-gray-400">$</span>
                    <span className="text-xl font-bold text-white">26</span>
                    <span className="text-xs text-gray-400 ml-1">/ Month</span>
                  </div>
                  <div className="text-xs text-gray-400">$312 per year</div>
                  <div className="mt-2">
                    <Link href="/sign-up" className="inline-flex items-center justify-center px-3 py-1 text-xs font-medium text-green-400 border border-green-400 rounded-md hover:bg-green-400/10 transition-colors">
                      Go Pro plan
                    </Link>
                  </div>
                </div>
              </th>
              <th className="py-4 px-4 text-center">
                <div className="flex flex-col items-center">
                  <span className="text-lg font-bold text-white">Premium</span>
                  <div className="flex items-baseline mt-1">
                    <span className="text-sm text-gray-400">$</span>
                    <span className="text-xl font-bold text-white">78</span>
                    <span className="text-xs text-gray-400 ml-1">/ Month</span>
                  </div>
                  <div className="text-xs text-gray-400">$936 per year</div>
                  <div className="mt-2">
                    <Link href="/sign-up" className="inline-flex items-center justify-center px-3 py-1 text-xs font-medium text-purple-400 border border-purple-400 rounded-md hover:bg-purple-400/10 transition-colors">
                      Go Premium plan
                    </Link>
                  </div>
                </div>
              </th>
            </tr>
          </thead>

          <tbody>
            {featureCategories.map((category, categoryIndex) => (
              <React.Fragment key={categoryIndex}>
                <tr className="bg-navy-dark/50">
                  <td colSpan={5} className="py-4 px-4 text-white font-semibold">{category.name}</td>
                </tr>

                {category.features.map((feature, featureIndex) => (
                  <tr key={featureIndex} className="border-b border-navy-light/30 transition-colors duration-200 hover:bg-navy-light/10">
                    <td className="py-3 px-4 text-gray-300">{feature.name}</td>
                    <td className="py-3 px-4 text-center">
                      {renderFeatureValue(feature.free, "blue")}
                    </td>
                    <td className="py-3 px-4 text-center">
                      {renderFeatureValue(feature.standard, "teal")}
                    </td>
                    <td className="py-3 px-4 text-center">
                      {renderFeatureValue(feature.pro, "green")}
                    </td>
                    <td className="py-3 px-4 text-center">
                      {renderFeatureValue(feature.premium, "purple")}
                    </td>
                  </tr>
                ))}
              </React.Fragment>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  )
}

// Helper function to render different types of feature values
function renderFeatureValue(value: boolean | string, color: "blue" | "teal" | "green" | "purple") {
  const colorClasses = {
    blue: "text-blue-400",
    teal: "text-teal-400",
    green: "text-green-400",
    purple: "text-purple-400"
  }

  if (typeof value === "boolean") {
    if (value) {
      return <Check className={`h-5 w-5 mx-auto ${colorClasses[color]}`} />
    } else {
      return <Minus className="h-5 w-5 mx-auto text-gray-500" />
    }
  } else {
    return <span className={colorClasses[color]}>{value}</span>
  }
}

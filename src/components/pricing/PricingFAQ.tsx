"use client"

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { ChevronDown } from 'lucide-react'

const faqs = [
  {
    question: "What features are available to users on the Free Plan?",
    answer: "The Free Plan offers basic AI anonymization features, including blur, pixelation, and basic editor tools. You'll receive a one-time offer of 5 credits to try our service, and you can preview the blur effect before applying it. You can also create your own workspace to manage your projects."
  },
  {
    question: "How can I cancel my subscription?",
    answer: "You can cancel your subscription at any time by going to your account settings and selecting 'Manage Subscription'. Your subscription will remain active until the end of your current billing period."
  },
  {
    question: "Can I get a refund after payment?",
    answer: "We offer a 14-day money-back guarantee for all paid plans. If you're not satisfied with our service, you can request a refund within 14 days of your purchase. After this period, refunds are provided at our discretion."
  },
  {
    question: "What payment methods are available?",
    answer: "We accept all major credit cards, including Visa, Mastercard, American Express, and Discover. We also support payment via PayPal for your convenience."
  },
  {
    question: "Is my card information secure?",
    answer: "Yes, we use industry-standard encryption and security measures to protect your payment information. We partner with trusted payment processors and never store your full credit card details on our servers."
  },
  {
    question: "I need a receipt for my payment.",
    answer: "You can access and download receipts for all your payments from your account dashboard under 'Billing History'. If you need a specific format or have any issues, please contact our support team."
  },
  {
    question: "Can I change my payment method?",
    answer: "Yes, you can update your payment method at any time through your account settings. Go to 'Billing' and select 'Update Payment Method' to make changes."
  },
  {
    question: "Are my uploaded video or image files safe?",
    answer: "We take data security very seriously. All content is processed on secure servers and automatically deleted after processing unless you choose to save it in your workspace. We never share your data with third parties."
  },
  {
    question: "I need more than the 4 hours of video processing available per month with the Premium plan.",
    answer: "If you need additional processing capacity beyond what's included in our Premium plan, please contact our sales team. We offer custom enterprise solutions for businesses with high-volume needs."
  },
  {
    question: "What are credits, and how do I use them?",
    answer: "Credits are the currency used within our platform to process your content. Each credit allows you to process a certain amount of content, depending on the file size and type. Credits are automatically deducted when you successfully process content through our platform."
  },
  {
    question: "How long do credits last, and when do they expire?",
    answer: "Credits from monthly plans refresh at the beginning of each billing cycle. Any unused credits from the previous month expire. For annual plans, credits are allocated monthly but do not accumulate if unused."
  }
]

export function PricingFAQ() {
  const [openIndex, setOpenIndex] = useState<number | null>(null)

  const toggleFAQ = (index: number) => {
    setOpenIndex(openIndex === index ? null : index)
  }

  return (
    <div className="max-w-4xl mx-auto px-6 py-16">
      <h2 className="scroll-animate text-3xl font-bold text-center text-white mb-12">
        Frequently Asked Questions (FAQ)
      </h2>

      <div className="scroll-animate delay-200 space-y-4">
        {faqs.map((faq, index) => (
          <div
            key={index}
            className="border border-navy-light rounded-lg overflow-hidden bg-navy-dark/50 transition-all duration-300 hover:border-blue-400/30"
          >
            <button
              onClick={() => toggleFAQ(index)}
              className="flex justify-between items-center w-full p-6 text-left"
            >
              <h3 className="text-lg font-medium text-white">{faq.question}</h3>
              <ChevronDown
                className={`h-5 w-5 text-gray-400 transition-transform duration-300 ${
                  openIndex === index ? 'transform rotate-180' : ''
                }`}
              />
            </button>

            <AnimatePresence>
              {openIndex === index && (
                <motion.div
                  initial={{ height: 0, opacity: 0 }}
                  animate={{ height: 'auto', opacity: 1 }}
                  exit={{ height: 0, opacity: 0 }}
                  transition={{ duration: 0.3 }}
                >
                  <div className="px-6 pb-6 text-gray-300">
                    {faq.answer}
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        ))}
      </div>
    </div>
  )
}

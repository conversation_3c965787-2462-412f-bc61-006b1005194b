# Supabase-Clerk Integration Guide

This guide provides step-by-step instructions for integrating Supa<PERSON> with Clerk authentication in your Next.js application.

## Prerequisites

- A Clerk account and application
- A Supabase account and project
- Next.js application with Clerk authentication

## Step 1: Set up Clerk as a Supabase third-party auth provider

1. In the Clerk Dashboard, navigate to the Supabase integration setup.
2. Select your configuration options, and then select "Activate Supabase integration". This will reveal the Clerk domain for your Clerk instance.
3. Save the Clerk domain.
4. In the Supabase Dashboard, navigate to Authentication > Sign In / Up.
5. Select "Add provider" and select Clerk from the list of providers.
6. Paste the Clerk domain you copied from the Clerk Dashboard.

## Step 2: Set up environment variables

Add the following environment variables to your `.env.local` file:

```
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
```

You can find these values in your Supabase dashboard under Project Settings > API.

## Step 3: Install the Supabase client library

```bash
npm install @supabase/supabase-js
```

## Step 4: Create Supabase client utilities

Create the following files:

### src/utils/supabase/client.ts

```typescript
// Client-side Supabase client that uses Clerk's session token
import { createClient } from '@supabase/supabase-js';
import { useSession } from '@clerk/nextjs';
import { useCallback, useMemo } from 'react';

// This function creates a Supabase client that includes the Clerk session token
// in the request headers for authentication
export function useSupabaseClient() {
  const { session } = useSession();

  // Create a memoized function to get the Clerk session token
  const getClerkToken = useCallback(async () => {
    return session?.getToken() ?? null;
  }, [session]);

  // Create a memoized Supabase client that uses the Clerk token
  const supabaseClient = useMemo(() => {
    return createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        global: {
          headers: {
            // This function will be called on each request to get the latest token
            async headers() {
              const token = await getClerkToken();
              return {
                Authorization: token ? `Bearer ${token}` : '',
              };
            },
          },
        },
      }
    );
  }, [getClerkToken]);

  return supabaseClient;
}
```

### src/utils/supabase/server.ts

```typescript
// Server-side Supabase client that uses Clerk's session token
import { createClient } from '@supabase/supabase-js';
import { auth } from '@clerk/nextjs/server';

// Create a Supabase client for server components
export function createServerSupabaseClient() {
  const { getToken } = auth();

  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      global: {
        headers: {
          Authorization: `Bearer ${getToken() || ''}`,
        },
      },
    }
  );
}

// Create a Supabase client for server actions
export async function createServerActionSupabaseClient() {
  const { getToken } = auth();
  const token = await getToken();

  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      global: {
        headers: {
          Authorization: `Bearer ${token || ''}`,
        },
      },
    }
  );
}
```

## Step 5: Create a server action to create users in Supabase

Create the following file:

### src/app/actions.ts

```typescript
'use server';

import { createServerActionSupabaseClient } from '@/utils/supabase/server';

// Server action to create a user in Supabase
export async function createUserInSupabase(
  userId: string,
  email: string,
  username: string,
  firstName: string,
  lastName: string,
  imageUrl: string
) {
  try {
    console.log('Server action: Creating user in Supabase with ID:', userId);
    
    // Create Supabase client
    const supabase = await createServerActionSupabaseClient();
    
    // Check if user already exists in Supabase
    const { data: existingUser, error: checkError } = await supabase
      .from('users')
      .select('id')
      .eq('id', userId)
      .maybeSingle();
    
    if (checkError) {
      console.error('Error checking for existing user:', checkError);
      return { success: false, error: `Error checking for existing user: ${checkError.message}` };
    }
    
    if (existingUser) {
      console.log('User already exists in Supabase:', existingUser);
      return { success: true, message: 'User already exists in Supabase' };
    }
    
    // Insert user into Supabase
    const { error: insertError } = await supabase
      .from('users')
      .insert({
        id: userId,
        email,
        username,
        first_name: firstName,
        last_name: lastName,
        avatar_url: imageUrl,
      });
    
    if (insertError) {
      console.error('Error inserting user into Supabase:', insertError);
      return { success: false, error: `Error inserting user: ${insertError.message}` };
    }
    
    console.log('Successfully created user in Supabase with ID:', userId);
    return { success: true, message: 'User created successfully in Supabase' };
  } catch (error: any) {
    console.error('Unexpected error creating user in Supabase:', error);
    return { success: false, error: `Unexpected error: ${error.message}` };
  }
}
```

## Step 6: Create a component to create users in Supabase

Create the following file:

### src/components/auth/CreateUserInSupabase.tsx

```typescript
'use client';

import { useEffect, useState } from 'react';
import { useUser } from '@clerk/nextjs';
import { createUserInSupabase } from '@/app/actions';

export function CreateUserInSupabase() {
  const { user, isLoaded } = useUser();
  const [isCreatingUser, setIsCreatingUser] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  useEffect(() => {
    async function createUserInSupabaseEffect() {
      if (!user || isCreatingUser || success) return;

      setIsCreatingUser(true);
      setError(null);

      try {
        console.log('Creating user in Supabase via server action:', user.id);
        console.log('User details:', {
          id: user.id,
          email: user.primaryEmailAddress?.emailAddress,
          username: user.username,
          firstName: user.firstName,
          lastName: user.lastName
        });

        // Call the server action to create the user
        const result = await createUserInSupabase(
          user.id,
          user.primaryEmailAddress?.emailAddress || '',
          user.username || '',
          user.firstName || '',
          user.lastName || '',
          user.imageUrl || ''
        );

        if (!result.success) {
          console.error('Error from server action:', result.error);
          setError(result.error || 'Unknown error');
          setIsCreatingUser(false);
          return;
        }

        console.log('Server action result:', result.message);
        setSuccess(true);
      } catch (err: any) {
        console.error('Exception calling server action:', err);
        setError('An unexpected error occurred');
      } finally {
        setIsCreatingUser(false);
      }
    }

    if (isLoaded && user) {
      createUserInSupabaseEffect();
    }
  }, [user, isLoaded, isCreatingUser, success]);

  // Display error message if there's an error
  if (error) {
    return (
      <div style={{ 
        position: 'fixed', 
        bottom: '20px', 
        right: '20px', 
        background: '#f44336', 
        color: 'white',
        padding: '10px',
        borderRadius: '5px',
        zIndex: 9999
      }}>
        Error creating user in Supabase: {error}
      </div>
    );
  }
  
  // This component doesn't render anything visible when there's no error
  return null;
}
```

## Step 7: Add the component to your auth layout

Update your auth layout to include the `CreateUserInSupabase` component:

```tsx
import { CreateUserInSupabase } from '@/components/auth/CreateUserInSupabase';

// Inside your ClerkProvider
<ClerkProvider>
  <CreateUserInSupabase />
  {children}
</ClerkProvider>
```

## Step 8: Run the SQL setup script

Run the following SQL script in the Supabase SQL Editor:

```sql
-- Function to get the Clerk user ID from the JWT token
CREATE OR REPLACE FUNCTION public.get_clerk_user_id()
RETURNS TEXT
LANGUAGE sql
SECURITY DEFINER
SET search_path = public
AS $$
  SELECT coalesce(auth.jwt() ->> 'sub', '')
$$;

-- Function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION public.update_updated_at()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$;

-- Create users table
CREATE TABLE IF NOT EXISTS public.users (
  id TEXT PRIMARY KEY,
  email TEXT NOT NULL,
  username TEXT,
  first_name TEXT,
  last_name TEXT,
  avatar_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create test_items table for testing the integration
CREATE TABLE IF NOT EXISTS public.test_items (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  user_id TEXT NOT NULL REFERENCES users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable Row Level Security
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.test_items ENABLE ROW LEVEL SECURITY;

-- Create policy for users to view their own data
CREATE POLICY "Users can view their own data" 
  ON public.users
  FOR SELECT 
  TO authenticated
  USING (id = get_clerk_user_id());

-- Create policy for users to update their own data
CREATE POLICY "Users can update their own data" 
  ON public.users
  FOR UPDATE 
  TO authenticated
  USING (id = get_clerk_user_id());

-- Create policy for test_items - users can view their own items
CREATE POLICY "Users can view their own test items" 
  ON public.test_items
  FOR SELECT 
  TO authenticated
  USING (user_id = get_clerk_user_id());

-- Create policy for test_items - users can insert their own items
CREATE POLICY "Users can insert their own test items" 
  ON public.test_items
  FOR INSERT 
  TO authenticated
  WITH CHECK (user_id = get_clerk_user_id());

-- Create policy for test_items - users can update their own items
CREATE POLICY "Users can update their own test items" 
  ON public.test_items
  FOR UPDATE 
  TO authenticated
  USING (user_id = get_clerk_user_id());

-- Create policy for test_items - users can delete their own items
CREATE POLICY "Users can delete their own test items" 
  ON public.test_items
  FOR DELETE 
  TO authenticated
  USING (user_id = get_clerk_user_id());

-- Create triggers to automatically update the updated_at field
CREATE TRIGGER update_users_updated_at
BEFORE UPDATE ON public.users
FOR EACH ROW
EXECUTE FUNCTION update_updated_at();

CREATE TRIGGER update_test_items_updated_at
BEFORE UPDATE ON public.test_items
FOR EACH ROW
EXECUTE FUNCTION update_updated_at();
```

## Step 9: Test the integration

1. Sign up or sign in with Clerk
2. Check the Supabase Table Editor for the `users` table
3. Confirm that a user record with your Clerk user ID exists

## Troubleshooting

If you encounter issues:

1. Check that Clerk is properly set up as a Supabase provider
2. Verify your environment variables are correct
3. Ensure the SQL setup script has been run successfully
4. Check the browser console for any errors
5. Verify that your Clerk user has a valid session token

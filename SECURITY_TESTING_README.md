# Security Testing Guide

This guide explains how to test the security of your Clerk-Supabase integration.

## Current Status

We've temporarily disabled Row Level Security (RLS) to ensure the application works correctly. This is a short-term solution while we work on implementing the proper Clerk-Supabase integration.

## Running the SQL Script

To disable <PERSON><PERSON> on all tables:

1. Go to the Supabase Dashboard
2. Navigate to the SQL Editor
3. Copy and paste the contents of `supabase/migrations/20240603_disable_rls_temporarily.sql`
4. Run the SQL script

## Testing the Security

After running the SQL script, you can test the security of your application:

1. Navigate to `/security-test` in your application
2. Run the client-side tests by clicking "Run RLS Tests"
3. Run the server-side tests by clicking "Run Server Tests"

Since R<PERSON> is disabled, all tests should pass, but this means that users can access all data in the database. This is not secure for production use.

## Next Steps

Once your application is stable, you should implement the proper Clerk-Supabase integration:

1. Follow the official Clerk-Supabase integration guide
2. Update your Supabase client code to use the `accessToken` option
3. Update your RLS policies to use `auth.jwt()->>'sub'` to get the user ID
4. Re-enable RLS on all tables

## Troubleshooting

If you encounter any issues:

1. Check the browser console for errors
2. Verify that the SQL script ran successfully
3. Make sure the tasks table exists in your database

## References

- [Clerk Supabase Integration Documentation](https://clerk.com/docs/integrations/databases/supabase)
- [Supabase Row Level Security Documentation](https://supabase.com/docs/guides/auth/row-level-security)

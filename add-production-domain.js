// This script adds your production domain to Clerk allowed origins
require('dotenv').config({ path: '.env.local' });
const https = require('https');

console.log('Adding production domain to Clerk allowed origins...');

// Check if secret key is present
const secretKey = process.env.CLERK_SECRET_KEY;
if (!secretKey) {
  console.error('❌ CLERK_SECRET_KEY is missing');
  process.exit(1);
}

// Function to make a request to the Clerk API
function makeClerkRequest(method, path, data = null) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'api.clerk.com',
      port: 443,
      path: `/v1${path}`,
      method: method,
      headers: {
        'Authorization': `Bearer ${secretKey}`,
        'Content-Type': 'application/json',
      }
    };

    const req = https.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsedData = JSON.parse(responseData);
          if (res.statusCode >= 200 && res.statusCode < 300) {
            resolve(parsedData);
          } else {
            console.error(`API Error (${res.statusCode}):`, parsedData);
            reject(parsedData);
          }
        } catch (e) {
          reject(new Error(`Failed to parse response: ${e.message}`));
        }
      });
    });
    
    req.on('error', (error) => {
      reject(error);
    });
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

// Get the current instance configuration
async function getInstanceConfig() {
  try {
    console.log('Retrieving current instance configuration...');
    return await makeClerkRequest('GET', '/instance');
  } catch (error) {
    console.error('Failed to retrieve instance configuration:', error);
    return null;
  }
}

// Update the instance configuration with production domain
async function updateAllowedOrigins(instance) {
  try {
    console.log('Updating allowed origins...');
    
    // Current allowed origins
    const currentOrigins = instance.allowed_origins || [];
    console.log('Current allowed origins:', currentOrigins);
    
    // Production domains to add
    const productionDomains = [
      'https://guardiavision.com',
      'https://www.guardiavision.com',
    ];
    
    // Check which domains need to be added
    const domainsToAdd = productionDomains.filter(domain => 
      !currentOrigins.includes(domain)
    );
    
    if (domainsToAdd.length === 0) {
      console.log('✅ All production domains are already configured');
      return true;
    }
    
    // Combine current and new domains
    const newOrigins = [...currentOrigins, ...domainsToAdd];
    console.log('New allowed origins:', newOrigins);
    
    // Update the instance configuration
    const result = await makeClerkRequest('PATCH', '/instance', {
      allowed_origins: newOrigins
    });
    
    console.log('✅ Successfully updated allowed origins');
    return true;
  } catch (error) {
    console.error('Failed to update allowed origins:', error);
    return false;
  }
}

// Main function
async function main() {
  try {
    // Get current instance configuration
    const instance = await getInstanceConfig();
    if (!instance) {
      console.log('❌ Could not retrieve instance configuration. Please check your API keys.');
      return;
    }
    
    // Update allowed origins
    const updated = await updateAllowedOrigins(instance);
    if (updated) {
      console.log('\n✅ Production domains have been added to your Clerk instance');
      console.log('You should now be able to sign up successfully from your production domain');
      console.log('\nNext steps:');
      console.log('1. Clear your browser cookies and local storage');
      console.log('2. Visit your application\'s sign-up page');
      console.log('3. Try to create a new account');
    } else {
      console.log('\n❌ Failed to update allowed origins');
      console.log('Please update them manually in your Clerk Dashboard:');
      console.log('1. Go to https://dashboard.clerk.com');
      console.log('2. Select your application');
      console.log('3. Go to "Settings" > "Domains & URLs"');
      console.log('4. Add your production domains to "Allowed Origins"');
    }
  } catch (error) {
    console.error('An unexpected error occurred:', error);
  }
}

// Run the main function
main();

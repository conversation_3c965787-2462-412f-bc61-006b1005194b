# 🚀 Professional SaaS Payment System Setup Guide

This guide will help you set up a complete, professional payment system with Clerk + Stripe integration, including automatic credit management, subscription handling, and webhook processing.

## 📋 Prerequisites

1. **Clerk Account** with billing enabled
2. **Stripe Account** with API keys
3. **Supabase Project** with database access
4. **Vercel Account** for deployment (optional but recommended)

## 🗄️ Step 1: Database Setup

### Apply the Database Migration

Run this SQL in your Supabase SQL Editor:

```sql
-- Copy the entire content from: supabase/migrations/20241231_professional_saas_schema.sql
-- This creates all necessary tables, functions, and policies
```

### Verify Tables Created

After running the migration, you should have these new tables:
- `subscription_plans` - Plan definitions with pricing and features
- `payment_history` - Complete payment tracking
- `subscription_history` - Subscription change tracking
- Enhanced `users` table with subscription fields
- Enhanced `credit_transactions` table with expiration tracking

## 🔧 Step 2: Environment Variables

Add these to your `.env.local` and Vercel environment:

```env
# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_stripe_webhook_secret

# Clerk Configuration
CLERK_WEBHOOK_SECRET=whsec_your_clerk_webhook_secret
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_your_clerk_publishable_key
CLERK_SECRET_KEY=sk_test_your_clerk_secret_key
CLERK_ENCRYPTION_KEY=your_clerk_encryption_key

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Cron Job Security (optional)
CRON_SECRET_TOKEN=your_secure_random_token
```

## 💳 Step 3: Stripe Setup

### 3.1 Create Products and Prices

In your Stripe Dashboard, create products with these exact IDs (or update the mapping in `webhook-helpers.ts`):

```javascript
// Update these in src/utils/stripe/webhook-helpers.ts
const priceMapping: Record<string, string> = {
  'price_1RSIGpR6OeqomohOPQNu7awg': 'standard', // $1/month
  'price_1RSIHaR6OeqomohOzOEwFOgZ': 'pro',      // $26/month  
  'price_1RSIICR6OeqomohOLsmbhNj8': 'premium',  // $78/month
  // Add yearly prices here
};
```

### 3.2 Configure Stripe Webhook

1. Go to Stripe Dashboard → Webhooks
2. Add endpoint: `https://yourdomain.com/api/webhooks/stripe`
3. Select these events:
   - `checkout.session.completed`
   - `payment_intent.succeeded`
   - `payment_intent.payment_failed`
   - `invoice.payment_succeeded`
   - `invoice.payment_failed`
   - `invoice.upcoming`
   - `customer.subscription.created`
   - `customer.subscription.updated`
   - `customer.subscription.deleted`
   - `customer.subscription.trial_will_end`
   - `customer.created`
   - `customer.updated`
   - `customer.deleted`

4. Copy the webhook signing secret to your environment variables

## 👤 Step 4: Clerk Setup

### 4.1 Enable Billing in Clerk

1. Go to Clerk Dashboard → Configure → Billing
2. Connect your Stripe account
3. Create pricing plans that match your Stripe products

### 4.2 Configure Clerk Webhook

1. Go to Clerk Dashboard → Webhooks
2. Add endpoint: `https://yourdomain.com/api/webhooks/clerk`
3. Select events:
   - `user.created`
   - `user.updated`
   - `user.deleted`

4. Copy the webhook signing secret to your environment variables

## 🚀 Step 5: Deploy and Test

### 5.1 Deploy to Production

```bash
# Commit all changes
git add .
git commit -m "Add professional payment system"
git push

# Deploy to Vercel (if using Vercel)
vercel --prod
```

### 5.2 Test the System

1. **Test User Registration**: Create a new user and verify they appear in Supabase
2. **Test Payment**: Purchase a subscription and verify:
   - Payment recorded in `payment_history`
   - User upgraded in `users` table
   - Credits added with expiration
   - Subscription recorded in `subscription_history`
3. **Test Cancellation**: Cancel subscription and verify downgrade
4. **Test Webhooks**: Check webhook logs in both Stripe and Clerk dashboards

## ⏰ Step 6: Set Up Credit Expiration (Optional)

### 6.1 Manual Credit Expiration

You can manually run credit expiration:
```bash
curl -X POST https://yourdomain.com/api/cron/expire-credits \
  -H "Authorization: Bearer your_cron_secret_token"
```

### 6.2 Automated Credit Expiration

Set up a cron job to run credit expiration automatically:

**Option A: Vercel Cron (Recommended)**
```javascript
// vercel.json
{
  "crons": [
    {
      "path": "/api/cron/expire-credits",
      "schedule": "0 0 * * *"
    }
  ]
}
```

**Option B: GitHub Actions**
```yaml
# .github/workflows/expire-credits.yml
name: Expire Credits
on:
  schedule:
    - cron: '0 0 * * *'
jobs:
  expire-credits:
    runs-on: ubuntu-latest
    steps:
      - name: Expire Credits
        run: |
          curl -X POST ${{ secrets.SITE_URL }}/api/cron/expire-credits \
            -H "Authorization: Bearer ${{ secrets.CRON_SECRET_TOKEN }}"
```

## 🎯 Step 7: Customize for Your Business

### 7.1 Update Plan Features

Edit `supabase/migrations/20241231_professional_saas_schema.sql` to customize:
- Plan names and descriptions
- Credit amounts and expiration periods
- Feature sets (API access, file size limits, etc.)
- Pricing (in cents)

### 7.2 Update Price Mapping

Edit `src/utils/stripe/webhook-helpers.ts` to match your Stripe price IDs:
```javascript
const priceMapping: Record<string, string> = {
  'your_stripe_price_id_1': 'standard',
  'your_stripe_price_id_2': 'pro',
  'your_stripe_price_id_3': 'premium',
};
```

### 7.3 Customize Credit Calculation

Edit the `calculateCreditsFromAmount` function in `webhook-helpers.ts`:
```javascript
export function calculateCreditsFromAmount(amountInCents: number): number {
  // Example: $1 = 100 credits
  const creditsPerDollar = 100;
  const dollars = amountInCents / 100;
  return Math.floor(dollars * creditsPerDollar);
}
```

## 🔍 Step 8: Monitoring and Maintenance

### 8.1 Monitor Webhooks

- Check Stripe Dashboard → Webhooks for delivery status
- Check Clerk Dashboard → Webhooks for delivery status
- Monitor Vercel function logs for errors

### 8.2 Database Maintenance

The system automatically:
- Expires credits based on expiration dates
- Cleans up old payment history (2+ years)
- Cleans up old subscription history (1+ year)
- Downgrades users with failed payments (7+ days)

### 8.3 Key Metrics to Monitor

- Subscription conversion rates
- Payment failure rates
- Credit usage patterns
- Churn rates

## 🆘 Troubleshooting

### Common Issues

1. **Webhooks Failing**: Check webhook URLs are accessible and secrets match
2. **Credits Not Adding**: Verify price mapping in `webhook-helpers.ts`
3. **User Not Found**: Ensure email addresses match between Clerk and Supabase
4. **Database Errors**: Check RLS policies and function permissions

### Debug Tools

- Use the admin tools in `/dashboard/billing` to test and debug
- Check Vercel function logs for detailed error messages
- Use the webhook setup guide for configuration verification

## ✅ Success Checklist

- [ ] Database migration applied successfully
- [ ] All environment variables configured
- [ ] Stripe products and webhooks configured
- [ ] Clerk billing and webhooks configured
- [ ] System deployed to production
- [ ] Test user registration works
- [ ] Test payment flow works
- [ ] Test subscription cancellation works
- [ ] Credit expiration scheduled
- [ ] Monitoring set up

## 🎉 You're Done!

Your professional SaaS payment system is now ready! Users can:
- Sign up and get free credits
- Purchase subscriptions with automatic credit allocation
- Manage their subscriptions (cancel, reactivate)
- View payment history and subscription details
- Have credits automatically expire based on plan rules

The system handles all edge cases including failed payments, subscription changes, user deletions, and automatic maintenance.

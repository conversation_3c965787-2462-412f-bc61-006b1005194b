import { Camera, FileText, Building, Users, Video, ShieldCheck } from "lucide-react"

export function UseCases() {
  const useCases = [
    {
      icon: <Camera className="h-8 w-8 text-green-400" />,
      title: "Journalism",
      description: "Protect identities in sensitive reporting while maintaining story integrity.",
    },
    {
      icon: <Video className="h-8 w-8 text-green-400" />,
      title: "Vlogging",
      description: "Blur bystanders and private information in your public content.",
    },
    {
      icon: <ShieldCheck className="h-8 w-8 text-green-400" />,
      title: "Security Footage",
      description: "Maintain privacy in surveillance while preserving security capabilities.",
    },
    {
      icon: <Building className="h-8 w-8 text-green-400" />,
      title: "Corporate Compliance",
      description: "Ensure visual content meets regulatory requirements before sharing.",
    },
    {
      icon: <FileText className="h-8 w-8 text-green-400" />,
      title: "Legal Documentation",
      description: "Redact sensitive information from visual evidence and documentation.",
    },
    {
      icon: <Users className="h-8 w-8 text-green-400" />,
      title: "Education",
      description: "Protect student privacy in classroom recordings and educational content.",
    },
  ]

  return (
    <section id="use-cases" className="bg-navy-dark py-24">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mx-auto max-w-3xl text-center">
          <h2 className="text-3xl font-bold tracking-tight text-white sm:text-4xl">
            Privacy Solutions for Every Industry
          </h2>
          <p className="mt-4 text-lg text-gray-300">
            See how organizations across different sectors use Guardiavision to protect privacy while maintaining
            content quality.
          </p>
        </div>

        <div className="mt-16 grid gap-x-8 gap-y-12 sm:grid-cols-2 lg:grid-cols-3">
          {useCases.map((useCase, index) => (
            <div key={index} className="group">
              <div className="mb-5 flex h-16 w-16 items-center justify-center rounded-xl bg-navy text-green-400 shadow-md transition-transform duration-300 group-hover:-translate-y-1 group-hover:shadow-lg">
                {useCase.icon}
              </div>
              <h3 className="mb-3 text-xl font-semibold text-white">{useCase.title}</h3>
              <p className="text-gray-300">{useCase.description}</p>
            </div>
          ))}
        </div>

        <div className="mx-auto mt-16 max-w-4xl rounded-2xl bg-gradient-to-r from-blue-400 to-green-400 p-1">
          <div className="rounded-xl bg-navy p-8 sm:p-10">
            <div className="text-center">
              <h3 className="text-2xl font-bold text-white">Need a custom privacy solution?</h3>
              <p className="mt-2 text-gray-300">
                Our team can help you implement privacy protection tailored to your specific needs.
              </p>
              <button className="mt-6 rounded-lg bg-green-400 px-6 py-3 font-medium text-navy transition-colors hover:bg-green-500">
                Contact Our Team
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}


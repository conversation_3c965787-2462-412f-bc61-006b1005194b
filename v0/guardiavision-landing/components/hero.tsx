import { But<PERSON> } from "@/components/ui/button"
import { Shield, Lock, Eye } from "lucide-react"

export function Hero() {
  return (
    <div className="relative overflow-hidden bg-navy">
      {/* Pixelated background pattern inspired by logo */}
      <div className="absolute inset-0 opacity-10">
        <div className="h-full w-full">
          {Array.from({ length: 20 }).map((_, rowIndex) => (
            <div key={`row-${rowIndex}`} className="flex">
              {Array.from({ length: 30 }).map((_, colIndex) => {
                const opacity = Math.random()
                const size = Math.floor(Math.random() * 3) + 1
                const isVisible = Math.random() > 0.6
                return isVisible ? (
                  <div
                    key={`pixel-${rowIndex}-${colIndex}`}
                    className={`h-${size} w-${size} m-1 rounded-sm`}
                    style={{
                      backgroundColor: Math.random() > 0.5 ? "#4ADE80" : "#60A5FA",
                      opacity: opacity * 0.8 + 0.2,
                    }}
                  />
                ) : null
              })}
            </div>
          ))}
        </div>
      </div>

      <div className="container relative mx-auto px-4 py-24 sm:px-6 lg:px-8 lg:py-32">
        <div className="grid grid-cols-1 gap-12 lg:grid-cols-2 lg:gap-8">
          <div className="flex flex-col justify-center">
            <div className="mb-6 inline-flex items-center rounded-full border border-navy-light bg-navy-dark px-3 py-1 text-sm text-gray-300">
              <Shield className="mr-1 h-3.5 w-3.5 text-green-400" />
              <span>Privacy-first content protection</span>
            </div>

            <h1 className="mb-6 text-4xl font-extrabold tracking-tight text-white sm:text-5xl md:text-6xl">
              <span className="block">AI-Powered Privacy</span>
              <span className="block bg-gradient-to-r from-blue-400 to-green-400 bg-clip-text text-transparent">
                For Your Visual Content
              </span>
            </h1>

            <p className="mb-8 max-w-2xl text-xl text-gray-300">
              Automatically blur or remove sensitive content in images and videos. Protect identities, secure
              information, and maintain compliance with ease.
            </p>

            <div className="flex flex-col space-y-4 sm:flex-row sm:space-x-4 sm:space-y-0">
              <Button size="lg" className="bg-green-400 text-navy hover:bg-green-500">
                Start Blurring Now
              </Button>
              <Button
                size="lg"
                variant="outline"
                className="border-blue-400 text-blue-400 hover:bg-blue-400 hover:text-navy"
              >
                Watch Demo
              </Button>
            </div>

            <div className="mt-8 flex items-center text-sm text-gray-400">
              <Lock className="mr-2 h-4 w-4 text-green-400" />
              <span>No credit card required • Cancel anytime</span>
            </div>
          </div>

          <div className="relative flex items-center justify-center lg:justify-end">
            <div className="relative w-full max-w-lg overflow-hidden rounded-2xl border border-navy-light bg-navy-dark p-2 shadow-2xl sm:p-4">
              <div className="aspect-[4/3] overflow-hidden rounded-lg bg-navy">
                <div className="relative h-full w-full">
                  {/* Hero image with pixelation effect */}
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="relative h-full w-full">
                      <div className="absolute left-0 top-0 h-full w-1/2 bg-blue-500/20">
                        {/* Pixelated side (left) */}
                        {Array.from({ length: 10 }).map((_, rowIndex) => (
                          <div key={`row-${rowIndex}`} className="flex">
                            {Array.from({ length: 8 }).map((_, colIndex) => {
                              const opacity = Math.random()
                              return (
                                <div
                                  key={`pixel-${rowIndex}-${colIndex}`}
                                  className="m-1 h-3 w-3 rounded-sm"
                                  style={{
                                    backgroundColor: "#60A5FA",
                                    opacity: opacity * 0.8 + 0.2,
                                  }}
                                />
                              )
                            })}
                          </div>
                        ))}
                      </div>
                      <div className="absolute right-0 top-0 h-full w-1/2 bg-green-500/20">
                        {/* Clear side (right) */}
                        <div className="h-full w-full rounded-r-lg bg-gradient-to-l from-green-400/40 to-transparent"></div>
                      </div>
                    </div>
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="rounded-full bg-green-400 p-4">
                        <Eye className="h-8 w-8 text-navy" />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="mt-4 flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="h-2 w-2 rounded-full bg-green-400"></div>
                  <span className="text-sm font-medium text-gray-300">Privacy Protection Active</span>
                </div>
                <span className="text-xs text-gray-400">Real-time processing</span>
              </div>
            </div>

            {/* Decorative elements */}
            <div className="absolute -bottom-6 -left-6 h-24 w-24 rounded-full bg-green-400/20 blur-2xl"></div>
            <div className="absolute -right-10 top-10 h-32 w-32 rounded-full bg-blue-400/20 blur-3xl"></div>
          </div>
        </div>
      </div>
    </div>
  )
}


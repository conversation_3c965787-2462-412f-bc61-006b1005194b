# File Upload System Documentation

## Overview
The GuardiaVision SaaS uses **Supabase Storage** as the primary cloud storage solution for all file uploads. Files are stored in Supabase buckets and processed through a simulated AI pipeline.

## 🗂️ **Storage Architecture**

### **Supabase Storage Buckets**
1. **`original-uploads`** - Stores original uploaded files
   - **Privacy**: Private (users can only access their own files)
   - **Size Limit**: 50MB per file
   - **File Types**: Images (JPEG, PNG, GIF), Videos (MP4, MOV, AVI)

2. **`processed-uploads`** - Stores processed/AI-enhanced files
   - **Privacy**: Public (for easy access to results)
   - **Size Limit**: 100MB per file
   - **File Types**: Same as original uploads

### **Database Tables**
1. **`processed_images`** - Tracks image processing
2. **`processed_videos`** - Tracks video processing

## 📁 **File Upload Flow**

### **Step 1: File Upload**
```typescript
// Location: src/components/uploads/FileUploader.tsx
const fileName = `${user.id}/${Date.now()}-${Math.random()}.${fileExt}`;

await supabase.storage
  .from('original-uploads')
  .upload(fileName, file);
```

### **Step 2: Database Record Creation**
```typescript
await supabase
  .from('processed_images') // or 'processed_videos'
  .insert({
    user_id: user.id,
    original_url: publicUrl,
    status: 'pending',
    credits_used: 1, // 1 credit per image, variable for videos
    metadata: { originalName, size, type }
  });
```

### **Step 3: Processing Trigger**
```typescript
// Calls /api/process-media to start AI processing
await fetch('/api/process-media', {
  method: 'POST',
  body: JSON.stringify({ mediaType, mediaId })
});
```

### **Step 4: Simulated Processing**
```typescript
// Location: src/app/api/process-media/route.ts
// Currently simulates processing with 2-second delay
// In production, this would call actual AI services
```

## 🔧 **Why Files "Disappear" Instantly**

### **Current Behavior**
Files appear to disappear after upload because:

1. **Upload completes** → File goes to Supabase storage ✅
2. **Database record created** → Status: 'pending' ✅
3. **Processing starts** → Status: 'processing' ✅
4. **UI clears upload area** → Files removed from interface ❌
5. **Processing completes** → Status: 'completed' ✅

### **The Issue**
The `FileUploader` component clears the upload area immediately after upload, before users can see the processing status.

### **Solution**
Keep files visible during processing with status indicators:

```typescript
// Instead of clearing immediately:
setFiles([]);

// Keep files with status:
setFiles(prev => prev.map(file => ({
  ...file,
  status: 'processing',
  progress: 100
})));
```

## 🌐 **Cloud Storage Details**

### **Supabase Storage Features**
- ✅ **CDN Integration** - Fast global delivery
- ✅ **Automatic Backups** - Built-in redundancy
- ✅ **Row Level Security** - User-based access control
- ✅ **Real-time Updates** - Live status changes
- ✅ **Image Transformations** - Resize, crop, optimize

### **File Organization**
```
original-uploads/
├── user_123/
│   ├── 1704067200000-abc123.jpg
│   ├── 1704067300000-def456.mp4
│   └── ...
└── user_456/
    ├── 1704067400000-ghi789.png
    └── ...
```

### **Access Control**
```sql
-- RLS Policy: Users can only access their own files
CREATE POLICY "User can access their own files" 
ON storage.objects FOR ALL 
USING (auth.uid()::text = (storage.foldername(name))[1]);
```

## 🔄 **Processing Pipeline**

### **Current Implementation (Simulation)**
```typescript
// 1. Update status to 'processing'
await supabase.from(tableName)
  .update({ status: 'processing' })
  .eq('id', mediaId);

// 2. Simulate processing delay
await new Promise(resolve => setTimeout(resolve, 2000));

// 3. Update with "processed" result
await supabase.from(tableName)
  .update({
    status: 'completed',
    processed_url: originalUrl, // Currently just copies original
  })
  .eq('id', mediaId);
```

### **Production Implementation (Future)**
```typescript
// Replace simulation with actual AI service calls:
const processedResult = await callRunPodAPI({
  imageUrl: originalUrl,
  processingType: 'face_blur'
});

await supabase.from(tableName)
  .update({
    status: 'completed',
    processed_url: processedResult.outputUrl,
  })
  .eq('id', mediaId);
```

## 🛠️ **Configuration Files**

### **Storage Setup**
```typescript
// Location: src/utils/supabase/storage-setup.ts
export async function setupStorageBuckets() {
  // Creates buckets if they don't exist
  // Sets up RLS policies
  // Configures file size limits and MIME types
}
```

### **Supabase Config**
```toml
# Location: supabase/config.toml
[storage]
enabled = true
file_size_limit = "50MiB"
```

## 📊 **Credit System Integration**

### **Credit Calculation**
- **Images**: 1 credit per image
- **Videos**: 1 credit per 10MB of video

### **Credit Deduction**
Credits are deducted immediately upon upload, before processing starts.

## 🔍 **Debugging File Issues**

### **Check Storage Buckets**
```sql
-- View all buckets
SELECT * FROM storage.buckets;

-- View files in bucket
SELECT * FROM storage.objects WHERE bucket_id = 'original-uploads';
```

### **Check Database Records**
```sql
-- View recent uploads
SELECT * FROM processed_images ORDER BY created_at DESC LIMIT 10;
SELECT * FROM processed_videos ORDER BY created_at DESC LIMIT 10;
```

### **Check Processing Status**
```typescript
// In browser console
fetch('/api/process-media', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ mediaType: 'image', mediaId: 123 })
});
```

## 🚀 **Enabling Real AI Processing**

### **Step 1: Replace Simulation**
Update `/api/process-media/route.ts` to call actual AI services instead of simulation.

### **Step 2: Configure RunPod Integration**
Add RunPod API credentials and endpoints for face blurring/anonymization.

### **Step 3: Handle Processing Results**
Store actual processed files in `processed-uploads` bucket.

### **Step 4: Error Handling**
Implement retry logic and failure notifications.

## 📋 **Summary**

- ✅ **Storage**: Supabase Storage (not local)
- ✅ **Buckets**: `original-uploads` (private), `processed-uploads` (public)
- ✅ **Database**: Tracks all uploads and processing status
- ✅ **Processing**: Currently simulated, ready for AI integration
- ❌ **UI Issue**: Files disappear too quickly (needs status display)
- 🔄 **Next Steps**: Keep files visible during processing, integrate real AI

The system is fully cloud-based with Supabase handling all storage, security, and access control. Files don't actually disappear - they're safely stored in the cloud, but the UI clears them too quickly.

import { expect, test } from '@playwright/test';

test.describe('I18n', () => {
  test.describe('English Only', () => {
    test('should display English text on the homepage', async ({ page }) => {
      await page.goto('/');

      await expect(page.getByText('The perfect SaaS template to build')).toBeVisible();
    });

    test('should display English text on the sign-in page', async ({ page }) => {
      await page.goto('/sign-in');

      await expect(page.getByText('Email address')).toBeVisible();
    });
  });
});

# Supabase-Clerk Integration: Issues and Fixes

This document outlines the specific issues we encountered with the Supabase-Clerk integration and the solutions that actually worked.

## Table of Contents

1. [Issue #1: Authentication Not Working with RLS Enabled](#issue-1-authentication-not-working-with-rls-enabled)
2. [Issue #2: Insecure RLS Policies](#issue-2-insecure-rls-policies)
3. [Issue #3: Clerk JWT <PERSON> Not Being Recognized](#issue-3-clerk-jwt-token-not-being-recognized)
4. [Testing the Integration](#testing-the-integration)

## Issue #1: Authentication Not Working with RLS Enabled

### Problem

When Row Level Security (RLS) was enabled on Supabase tables, users couldn't access their data even when authenticated through <PERSON>. The application would work with <PERSON><PERSON> disabled but fail with RLS enabled.

### Root Cause

The Supabase client wasn't properly configured to send the Clerk JWT token to Supabase, so the database couldn't identify the user making the request.

### Solution That Worked

We simplified the Supabase client configuration to use the official Clerk-Supabase integration approach:

```typescript
// Client-side Supabase client
import { createClient } from '@supabase/supabase-js';
import { useSession } from '@clerk/nextjs';
import { useMemo } from 'react';

export function useSupabaseClient() {
  const { session } = useSession();

  const supabaseClient = useMemo(() => {
    return createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        // Use the accessToken option for the official Clerk-Supabase integration
        async accessToken() {
          return session?.getToken() ?? null;
        },
      }
    );
  }, [session]);

  return supabaseClient;
}
```

```typescript
// Server-side Supabase client
import { createClient } from '@supabase/supabase-js';
import { auth } from '@clerk/nextjs/server';

export function createServerSupabaseClient() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      async accessToken() {
        const { getToken } = auth();
        return getToken();
      },
    }
  );
}
```

This ensures that the Clerk JWT token is properly passed to Supabase with each request.

## Issue #2: Insecure RLS Policies

### Problem

Our initial RLS policies were configured to allow all users to access all data, which defeated the purpose of enabling RLS. The policies had `qual: "true"` and `with_check: "true"`, which meant "allow access to all rows" and "allow all inserts".

### Root Cause

The RLS policies were created with permissive conditions that didn't actually restrict access based on user identity.

### Solution That Worked

We updated all RLS policies to properly restrict access based on the user's Clerk ID:

```sql
-- Fix users table policies
DROP POLICY IF EXISTS "Users can view their own data" ON public.users;
CREATE POLICY "Users can view their own data"
  ON public.users
  FOR SELECT
  TO authenticated
  USING (id = auth.jwt()->>'sub');

DROP POLICY IF EXISTS "Users can update their own data" ON public.users;
CREATE POLICY "Users can update their own data"
  ON public.users
  FOR UPDATE
  TO authenticated
  USING (id = auth.jwt()->>'sub');

-- Fix credit_transactions table policies
DROP POLICY IF EXISTS "Users can view their own credit transactions" ON public.credit_transactions;
CREATE POLICY "Users can view their own credit transactions"
  ON public.credit_transactions
  FOR SELECT
  TO authenticated
  USING (user_id = auth.jwt()->>'sub');

-- Fix processed_images table policies
DROP POLICY IF EXISTS "Users can view their own processed images" ON public.processed_images;
CREATE POLICY "Users can view their own processed images"
  ON public.processed_images
  FOR SELECT
  TO authenticated
  USING (user_id = auth.jwt()->>'sub');

DROP POLICY IF EXISTS "Users can insert their own processed images" ON public.processed_images;
CREATE POLICY "Users can insert their own processed images"
  ON public.processed_images
  FOR INSERT
  TO authenticated
  WITH CHECK (user_id = auth.jwt()->>'sub');

-- Fix processed_videos table policies
DROP POLICY IF EXISTS "Users can view their own processed videos" ON public.processed_videos;
CREATE POLICY "Users can view their own processed videos"
  ON public.processed_videos
  FOR SELECT
  TO authenticated
  USING (user_id = auth.jwt()->>'sub');

DROP POLICY IF EXISTS "Users can insert their own processed videos" ON public.processed_videos;
CREATE POLICY "Users can insert their own processed videos"
  ON public.processed_videos
  FOR INSERT
  TO authenticated
  WITH CHECK (user_id = auth.jwt()->>'sub');

-- Fix tasks table policies
DROP POLICY IF EXISTS "Users can view their own tasks" ON public.tasks;
CREATE POLICY "Users can view their own tasks"
  ON public.tasks
  FOR SELECT
  TO authenticated
  USING (user_id = auth.jwt()->>'sub');

DROP POLICY IF EXISTS "Users can insert their own tasks" ON public.tasks;
CREATE POLICY "Users can insert their own tasks"
  ON public.tasks
  FOR INSERT
  TO authenticated
  WITH CHECK (user_id = auth.jwt()->>'sub');
```

These updated policies ensure that users can only access their own data by comparing the `user_id` field (or `id` for the users table) with the `sub` claim from the Clerk JWT token.

## Issue #3: Clerk JWT Token Not Being Recognized

### Problem

Even with the correct client configuration, sometimes the Clerk JWT token wasn't being recognized by Supabase.

### Root Cause

Supabase needs to be configured to recognize Clerk as a JWT issuer.

### Solution That Worked

We set up Clerk as a third-party auth provider in Supabase:

1. In the Clerk Dashboard:
   - Go to "Integrations" > "Supabase"
   - Activate the Supabase integration
   - Copy the Clerk domain (e.g., `clerk.your-app.clerk.accounts.dev`)

2. In the Supabase Dashboard:
   - Go to "Authentication" > "Providers"
   - Add Clerk as a provider
   - Paste the Clerk domain

This ensures that Supabase recognizes and validates the Clerk JWT tokens.

## Testing the Integration

To verify that our fixes worked, we created a security test page at `/security-test` that tests both client-side and server-side access to Supabase with RLS enabled.

### Client-Side Test Results

The client-side tests confirmed that:
- Users can access their own data
- Users cannot access other users' data
- Users can insert data with their own user ID
- Users cannot insert data with another user ID

### Server-Side Test Results

The server-side tests confirmed that:
- The server can access the authenticated user's data
- When querying all users, RLS filters the results to only show the authenticated user's data

### SQL Test Results

We also ran SQL tests to verify the RLS policies:

```sql
SELECT
  schemaname,
  tablename,
  policyname,
  permissive,
  roles,
  cmd,
  qual,
  with_check
FROM
  pg_policies
WHERE
  schemaname = 'public'
ORDER BY
  tablename, policyname;
```

The results confirmed that all tables have RLS enabled with policies that restrict access based on user identity.

## Conclusion

The key to making Clerk and Supabase work together with RLS enabled was:

1. **Proper Client Configuration**: Using the `accessToken` option to pass the Clerk JWT token to Supabase
2. **Secure RLS Policies**: Using `auth.jwt()->>'sub'` to get the user ID in RLS policies
3. **Clerk as Auth Provider**: Setting up Clerk as a third-party auth provider in Supabase

With these fixes in place, our application now properly enforces data isolation between users, ensuring that users can only access their own data.

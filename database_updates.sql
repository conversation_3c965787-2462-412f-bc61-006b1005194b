-- Add subscription tracking columns to users table
ALTER TABLE public.users ADD COLUMN IF NOT EXISTS clerk_plan_id TEXT;
ALTER TABLE public.users ADD COLUMN IF NOT EXISTS subscription_status TEXT DEFAULT 'free';
ALTER TABLE public.users ADD COLUMN IF NOT EXISTS subscription_period_end TIMESTAMP WITH TIME ZONE;
ALTER TABLE public.users ADD COLUMN IF NOT EXISTS is_annual BOOLEAN DEFAULT false;

-- Update credit_transactions table to include payment tracking
ALTER TABLE public.credit_transactions ADD COLUMN IF NOT EXISTS transaction_type TEXT DEFAULT 'usage';
ALTER TABLE public.credit_transactions ADD COLUMN IF NOT EXISTS stripe_payment_id TEXT;

-- Update existing users to have proper subscription status
UPDATE public.users 
SET subscription_status = 'active' 
WHERE subscription_status IS NULL OR subscription_status = '';

-- Create index for better performance
CREATE INDEX IF NOT EXISTS idx_users_clerk_plan_id ON public.users(clerk_plan_id);
CREATE INDEX IF NOT EXISTS idx_users_subscription_status ON public.users(subscription_status);
CREATE INDEX IF NOT EXISTS idx_credit_transactions_type ON public.credit_transactions(transaction_type);

-- Update RLS policies to include new columns
-- Note: Run these if you have RLS enabled and need to update policies

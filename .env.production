# FIXME: Configure environment variables for production

NEXT_PUBLIC_SENTRY_DSN=



NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_live_Y2xlcmsuZ3VhcmRpYXZpc2lvbi5jb20k
CLERK_SECRET_KEY=**************************************************

# Clerk configuration
NEXT_PUBLIC_CLERK_SIGN_IN_URL=/sign-in
NEXT_PUBLIC_CLERK_SIGN_UP_URL=/sign-up
NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL=/dashboard
NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL=/dashboard

# OAuth configuration
NEXT_PUBLIC_CLERK_OAUTH_CALLBACK_URL=/dashboard
# Hosting
# Replace by your domain name
# NEXT_PUBLIC_APP_URL=https://example.com

# Sentry DSN
NEXT_PUBLIC_SENTRY_DSN=

# Stripe
# If you need a real Stripe subscription payment with checkout page, customer portal, webhook, etc.
# You can check out the Next.js Boilerplate Pro: https://nextjs-boilerplate.com/pro-saas-starter-kit
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_live
# Use Stripe test mode price id or production price id
BILLING_PLAN_ENV=prod

######## [BEGIN] SENSITIVE DATA ######## For security reason, don't update the following variables (secret key) directly in this file.
######## Please create a new file named `.env.production.local`, all environment files ending with `.local` won't be tracked by Git.
######## After creating the file, you can add the following variables.
# Database
# Using an incorrect DATABASE_URL value, Next.js build will timeout and you will get the following error: "because it took more than 60 seconds"
# DATABASE_URL=postgresql://postgres@localhost:5432/postgres

# Stripe
STRIPE_SECRET_KEY=your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret

# Error monitoring
# SENTRY_AUTH_TOKEN=

# Logging ingestion
# LOGTAIL_SOURCE_TOKEN=
######## [END] SENSITIVE DATA

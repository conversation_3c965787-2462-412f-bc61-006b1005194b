import { NextRequest, NextResponse } from 'next/server';
import { clerkClient } from '@clerk/nextjs/server';

export async function POST(req: NextRequest) {
  try {
    const { email, password, firstName, lastName } = await req.json();

    if (!email || !password) {
      return NextResponse.json(
        { error: 'Email and password are required' },
        { status: 400 }
      );
    }

    console.log(`API: Attempting to create user with email ${email}`);

    try {
      // Create the user directly with the Clerk API
      const user = await clerkClient.users.createUser({
        emailAddress: [email],
        password,
        firstName: firstName || 'User',
        lastName: lastName || '',
        skipPasswordChecks: true,
        skipPasswordRequirement: true,
      });

      console.log(`API: Created user with ID: ${user.id}`);

      // We can't create a session directly with the Clerk API in this context
      // Instead, we'll just return the user ID and let the client handle authentication
      console.log(`API: User created successfully, returning user ID`);

      return NextResponse.json({
        success: true,
        userId: user.id,
        message: 'User created successfully. Please sign in with your credentials.',
      });
    } catch (createErr: any) {
      console.error('API: Error creating user:', createErr);

      return NextResponse.json({
        success: false,
        error: createErr.message || 'Failed to create user',
        details: createErr.errors || [],
      }, { status: 500 });
    }
  } catch (err: any) {
    console.error('API: Server error:', err);

    return NextResponse.json(
      { error: err.message || 'Internal server error' },
      { status: 500 }
    );
  }
}

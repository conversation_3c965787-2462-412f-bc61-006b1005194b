"use client"

import React from 'react'

interface YouTubeVideoProps {
  videoId: string
  autoplay?: boolean
  title?: string
  className?: string
}

export function YouTubeVideo({ 
  videoId, 
  autoplay = false, 
  title = "YouTube Video",
  className = "w-full aspect-video rounded-lg"
}: YouTubeVideoProps) {
  // Construct the YouTube embed URL with proper parameters
  const embedUrl = `https://www.youtube.com/embed/${videoId}?rel=0&showinfo=0&modestbranding=1${autoplay ? '&autoplay=1' : ''}`
  
  return (
    <div className={className}>
      <iframe
        src={embedUrl}
        title={title}
        className="w-full h-full rounded-lg"
        style={{ border: 0 }}
        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
        allowFullScreen
      />
    </div>
  )
}

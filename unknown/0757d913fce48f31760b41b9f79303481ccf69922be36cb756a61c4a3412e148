'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';

export default function VerifyEmailFixPage() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [result, setResult] = useState<any>(null);
  const router = useRouter();

  const handleCheckEmail = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      setLoading(true);
      setError('');
      setResult(null);

      // Check if the email exists using the enhanced endpoint
      const response = await fetch('/api/clerk-fix-verified', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          action: 'check'
        }),
      });

      if (!response.ok) {
        throw new Error(`API error: ${response.status}`);
      }

      const data = await response.json();
      setResult(data);
    } catch (err: any) {
      console.error('Error checking email:', err);
      setError(err.message || 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  const handleFixVerified = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      setLoading(true);
      setError('');
      setResult(null);

      // Try to fix the "already verified" issue by creating a new user
      const response = await fetch('/api/clerk-fix-verified', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          password,
          action: 'create_user'
        }),
      });

      if (!response.ok) {
        throw new Error(`API error: ${response.status}`);
      }

      const data = await response.json();
      setResult(data);
    } catch (err: any) {
      console.error('Error fixing verified issue:', err);
      setError(err.message || 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteUser = async () => {
    if (!result?.users?.[0]?.id) {
      setError('No user ID found to delete');
      return;
    }

    try {
      setLoading(true);
      setError('');

      // Delete the user account
      const response = await fetch('/api/clerk-fix-verified', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          action: 'delete_user'
        }),
      });

      if (!response.ok) {
        throw new Error(`API error: ${response.status}`);
      }

      const data = await response.json();
      setResult(data);
    } catch (err: any) {
      console.error('Error deleting user:', err);
      setError(err.message || 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-900 text-white p-8">
      <div className="max-w-md mx-auto bg-gray-800 p-6 rounded-lg shadow-lg">
        <h1 className="text-2xl font-bold mb-6">Email Verification Fix Tool</h1>

        <form onSubmit={handleCheckEmail} className="mb-8">
          <div className="mb-4">
            <label className="block mb-2">Email Address</label>
            <input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="w-full p-2 bg-gray-700 rounded"
              required
            />
          </div>

          <div className="mb-4">
            <label className="block mb-2">Password (for fix attempts)</label>
            <input
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="w-full p-2 bg-gray-700 rounded"
            />
          </div>

          <div className="flex space-x-4">
            <button
              type="submit"
              className="px-4 py-2 bg-blue-600 rounded hover:bg-blue-700"
              disabled={loading}
            >
              {loading ? 'Checking...' : 'Check Email Status'}
            </button>

            <button
              type="button"
              onClick={handleFixVerified}
              className="px-4 py-2 bg-green-600 rounded hover:bg-green-700"
              disabled={loading || !password}
            >
              {loading ? 'Processing...' : 'Fix Verified Issue'}
            </button>
          </div>
        </form>

        {error && (
          <div className="mb-4 p-3 bg-red-800 rounded">
            <p>{error}</p>
          </div>
        )}

        {result && (
          <div className="mb-4 p-3 bg-gray-700 rounded">
            <h3 className="font-bold mb-2">Result:</h3>

            {/* Show delete button if a user was found */}
            {result.exists && result.users && result.users.length > 0 && (
              <div className="mb-4 p-3 bg-red-900 rounded">
                <p className="mb-2">User account found with this email. You can delete it to resolve the "already verified" issue.</p>
                <button
                  type="button"
                  onClick={handleDeleteUser}
                  className="px-4 py-2 bg-red-600 rounded hover:bg-red-700"
                  disabled={loading}
                >
                  {loading ? 'Deleting...' : 'Delete User Account'}
                </button>
              </div>
            )}

            {/* Show success message if a user was deleted */}
            {result.success && result.deletedUserId && (
              <div className="mb-4 p-3 bg-green-900 rounded">
                <p className="mb-2">✅ User account successfully deleted!</p>
                <p>You should now be able to sign up with this email address.</p>
              </div>
            )}

            <pre className="whitespace-pre-wrap overflow-auto max-h-60">
              {JSON.stringify(result, null, 2)}
            </pre>

            {result.recommendation && (
              <div className="mt-4 p-2 bg-blue-900 rounded">
                <p><strong>Recommendation:</strong> {result.recommendation}</p>
              </div>
            )}

            {result.alternativeSolution && (
              <div className="mt-2 p-2 bg-green-900 rounded">
                <p><strong>Alternative:</strong> {result.alternativeSolution}</p>
              </div>
            )}

            {/* Show next steps if available */}
            {result.nextStep && (
              <div className="mt-2 p-2 bg-yellow-900 rounded">
                <p><strong>Next Step:</strong> {result.nextStep}</p>
              </div>
            )}
          </div>
        )}

        <div className="mt-8 border-t border-gray-700 pt-4">
          <h3 className="font-bold mb-2">Other Options:</h3>
          <ul className="list-disc pl-5 space-y-2">
            <li>
              <button
                onClick={() => router.push('/fixed-signup')}
                className="text-blue-400 hover:underline"
              >
                Try Fixed Signup Page
              </button>
            </li>
            <li>
              <button
                onClick={() => router.push('/server-signup')}
                className="text-blue-400 hover:underline"
              >
                Try Server-Side Signup
              </button>
            </li>
            <li>
              <button
                onClick={() => router.push('/simple-signup')}
                className="text-blue-400 hover:underline"
              >
                Try Simple Signup
              </button>
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
}
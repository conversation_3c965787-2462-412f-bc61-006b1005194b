"use client"

import { useState } from "react"
import Link from "next/link"
import Image from "next/image"
import { But<PERSON> } from "@/components/ui/button"
import { Menu, X } from "lucide-react"

export function Navbar() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)

  return (
    <nav className="sticky top-0 z-50 border-b border-navy-light bg-navy/90 backdrop-blur-md">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex h-16 items-center justify-between">
          <div className="flex items-center">
            <Link href="/" className="flex items-center">
              <Image src="/images/logo.png" alt="Guardiavision Logo" width={40} height={40} className="h-10 w-10" />
              <span className="ml-3 text-xl font-bold text-white">Guardiavision</span>
            </Link>
          </div>

          {/* Desktop navigation */}
          <div className="hidden md:block">
            <div className="ml-10 flex items-center space-x-8">
              <Link href="#features" className="text-sm text-gray-300 hover:text-green-400">
                Features
              </Link>
              <Link href="#use-cases" className="text-sm text-gray-300 hover:text-green-400">
                Use Cases
              </Link>
              <Link href="#pricing" className="text-sm text-gray-300 hover:text-green-400">
                Pricing
              </Link>
              <Link href="#testimonials" className="text-sm text-gray-300 hover:text-green-400">
                Testimonials
              </Link>
              <Button variant="outline" className="border-green-400 text-green-400 hover:bg-green-400 hover:text-navy">
                Log In
              </Button>
              <Button className="bg-green-400 text-navy hover:bg-green-500">Start Free Trial</Button>
            </div>
          </div>

          {/* Mobile menu button */}
          <div className="flex md:hidden">
            <button
              type="button"
              className="inline-flex items-center justify-center rounded-md p-2 text-gray-400 hover:bg-navy-light hover:text-white"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
            >
              <span className="sr-only">Open main menu</span>
              {isMenuOpen ? (
                <X className="block h-6 w-6" aria-hidden="true" />
              ) : (
                <Menu className="block h-6 w-6" aria-hidden="true" />
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile menu */}
      {isMenuOpen && (
        <div className="md:hidden">
          <div className="space-y-1 px-2 pb-3 pt-2">
            <Link
              href="#features"
              className="block rounded-md px-3 py-2 text-base font-medium text-gray-300 hover:bg-navy-light hover:text-white"
              onClick={() => setIsMenuOpen(false)}
            >
              Features
            </Link>
            <Link
              href="#use-cases"
              className="block rounded-md px-3 py-2 text-base font-medium text-gray-300 hover:bg-navy-light hover:text-white"
              onClick={() => setIsMenuOpen(false)}
            >
              Use Cases
            </Link>
            <Link
              href="#pricing"
              className="block rounded-md px-3 py-2 text-base font-medium text-gray-300 hover:bg-navy-light hover:text-white"
              onClick={() => setIsMenuOpen(false)}
            >
              Pricing
            </Link>
            <Link
              href="#testimonials"
              className="block rounded-md px-3 py-2 text-base font-medium text-gray-300 hover:bg-navy-light hover:text-white"
              onClick={() => setIsMenuOpen(false)}
            >
              Testimonials
            </Link>
            <div className="mt-4 space-y-2 px-3">
              <Button
                variant="outline"
                className="w-full border-green-400 text-green-400 hover:bg-green-400 hover:text-navy"
              >
                Log In
              </Button>
              <Button className="w-full bg-green-400 text-navy hover:bg-green-500">Start Free Trial</Button>
            </div>
          </div>
        </div>
      )}
    </nav>
  )
}


import type { NextApiRequest, NextApiResponse } from 'next';

type ResponseData = {
  message: string;
  timestamp: string;
  success?: boolean;
  error?: string;
};

export default function handler(
  _req: NextApiRequest,
  res: NextApiResponse<ResponseData>
) {
  try {
    // Set proper content type header
    res.setHeader('Content-Type', 'application/json');

    // Simulate a successful response
    res.status(200).json({
      message: 'Pages API is working!',
      timestamp: new Date().toISOString(),
      success: true
    });
  } catch (error) {
    console.error('Error in Pages API handler:', error);

    // Set proper content type header
    res.setHeader('Content-Type', 'application/json');

    res.status(500).json({
      message: 'Internal Server Error',
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}

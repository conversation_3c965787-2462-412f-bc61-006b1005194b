"use client"

import { useState } from "react"
import { <PERSON>lide<PERSON> } from "@/components/ui/Slider"
import { Card, CardContent } from "@/components/ui/Card"

export function Demo() {
  const [sliderValue, setSliderValue] = useState(50)

  return (
    <section id="demo" className="bg-navy-dark py-24">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mx-auto max-w-3xl text-center">
          <h2 className="text-3xl font-bold tracking-tight text-white sm:text-4xl">See Guardiavision in Action</h2>
          <p className="mt-4 text-lg text-gray-300">
            Drag the slider to reveal how our AI precisely blurs sensitive content while preserving the integrity of
            your media.
          </p>
        </div>

        <div className="mx-auto mt-16 max-w-4xl">
          <Card className="overflow-hidden border-navy-light bg-navy shadow-xl">
            <CardContent className="p-0">
              <div className="relative aspect-video w-full overflow-hidden">
                <div className="absolute inset-0">
                  {/* Original image (right side) */}
                  <img
                    src="/placeholder.svg?height=720&width=1280"
                    alt="Original content"
                    className="h-full w-full object-cover"
                  />
                </div>

                <div className="absolute inset-0 overflow-hidden" style={{ width: `${sliderValue}%` }}>
                  {/* Blurred image (left side) with pixelation effect */}
                  <div className="relative h-full" style={{ width: `${100 / (sliderValue / 100)}%` }}>
                    <div className="absolute inset-0">
                      <img
                        src="/placeholder.svg?height=720&width=1280"
                        alt="Blurred content"
                        className="h-full w-full object-cover"
                      />
                    </div>

                    {/* Pixelation overlay */}
                    <div className="absolute inset-0">
                      <div className="grid h-full w-full grid-cols-12 grid-rows-8">
                        {Array.from({ length: 96 }).map((_, index) => {
                          const isBlurred = Math.random() > 0.5
                          return isBlurred ? (
                            <div
                              key={index}
                              className="flex items-center justify-center"
                              style={{
                                backgroundColor:
                                  Math.random() > 0.7 ? "rgba(74, 222, 128, 0.3)" : "rgba(96, 165, 250, 0.3)",
                              }}
                            >
                              {Math.random() > 0.8 && <div className="h-full w-full rounded-sm bg-green-400/20"></div>}
                            </div>
                          ) : null
                        })}
                      </div>
                    </div>

                    {/* Simulated blur areas */}
                    <div className="absolute left-[20%] top-[30%] h-16 w-16 rounded-full bg-green-400/50 blur-md"></div>
                    <div className="absolute left-[60%] top-[40%] h-20 w-20 rounded-full bg-green-400/50 blur-md"></div>
                  </div>
                </div>

                {/* Divider line */}
                <div
                  className="absolute bottom-0 top-0 w-1 cursor-ew-resize bg-white shadow-[0_0_10px_rgba(0,0,0,0.3)]"
                  style={{ left: `${sliderValue}%` }}
                >
                  <div className="absolute left-1/2 top-1/2 flex h-10 w-10 -translate-x-1/2 -translate-y-1/2 items-center justify-center rounded-full border-4 border-white bg-green-400 shadow-lg">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path
                        d="M8 5L3 10L8 15M16 5L21 10L16 15"
                        stroke="#0a192f"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                  </div>
                </div>
              </div>

              <div className="bg-navy-light p-6">
                <Slider
                  value={[sliderValue]}
                  onValueChange={(value) => {
                    if (value && value.length > 0 && typeof value[0] === 'number') {
                      setSliderValue(value[0])
                    }
                  }}
                  min={0}
                  max={100}
                  step={1}
                  className="py-4"
                />
                <div className="mt-2 flex justify-between text-sm text-gray-300">
                  <span>Protected Content</span>
                  <span>Original Content</span>
                </div>
              </div>
            </CardContent>
          </Card>

          <div className="mt-8 flex flex-wrap justify-center gap-4 text-center">
            <div className="min-w-[180px] rounded-lg bg-navy-light p-4">
              <div className="text-2xl font-bold text-green-400">99.8%</div>
              <div className="text-sm text-gray-300">Detection Accuracy</div>
            </div>
            <div className="min-w-[180px] rounded-lg bg-navy-light p-4">
              <div className="text-2xl font-bold text-green-400">&lt;50ms</div>
              <div className="text-sm text-gray-300">Processing Time</div>
            </div>
            <div className="min-w-[180px] rounded-lg bg-navy-light p-4">
              <div className="text-2xl font-bold text-green-400">GDPR</div>
              <div className="text-sm text-gray-300">Compliant</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}


import { <PERSON>, CardContent } from "@/components/ui/Card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"

export function Testimonials() {
  const testimonials = [
    {
      quote:
        "Guardiavision has transformed how we handle sensitive footage. The AI accuracy is impressive, and it's saved us countless hours of manual blurring.",
      author: "<PERSON>",
      role: "Video Editor, Global News Network",
      avatar: "<PERSON><PERSON>",
    },
    {
      quote:
        "As a documentary filmmaker, I need to protect identities without compromising quality. Guardiavision does exactly that, with an interface that's incredibly easy to use.",
      author: "<PERSON>",
      role: "Independent Filmmaker",
      avatar: "MC",
    },
    {
      quote:
        "The API integration was seamless. We've incorporated Guardiavision into our security platform, and our clients love the added privacy protection.",
      author: "<PERSON><PERSON>",
      role: "CTO, SecureVision Inc.",
      avatar: "PP",
    },
  ]

  const logos = [
    "TechCorp",
    "MediaGlobal",
    "SecureData",
    "PrivacyFirst",
    "ContentShield",
    "DataGuard",
    "TrustSec",
    "VisualProtect",
  ]

  return (
    <section id="testimonials" className="py-24">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mx-auto max-w-3xl text-center">
          <h2 className="text-3xl font-bold tracking-tight text-white sm:text-4xl">
            Trusted by Content Creators and Enterprises
          </h2>
          <p className="mt-4 text-lg text-gray-300">
            Join thousands of professionals who rely on Guardiavision for their privacy needs.
          </p>
        </div>

        <div className="mt-16 grid gap-8 md:grid-cols-3">
          {testimonials.map((testimonial, index) => (
            <Card key={index} className="border-navy-light bg-navy-dark shadow-md">
              <CardContent className="p-6 pt-6">
                <div className="mb-6 text-lg text-gray-200">"{testimonial.quote}"</div>
                <div className="flex items-center">
                  <Avatar className="h-10 w-10 border border-navy-light">
                    <AvatarImage src={`/placeholder.svg?height=40&width=40&text=${testimonial.avatar}`} />
                    <AvatarFallback className="bg-green-400/20 text-green-400">{testimonial.avatar}</AvatarFallback>
                  </Avatar>
                  <div className="ml-3">
                    <div className="text-sm font-medium text-white">{testimonial.author}</div>
                    <div className="text-xs text-gray-400">{testimonial.role}</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="mt-20">
          <h3 className="text-center text-sm font-semibold uppercase tracking-wider text-gray-400">
            Trusted by leading companies
          </h3>
          <div className="mt-8 flex flex-wrap justify-center gap-x-8 gap-y-4">
            {logos.map((logo, index) => (
              <div key={index} className="flex h-12 items-center justify-center">
                <div className="text-lg font-bold text-gray-500 transition-colors hover:text-green-400">{logo}</div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}


import sgMail from '@sendgrid/mail';

// Initialize SendGrid with API key
if (process.env.SENDGRID_API_KEY) {
  sgMail.setApiKey(process.env.SENDGRID_API_KEY);
}

interface SendEmailParams {
  to: string;
  subject: string;
  html: string;
  from?: string;
}

/**
 * Send an email using SendGrid
 */
export async function sendEmail({ to, subject, html, from }: SendEmailParams) {
  // Default from email if not provided
  const fromEmail = from || process.env.EMAIL_FROM || '<EMAIL>';
  
  try {
    const msg = {
      to,
      from: fromEmail,
      subject,
      html,
    };
    
    const response = await sgMail.send(msg);
    console.log('Email sent successfully:', response[0].statusCode);
    return { success: true };
  } catch (error) {
    console.error('Error sending email:', error);
    return { success: false, error };
  }
}

/**
 * Send a welcome email to a new user
 */
export async function sendWelcomeEmail(email: string, firstName: string = '') {
  const name = firstName || 'there'; // Default to "there" if no first name
  
  const subject = 'Welcome to Guardia Vision!';
  
  // Create a beautiful HTML email template
  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Welcome to Guardia Vision</title>
      <style>
        body {
          font-family: 'Helvetica Neue', Arial, sans-serif;
          line-height: 1.6;
          color: #333;
          background-color: #f9f9f9;
          margin: 0;
          padding: 0;
        }
        .container {
          max-width: 600px;
          margin: 0 auto;
          padding: 20px;
          background-color: #ffffff;
        }
        .header {
          background-color: #0a192f;
          padding: 20px;
          text-align: center;
          border-radius: 5px 5px 0 0;
        }
        .header h1 {
          color: #ffffff;
          margin: 0;
          font-size: 28px;
        }
        .content {
          padding: 30px 20px;
          border-left: 1px solid #e0e0e0;
          border-right: 1px solid #e0e0e0;
        }
        .footer {
          background-color: #f5f5f5;
          padding: 15px;
          text-align: center;
          font-size: 12px;
          color: #888;
          border-radius: 0 0 5px 5px;
          border: 1px solid #e0e0e0;
        }
        .button {
          display: inline-block;
          background-color: #22C55E;
          color: white;
          text-decoration: none;
          padding: 12px 25px;
          border-radius: 4px;
          margin: 20px 0;
          font-weight: bold;
        }
        .button:hover {
          background-color: #4ADE80;
        }
        .highlight {
          color: #22C55E;
          font-weight: bold;
        }
        .divider {
          height: 1px;
          background-color: #e0e0e0;
          margin: 25px 0;
        }
        .feature {
          display: flex;
          align-items: center;
          margin-bottom: 15px;
        }
        .feature-icon {
          width: 30px;
          text-align: center;
          margin-right: 10px;
          color: #22C55E;
          font-size: 18px;
        }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>Welcome to Guardia Vision!</h1>
        </div>
        <div class="content">
          <p>Hello ${name},</p>
          
          <p>Thank you for joining Guardia Vision! We're excited to have you on board and can't wait to help you protect your visual content.</p>
          
          <p>With Guardia Vision, you can:</p>
          
          <div class="feature">
            <div class="feature-icon">✓</div>
            <div>Automatically blur sensitive information in images</div>
          </div>
          <div class="feature">
            <div class="feature-icon">✓</div>
            <div>Process videos with advanced AI technology</div>
          </div>
          <div class="feature">
            <div class="feature-icon">✓</div>
            <div>Protect your privacy with state-of-the-art security</div>
          </div>
          
          <div class="divider"></div>
          
          <p>Ready to get started? Click the button below to access your dashboard:</p>
          
          <div style="text-align: center;">
            <a href="${process.env.NEXT_PUBLIC_APP_URL || 'https://guardiavision.com'}/dashboard" class="button">Go to Dashboard</a>
          </div>
          
          <p>If you have any questions or need assistance, please don't hesitate to contact our support team at <a href="mailto:<EMAIL>"><EMAIL></a>.</p>
          
          <p>Best regards,<br>The Guardia Vision Team</p>
        </div>
        <div class="footer">
          <p>© ${new Date().getFullYear()} Guardia Vision. All rights reserved.</p>
          <p>
            <a href="${process.env.NEXT_PUBLIC_APP_URL || 'https://guardiavision.com'}/privacy-policy">Privacy Policy</a> | 
            <a href="${process.env.NEXT_PUBLIC_APP_URL || 'https://guardiavision.com'}/terms-of-service">Terms of Service</a>
          </p>
        </div>
      </div>
    </body>
    </html>
  `;
  
  return sendEmail({ to: email, subject, html });
}

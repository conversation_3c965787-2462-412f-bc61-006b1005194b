import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { ShieldCheck } from '@/components/icons/ShieldCheck';

import { buttonVariants } from '@/components/ui/buttonVariants';
import { CustomCTABanner } from '@/features/landing/CustomCTABanner';
import { Section } from '@/features/landing/Section';

export const CTA = () => {
  const t = useTranslations('CTA');

  return (
    <Section>
      <CustomCTABanner
        title={t('title')}
        description={t('description')}
        buttons={(
          <Link
            className={buttonVariants({ variant: 'default', size: 'lg', className: 'bg-green-400 hover:bg-green-500 text-navy' })}
            href="/sign-up"
          >
            <ShieldCheck className="mr-2 size-5" />
            {t('button_text')}
          </Link>
        )}
      />
    </Section>
  );
};

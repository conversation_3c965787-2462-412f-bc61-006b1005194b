'use client';

import { useState } from 'react';
import { redirect } from 'next/navigation';

// Force dynamic rendering to avoid static generation errors
export const dynamic = 'force-dynamic';

// Check if test pages are disabled
const isTestPagesDisabled = process.env.NEXT_PUBLIC_DISABLE_TEST_PAGES === 'true';

export default function ApiTestPage() {
  // Redirect to home page if test pages are disabled
  if (isTestPagesDisabled) {
    redirect('/');
  }
  const [getResult, setGetResult] = useState<any>(null);
  const [postResult, setPostResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const testGetRequest = async () => {
    setLoading(true);
    setError('');

    try {
      const response = await fetch('http://localhost:3001/api/test-api');
      const data = await response.json();
      setGetResult(data);
    } catch (err: any) {
      setError(`GET Error: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  const testPostRequest = async () => {
    setLoading(true);
    setError('');

    try {
      const response = await fetch('http://localhost:3001/api/test-api', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ test: 'data', timestamp: new Date().toISOString() }),
      });
      const data = await response.json();
      setPostResult(data);
    } catch (err: any) {
      setError(`POST Error: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  // Test the check-email endpoint
  const testCheckEmail = async () => {
    setLoading(true);
    setError('');

    try {
      const response = await fetch('http://localhost:3001/api/check-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email: '<EMAIL>' }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      setPostResult({
        endpoint: 'check-email',
        data
      });
    } catch (err: any) {
      setError(`check-email Error: ${err.message}`);

      // Try the alternative endpoint
      try {
        const altResponse = await fetch('http://localhost:3001/api/check-email-alt', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ email: '<EMAIL>' }),
        });

        if (!altResponse.ok) {
          throw new Error(`HTTP error! status: ${altResponse.status}`);
        }

        const altData = await altResponse.json();
        setPostResult({
          endpoint: 'check-email-alt',
          data: altData
        });
      } catch (altErr: any) {
        setError(`Both endpoints failed: ${err.message}, Alt: ${altErr.message}`);
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-8 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">API Test Page</h1>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      <div className="flex space-x-4 mb-8">
        <button
          onClick={testGetRequest}
          disabled={loading}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:bg-blue-300"
        >
          Test GET
        </button>

        <button
          onClick={testPostRequest}
          disabled={loading}
          className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:bg-green-300"
        >
          Test POST
        </button>

        <button
          onClick={testCheckEmail}
          disabled={loading}
          className="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 disabled:bg-purple-300"
        >
          Test check-email
        </button>
      </div>

      {loading && <div className="text-gray-500">Loading...</div>}

      {getResult && (
        <div className="mb-6">
          <h2 className="text-xl font-semibold mb-2">GET Result:</h2>
          <pre className="bg-gray-100 p-4 rounded overflow-auto">
            {JSON.stringify(getResult, null, 2)}
          </pre>
        </div>
      )}

      {postResult && (
        <div>
          <h2 className="text-xl font-semibold mb-2">POST Result:</h2>
          <pre className="bg-gray-100 p-4 rounded overflow-auto">
            {JSON.stringify(postResult, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
}

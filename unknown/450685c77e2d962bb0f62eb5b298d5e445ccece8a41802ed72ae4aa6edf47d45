import { Check } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/Card"
// These components don't exist in the Card component, so we'll define them here
const CardHeader = ({ children }: { children: React.ReactNode }) => <div className="p-6">{children}</div>
const CardTitle = ({ children, className }: { children: React.ReactNode; className?: string }) => <h3 className={`text-lg font-semibold ${className || ''}`}>{children}</h3>
const CardDescription = ({ children, className }: { children: React.ReactNode; className?: string }) => <p className={`text-sm text-gray-400 ${className || ''}`}>{children}</p>
const CardFooter = ({ children, className }: { children: React.ReactNode; className?: string }) => <div className={`p-6 pt-0 ${className || ''}`}>{children}</div>

export function Pricing() {
  const plans = [
    {
      name: "Free Trial",
      price: "$0",
      description: "Perfect for testing our features",
      features: ["Process up to 50 images", "Basic blur options", "Standard processing speed", "Email support"],
      cta: "Start Free Trial",
      popular: false,
    },
    {
      name: "Pro",
      price: "$29",
      period: "/month",
      description: "For content creators and small teams",
      features: [
        "Process up to 1,000 images/month",
        "Process up to 10 hours of video/month",
        "Advanced blur customization",
        "Faster processing speed",
        "API access with 1,000 calls/month",
        "Priority email support",
      ],
      cta: "Get Started",
      popular: true,
    },
    {
      name: "Enterprise",
      price: "Custom",
      description: "For organizations with advanced needs",
      features: [
        "Unlimited processing",
        "Full customization options",
        "Fastest processing speed",
        "Unlimited API access",
        "Dedicated account manager",
        "Custom integration support",
        "On-premise deployment option",
      ],
      cta: "Contact Sales",
      popular: false,
    },
  ]

  return (
    <section id="pricing" className="py-24">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mx-auto max-w-3xl text-center">
          <h2 className="text-3xl font-bold tracking-tight text-white sm:text-4xl">Simple, Transparent Pricing</h2>
          <p className="mt-4 text-lg text-gray-300">
            Choose the plan that fits your needs. All plans include our core privacy protection features.
          </p>
        </div>

        <div className="mt-16 grid gap-8 lg:grid-cols-3">
          {plans.map((plan, index) => (
            <Card
              key={index}
              className={`relative overflow-hidden border-navy-light bg-navy-dark shadow-lg transition-all duration-200 hover:-translate-y-1 hover:shadow-xl ${
                plan.popular ? "border-t-4 border-t-green-400" : ""
              }`}
            >
              {plan.popular && (
                <div className="absolute right-0 top-0 rounded-bl-lg bg-green-400 px-3 py-1 text-xs font-medium text-navy">
                  Most Popular
                </div>
              )}
              <CardHeader>
                <CardTitle className="text-2xl text-white">{plan.name}</CardTitle>
                <div className="mt-4 flex items-baseline">
                  <span className="text-4xl font-extrabold tracking-tight text-white">{plan.price}</span>
                  {plan.period && <span className="ml-1 text-xl font-medium text-gray-400">{plan.period}</span>}
                </div>
                <CardDescription className="mt-2 text-base text-gray-300">{plan.description}</CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-3">
                  {plan.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-start">
                      <Check className="mr-3 h-5 w-5 flex-shrink-0 text-green-400" />
                      <span className="text-gray-200">{feature}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
              <CardFooter>
                <Button
                  className={`w-full ${
                    plan.popular
                      ? "bg-green-400 text-navy hover:bg-green-500"
                      : "bg-navy-light text-white hover:bg-navy-light/80"
                  }`}
                >
                  {plan.cta}
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>

        <div className="mt-16 rounded-xl bg-navy-light p-8 sm:p-10">
          <div className="text-center">
            <h3 className="text-xl font-semibold text-white">Need a custom plan?</h3>
            <p className="mt-2 text-gray-300">Contact our sales team for volume discounts or custom requirements.</p>
            <Button
              variant="outline"
              className="mt-6 border-green-400 text-green-400 hover:bg-green-400 hover:text-navy"
            >
              Contact Sales
            </Button>
          </div>
        </div>
      </div>
    </section>
  )
}


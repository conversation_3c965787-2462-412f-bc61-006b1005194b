import { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useSignIn } from '@clerk/nextjs';
import { Button } from '@/components/ui/button';

export default function PasswordResetConfirmation() {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);

  const router = useRouter();
  const searchParams = useSearchParams();
  const { isLoaded, signIn } = useSignIn();

  // Get the token from the URL
  const token = searchParams?.get('token');

  useEffect(() => {
    if (!isLoaded) return;

    const verifyToken = async () => {
      try {
        // Check if we have a token
        if (!token) {
          setError('Missing reset token. Please request a new password reset link.');
          setIsLoading(false);
          return;
        }

        // For Clerk's email link verification, we don't need to explicitly verify the token
        // The token is already verified by Clerk when the user clicks the link
        // We can just show the password reset form

        setIsLoading(false);
        setSuccess('Please set a new password to complete the reset process.');
        console.log('Ready for password reset');
      } catch (err: any) {
        console.error('Error:', err);
        setError('An error occurred. Please try again or request a new password reset link.');
        setIsLoading(false);
      }
    };

    verifyToken();
  }, [isLoaded, token]);

  const handleResetPassword = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!isLoaded || !newPassword) {
      setError('Please enter a new password');
      return;
    }

    if (newPassword.length < 8) {
      setError('Password must be at least 8 characters long');
      return;
    }

    try {
      setIsLoading(true);
      setError('');

      // Reset the password
      await signIn.resetPassword({
        password: newPassword,
        signOutOfOtherSessions: true,
      });

      // Store the email in localStorage for the sign-in page
      if (token) {
        localStorage.setItem('passwordResetComplete', 'true');
      }

      // Show success message
      setSuccess('Your password has been successfully reset!');

      // Redirect to sign-in page after a delay
      setTimeout(() => {
        router.push('/en/sign-in');
      }, 3000);
    } catch (err: any) {
      console.error('Error resetting password:', err);
      setError(err.errors?.[0]?.message || 'An error occurred while resetting your password');
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-[300px]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#22C55E]"></div>
      </div>
    );
  }

  return (
    <div className="w-full rounded-lg border border-[#112240] bg-[#0a192f] p-6 shadow-xl">
      <div className="mb-6 text-center">
        <h2 className="text-2xl font-bold text-white">Reset Your Password</h2>
        <p className="text-[#94a3b8]">Create a new secure password</p>
      </div>

      {error && (
        <div className="mb-4 rounded-md bg-red-500/10 p-3 text-sm text-red-500">
          {error}
        </div>
      )}

      {success && (
        <div className="mb-4 rounded-md bg-green-500/10 p-3 text-sm text-green-500">
          {success}
        </div>
      )}

      <form onSubmit={handleResetPassword} className="space-y-4">
        <div className="space-y-2">
          <label htmlFor="new-password" className="block text-sm font-medium text-white">
            New Password
          </label>
          <div className="relative">
            <input
              id="new-password"
              type={showPassword ? 'text' : 'password'}
              value={newPassword}
              onChange={(e) => setNewPassword(e.target.value)}
              className="block w-full rounded-md border border-[#1e3a6f] bg-[#112240] p-2.5 text-white placeholder-[#94a3b8] focus:border-[#22C55E] focus:outline-none focus:ring-1 focus:ring-[#22C55E]"
              placeholder="••••••••"
              required
              minLength={8}
            />
            <button
              type="button"
              className="absolute inset-y-0 right-0 flex items-center pr-3 text-[#94a3b8] hover:text-white"
              onClick={() => setShowPassword(!showPassword)}
            >
              {showPassword ? (
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                  <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd" />
                </svg>
              ) : (
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.78-1.781zm4.261 4.26l1.514 1.515a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z" clipRule="evenodd" />
                  <path d="M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.741L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.065 7 9.542 7 .847 0 1.669-.105 2.454-.303z" />
                </svg>
              )}
            </button>
          </div>
          <p className="text-xs text-[#94a3b8]">Password must be at least 8 characters long</p>
        </div>

        <Button
          type="submit"
          className="w-full rounded-md bg-[#22C55E] px-5 py-2.5 text-center text-sm font-medium text-white hover:bg-[#4ADE80] focus:outline-none focus:ring-4 focus:ring-[#22C55E]/50"
          disabled={isLoading}
        >
          {isLoading ? 'Resetting...' : 'Reset Password'}
        </Button>
      </form>
    </div>
  );
}

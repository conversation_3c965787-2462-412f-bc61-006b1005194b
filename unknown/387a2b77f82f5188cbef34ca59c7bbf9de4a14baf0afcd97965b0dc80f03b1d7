import { Card, CardContent } from "@/components/ui/Card"
// These components don't exist in the Card component, so we'll define them here
const CardHeader = ({ children }: { children: React.ReactNode }) => <div className="p-6">{children}</div>
const CardTitle = ({ children, className }: { children: React.ReactNode; className?: string }) => <h3 className={`text-lg font-semibold ${className || ''}`}>{children}</h3>
const CardDescription = ({ children, className }: { children: React.ReactNode; className?: string }) => <p className={`text-sm text-gray-400 ${className || ''}`}>{children}</p>
import { Shield, Zap, Code, Layers, Settings, Lock } from "lucide-react"

export function Features() {
  const features = [
    {
      icon: <Settings className="h-6 w-6 text-green-400" />,
      title: "Customizable Blur",
      description: "Specify exactly what you want to blur or remove: faces, license plates, text, or entire people.",
    },
    {
      icon: <Zap className="h-6 w-6 text-green-400" />,
      title: "Real-Time Processing",
      description: "Process videos and images in real-time with minimal latency, perfect for live streaming.",
    },
    {
      icon: <Shield className="h-6 w-6 text-green-400" />,
      title: "AI Precision",
      description: "Our advanced AI ensures accurate detection and blurring of sensitive content with 99.8% accuracy.",
    },
    {
      icon: <Code className="h-6 w-6 text-green-400" />,
      title: "API Integration",
      description: "Seamlessly integrate with your existing workflows through our robust REST API and SDKs.",
    },
    {
      icon: <Layers className="h-6 w-6 text-green-400" />,
      title: "Batch Processing",
      description: "Process entire libraries of content with our efficient batch processing capabilities.",
    },
    {
      icon: <Lock className="h-6 w-6 text-green-400" />,
      title: "Privacy Guaranteed",
      description: "Your content never leaves your control. All processing happens securely in your environment.",
    },
  ]

  return (
    <section id="features" className="py-24">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mx-auto max-w-3xl text-center">
          <h2 className="text-3xl font-bold tracking-tight text-white sm:text-4xl">
            Powerful Features for Complete Privacy Control
          </h2>
          <p className="mt-4 text-lg text-gray-300">
            Guardiavision combines cutting-edge AI with intuitive controls to give you complete mastery over your visual
            content's privacy.
          </p>
        </div>

        <div className="mt-16 grid gap-8 sm:grid-cols-2 lg:grid-cols-3">
          {features.map((feature, index) => (
            <Card
              key={index}
              className="border-navy-light bg-navy-dark shadow-md transition-all duration-200 hover:-translate-y-1 hover:shadow-lg"
            >
              <CardHeader>
                <div className="mb-4 inline-flex h-12 w-12 items-center justify-center rounded-lg bg-navy">
                  {feature.icon}
                </div>
                <CardTitle className="text-xl text-white">{feature.title}</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-base text-gray-300">{feature.description}</CardDescription>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  )
}


-- Drop existing tables if they exist (for a clean reset)
DROP TABLE IF EXISTS public.credit_transactions;
DROP TABLE IF EXISTS public.processed_images;
DROP TABLE IF EXISTS public.processed_videos;
DROP TABLE IF EXISTS public.tasks;
DROP TABLE IF EXISTS public.test_items;
DROP TABLE IF EXISTS public.users;

-- Function to get the Clerk user ID from the JWT token
CREATE OR REPLACE FUNCTION public.get_clerk_user_id()
RETURNS TEXT
LANGUAGE sql
SECURITY DEFINER
SET search_path = public
AS $$
  SELECT coalesce(auth.jwt() ->> 'sub', '')
$$;

-- Function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION public.update_updated_at()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$;

-- Create users table
CREATE TABLE IF NOT EXISTS public.users (
  id TEXT PRIMARY KEY, -- This will be the Clerk user ID
  email TEXT NOT NULL,
  username TEXT,
  first_name TEXT,
  last_name TEXT,
  avatar_url TEXT,
  credits INTEGER DEFAULT 50, -- Start with 50 free credits
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create a table to track credit usage
CREATE TABLE IF NOT EXISTS public.credit_transactions (
  id SERIAL PRIMARY KEY,
  user_id TEXT REFERENCES public.users(id),
  amount INTEGER NOT NULL, -- Positive for additions, negative for usage
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  transaction_type TEXT NOT NULL -- 'purchase', 'usage', 'refund', 'bonus', etc.
);

-- Create a table to track processed images
CREATE TABLE IF NOT EXISTS public.processed_images (
  id SERIAL PRIMARY KEY,
  user_id TEXT REFERENCES public.users(id),
  original_url TEXT,
  processed_url TEXT,
  status TEXT NOT NULL, -- 'pending', 'processing', 'completed', 'failed'
  credits_used INTEGER NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  metadata JSONB -- Store additional metadata about the processing
);

-- Create a table to track processed videos
CREATE TABLE IF NOT EXISTS public.processed_videos (
  id SERIAL PRIMARY KEY,
  user_id TEXT REFERENCES public.users(id),
  original_url TEXT,
  processed_url TEXT,
  status TEXT NOT NULL, -- 'pending', 'processing', 'completed', 'failed'
  credits_used INTEGER NOT NULL,
  duration_seconds INTEGER,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  metadata JSONB -- Store additional metadata about the processing
);

-- Create a test tasks table for Clerk integration testing
CREATE TABLE IF NOT EXISTS public.tasks (
  id SERIAL PRIMARY KEY,
  name TEXT NOT NULL,
  user_id TEXT NOT NULL DEFAULT get_clerk_user_id(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create test_items table for testing the integration (legacy)
CREATE TABLE IF NOT EXISTS public.test_items (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  user_id TEXT NOT NULL REFERENCES users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable Row Level Security on all tables
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.credit_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.processed_images ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.processed_videos ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tasks ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.test_items ENABLE ROW LEVEL SECURITY;

-- Create triggers to update timestamps
DROP TRIGGER IF EXISTS update_users_updated_at ON public.users;
CREATE TRIGGER update_users_updated_at
BEFORE UPDATE ON public.users
FOR EACH ROW
EXECUTE FUNCTION update_updated_at();

DROP TRIGGER IF EXISTS update_processed_images_updated_at ON public.processed_images;
CREATE TRIGGER update_processed_images_updated_at
BEFORE UPDATE ON public.processed_images
FOR EACH ROW
EXECUTE FUNCTION update_updated_at();

DROP TRIGGER IF EXISTS update_processed_videos_updated_at ON public.processed_videos;
CREATE TRIGGER update_processed_videos_updated_at
BEFORE UPDATE ON public.processed_videos
FOR EACH ROW
EXECUTE FUNCTION update_updated_at();

DROP TRIGGER IF EXISTS update_test_items_updated_at ON public.test_items;
CREATE TRIGGER update_test_items_updated_at
BEFORE UPDATE ON public.test_items
FOR EACH ROW
EXECUTE FUNCTION update_updated_at();

-- Create policies for users table
DROP POLICY IF EXISTS "Users can view their own data" ON public.users;
CREATE POLICY "Users can view their own data"
  ON public.users
  FOR SELECT
  TO authenticated
  USING (id = get_clerk_user_id());

DROP POLICY IF EXISTS "Users can update their own data" ON public.users;
CREATE POLICY "Users can update their own data"
  ON public.users
  FOR UPDATE
  TO authenticated
  USING (id = get_clerk_user_id());

-- Create policies for credit_transactions table
DROP POLICY IF EXISTS "Users can view their own credit transactions" ON public.credit_transactions;
CREATE POLICY "Users can view their own credit transactions"
  ON public.credit_transactions
  FOR SELECT
  TO authenticated
  USING (user_id = get_clerk_user_id());

-- Create policies for processed_images table
DROP POLICY IF EXISTS "Users can view their own processed images" ON public.processed_images;
CREATE POLICY "Users can view their own processed images"
  ON public.processed_images
  FOR SELECT
  TO authenticated
  USING (user_id = get_clerk_user_id());

DROP POLICY IF EXISTS "Users can insert their own processed images" ON public.processed_images;
CREATE POLICY "Users can insert their own processed images"
  ON public.processed_images
  FOR INSERT
  TO authenticated
  WITH CHECK (user_id = get_clerk_user_id());

-- Create policies for processed_videos table
DROP POLICY IF EXISTS "Users can view their own processed videos" ON public.processed_videos;
CREATE POLICY "Users can view their own processed videos"
  ON public.processed_videos
  FOR SELECT
  TO authenticated
  USING (user_id = get_clerk_user_id());

DROP POLICY IF EXISTS "Users can insert their own processed videos" ON public.processed_videos;
CREATE POLICY "Users can insert their own processed videos"
  ON public.processed_videos
  FOR INSERT
  TO authenticated
  WITH CHECK (user_id = get_clerk_user_id());

-- Create policies for tasks table
DROP POLICY IF EXISTS "User can view their own tasks" ON public.tasks;
CREATE POLICY "User can view their own tasks"
  ON public.tasks
  FOR SELECT
  TO authenticated
  USING (user_id = get_clerk_user_id());

DROP POLICY IF EXISTS "Users must insert their own tasks" ON public.tasks;
CREATE POLICY "Users must insert their own tasks"
  ON public.tasks
  FOR INSERT
  TO authenticated
  WITH CHECK (user_id = get_clerk_user_id());

-- Create policies for test_items table
DROP POLICY IF EXISTS "Users can view their own test items" ON public.test_items;
CREATE POLICY "Users can view their own test items"
  ON public.test_items
  FOR SELECT
  TO authenticated
  USING (user_id = get_clerk_user_id());

DROP POLICY IF EXISTS "Users can insert their own test items" ON public.test_items;
CREATE POLICY "Users can insert their own test items"
  ON public.test_items
  FOR INSERT
  TO authenticated
  WITH CHECK (user_id = get_clerk_user_id());

DROP POLICY IF EXISTS "Users can update their own test items" ON public.test_items;
CREATE POLICY "Users can update their own test items"
  ON public.test_items
  FOR UPDATE
  TO authenticated
  USING (user_id = get_clerk_user_id());

DROP POLICY IF EXISTS "Users can delete their own test items" ON public.test_items;
CREATE POLICY "Users can delete their own test items"
  ON public.test_items
  FOR DELETE
  TO authenticated
  USING (user_id = get_clerk_user_id());

-- Allow service role to bypass RLS
ALTER TABLE public.users FORCE ROW LEVEL SECURITY;
ALTER TABLE public.credit_transactions FORCE ROW LEVEL SECURITY;
ALTER TABLE public.processed_images FORCE ROW LEVEL SECURITY;
ALTER TABLE public.processed_videos FORCE ROW LEVEL SECURITY;
ALTER TABLE public.tasks FORCE ROW LEVEL SECURITY;
ALTER TABLE public.test_items FORCE ROW LEVEL SECURITY;

-- Create policy for service role to access all tables
DROP POLICY IF EXISTS "Service role can do anything" ON public.users;
CREATE POLICY "Service role can do anything"
  ON public.users
  FOR ALL
  TO service_role
  USING (true)
  WITH CHECK (true);

DROP POLICY IF EXISTS "Service role can do anything" ON public.credit_transactions;
CREATE POLICY "Service role can do anything"
  ON public.credit_transactions
  FOR ALL
  TO service_role
  USING (true)
  WITH CHECK (true);

DROP POLICY IF EXISTS "Service role can do anything" ON public.processed_images;
CREATE POLICY "Service role can do anything"
  ON public.processed_images
  FOR ALL
  TO service_role
  USING (true)
  WITH CHECK (true);

DROP POLICY IF EXISTS "Service role can do anything" ON public.processed_videos;
CREATE POLICY "Service role can do anything"
  ON public.processed_videos
  FOR ALL
  TO service_role
  USING (true)
  WITH CHECK (true);

DROP POLICY IF EXISTS "Service role can do anything" ON public.tasks;
CREATE POLICY "Service role can do anything"
  ON public.tasks
  FOR ALL
  TO service_role
  USING (true)
  WITH CHECK (true);

DROP POLICY IF EXISTS "Service role can do anything" ON public.test_items;
CREATE POLICY "Service role can do anything"
  ON public.test_items
  FOR ALL
  TO service_role
  USING (true)
  WITH CHECK (true);

import { NextResponse } from 'next/server';
import { clerkClient } from '@clerk/nextjs/server';

export async function GET() {
  try {
    // Get environment variables
    const envVars = {
      NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY: process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY?.substring(0, 10) + '...',
      CLERK_SECRET_KEY: process.env.CLERK_SECRET_KEY ? process.env.CLERK_SECRET_KEY.substring(0, 10) + '...' : 'missing',
      CLERK_ENCRYPTION_KEY: process.env.CLERK_ENCRYPTION_KEY ? 'present' : 'missing',
      NODE_ENV: process.env.NODE_ENV,
    };

    // Test creating a user
    try {
      const testEmail = `test-${Date.now()}@example.com`;
      const testPassword = 'Password123!';
      
      const user = await clerkClient.users.createUser({
        emailAddress: [testEmail],
        password: testPassword,
        firstName: 'Test',
        lastName: 'User',
      });
      
      return NextResponse.json({
        success: true,
        message: 'Test user creation successful',
        userId: user.id,
        envVars,
      });
    } catch (createError: any) {
      return NextResponse.json({
        success: false,
        message: 'Test user creation failed',
        error: createError.message || 'Unknown error',
        errorDetails: createError.errors || [],
        envVars,
      }, { status: 500 });
    }
  } catch (error: any) {
    console.error('Clerk API test failed:', error);

    return NextResponse.json({
      success: false,
      error: error.message || 'Unknown error',
      stack: error.stack,
      envVars: {
        NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY: process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY?.substring(0, 10) + '...',
        CLERK_SECRET_KEY: process.env.CLERK_SECRET_KEY ? 'present' : 'missing',
        CLERK_ENCRYPTION_KEY: process.env.CLERK_ENCRYPTION_KEY ? 'present' : 'missing',
        NODE_ENV: process.env.NODE_ENV,
      }
    }, { status: 500 });
  }
}

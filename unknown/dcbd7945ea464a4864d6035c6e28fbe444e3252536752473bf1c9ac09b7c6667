import { NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

export async function GET() {
  try {
    // Get environment variables
    const envVars = {
      NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY: process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY?.substring(0, 10) + '...',
      CLERK_SECRET_KEY: process.env.CLERK_SECRET_KEY ? process.env.CLERK_SECRET_KEY.substring(0, 10) + '...' : 'missing',
      CLERK_WEBHOOK_SECRET: process.env.CLERK_WEBHOOK_SECRET ? process.env.CLERK_WEBHOOK_SECRET.substring(0, 10) + '...' : 'missing',
      CLERK_ENCRYPTION_KEY: process.env.CLERK_ENCRYPTION_KEY ? 'present' : 'missing',
      NODE_ENV: process.env.NODE_ENV,
    };

    // Read environment files
    const envFiles = {
      '.env.local': readEnvFile('.env.local'),
      '.env.development': readEnvFile('.env.development'),
      '.env': readEnvFile('.env'),
    };

    return NextResponse.json({
      success: true,
      message: 'Environment variables check',
      envVars,
      envFiles,
    });
  } catch (error: any) {
    console.error('Environment check failed:', error);

    return NextResponse.json({
      success: false,
      error: error.message || 'Unknown error',
      stack: error.stack,
    }, { status: 500 });
  }
}

function readEnvFile(fileName: string) {
  try {
    const filePath = path.join(process.cwd(), fileName);
    if (fs.existsSync(filePath)) {
      const content = fs.readFileSync(filePath, 'utf8');
      const envVars: Record<string, string> = {};

      content.split('\n').forEach(line => {
        if (line.trim() && !line.startsWith('#')) {
          const match = line.match(/^([^=]+)=(.*)$/);
          if (match && match[1] && match[2]) {
            const key = match[1].trim();
            const value = match[2].trim();

            if (key.includes('CLERK') && key !== 'CLERK_ENCRYPTION_KEY') {
              envVars[key] = value.substring(0, 10) + '...';
            } else if (key === 'CLERK_ENCRYPTION_KEY') {
              envVars[key] = 'present';
            } else {
              envVars[key] = value;
            }
          }
        }
      });

      return envVars;
    }
    return { error: 'File not found' };
  } catch (error: any) {
    return { error: error.message };
  }
}

import { NextResponse } from 'next/server';
import { clerkClient } from '@clerk/nextjs/server';

export async function GET() {
  try {
    // Try to get a list of users to test if the Clerk API is working
    const users = await clerkClient.users.getUserList({
      limit: 1,
    });

    return NextResponse.json({
      success: true,
      message: 'Clerk API is working correctly',
      userCount: users.data.length,
      apiKeyInfo: {
        publishableKeyPrefix: process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY?.substring(0, 10) + '...',
        publishableKeyLength: process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY?.length,
        secretKeyPrefix: process.env.CLERK_SECRET_KEY?.substring(0, 10) + '...',
        secretKeyLength: process.env.CLERK_SECRET_KEY?.length,
      }
    });
  } catch (error: any) {
    console.error('Clerk API test failed:', error);

    return NextResponse.json({
      success: false,
      error: error.message || 'Unknown error',
      stack: error.stack,
      apiKeyInfo: {
        publishableKeyPrefix: process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY?.substring(0, 10) + '...',
        publishableKeyLength: process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY?.length,
        secretKeyPrefix: process.env.CLERK_SECRET_KEY?.substring(0, 10) + '...',
        secretKeyLength: process.env.CLERK_SECRET_KEY?.length,
      }
    }, { status: 500 });
  }
}

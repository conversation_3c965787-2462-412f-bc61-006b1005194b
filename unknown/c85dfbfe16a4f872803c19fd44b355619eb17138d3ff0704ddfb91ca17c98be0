import { auth } from '@clerk/nextjs/server';
import { redirect } from 'next/navigation';

export default function CenteredLayout(props: { children: React.ReactNode }) {
  const { userId } = auth();

  if (userId) {
    redirect('/dashboard');
  }

  return (
    <div className="relative flex min-h-screen items-center justify-center px-4 py-12">
      {/* Background pattern */}
      <div className="absolute inset-0 z-0 overflow-hidden">
        <div className="absolute inset-0 bg-[#0a192f]"></div>
        <div className="absolute inset-0 opacity-5" style={{
          backgroundImage: 'radial-gradient(#22C55E 1px, transparent 1px), radial-gradient(#3b82f6 1px, transparent 1px)',
          backgroundSize: '40px 40px',
          backgroundPosition: '0 0, 20px 20px'
        }}></div>
        <div className="absolute left-0 right-0 top-0 h-px bg-gradient-to-r from-transparent via-[#22C55E]/20 to-transparent"></div>
        <div className="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-[#3b82f6]/20 to-transparent"></div>
      </div>

      {/* Content */}
      <div className="relative z-10 w-full max-w-md">
        {props.children}
      </div>
    </div>
  );
}

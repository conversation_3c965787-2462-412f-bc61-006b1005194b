'use client';

import { useState } from 'react';
import { useSignUp } from '@clerk/nextjs';
import { useRouter } from 'next/navigation';

export default function SimpleSignUp() {
  const { isLoaded, signUp } = useSignUp();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [verifying, setVerifying] = useState(false);
  const [code, setCode] = useState('');
  const router = useRouter();

  // Check if email already exists
  const checkEmailExists = async (email: string): Promise<boolean> => {
    try {
      const response = await fetch('/api/check-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      if (!response.ok) {
        // If the API call fails, we'll proceed with normal signup
        console.warn('Failed to check email existence, proceeding with normal signup');
        return false;
      }

      const data = await response.json();
      return data.exists === true;
    } catch (err) {
      console.error('Error checking email existence:', err);
      // If there's an error, we'll proceed with normal signup
      return false;
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!isLoaded) {
      setError('Clerk is not loaded yet');
      return;
    }

    try {
      setIsLoading(true);
      setError('');

      // First check if the email already exists
      const emailExists = await checkEmailExists(email);
      if (emailExists) {
        setError('This email address is already registered. Please try signing in instead.');
        setIsLoading(false);
        return;
      }

      // Log the attempt
      console.log('Attempting to sign up with:', { email, password, firstName, lastName });

      // Create the user
      const result = await signUp.create({
        emailAddress: email,
        password,
        firstName,
        lastName,
      });

      console.log('Sign-up creation result:', result);

      // Prepare verification
      await signUp.prepareEmailAddressVerification({ strategy: 'email_code' });

      // Switch to verification mode
      setVerifying(true);
    } catch (err: any) {
      console.error('Sign-up error:', err);
      console.log('Error details:', JSON.stringify(err, null, 2));

      if (err.errors && Array.isArray(err.errors)) {
        setError(err.errors[0]?.message || 'An error occurred');
      } else if (err.message) {
        setError(err.message);
      } else {
        setError('An unknown error occurred');
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleVerification = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!isLoaded) {
      setError('Clerk is not loaded yet');
      return;
    }

    try {
      setIsLoading(true);
      setError('');

      // Verify the code
      const result = await signUp.attemptEmailAddressVerification({
        code,
      });

      console.log('Verification result:', result);

      if (result.status === 'complete') {
        router.push('/dashboard');
      } else if (result.status === 'missing_requirements') {
        // Handle missing username requirement
        console.log('Missing requirements detected:', result);

        try {
          // Handle missing requirements without username
          console.log('Missing requirements detected, but we are not using usernames');
          setError('Account creation requires additional information. Please contact support.');

          // Try verification again
          const verifyResult = await signUp.attemptEmailAddressVerification({
            code,
          });

          console.log('Second verification attempt result:', verifyResult);

          if (verifyResult.status === 'complete') {
            router.push('/dashboard');
          } else {
            setError(`Verification still incomplete. Status: ${verifyResult.status}`);
          }
        } catch (updateErr: any) {
          console.error('Error updating user requirements:', updateErr);
          setError(updateErr.errors?.[0]?.message || 'Failed to complete signup requirements');
        }
      } else {
        setError(`Verification not complete. Status: ${result.status}`);
      }
    } catch (err: any) {
      console.error('Verification error:', err);

      if (err.errors && Array.isArray(err.errors)) {
        setError(err.errors[0]?.message || 'Verification failed');
      } else if (err.message) {
        setError(err.message);
      } else {
        setError('An unknown verification error occurred');
      }
    } finally {
      setIsLoading(false);
    }
  };

  if (!isLoaded) {
    return <div>Loading...</div>;
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-900 text-white p-4">
      <div className="w-full max-w-md p-8 space-y-8 bg-gray-800 rounded-lg shadow-lg">
        <div className="text-center">
          <h1 className="text-2xl font-bold">Simple Sign Up</h1>
          <p className="text-gray-400">Testing Clerk sign-up functionality</p>
        </div>

        {error && (
          <div className="bg-red-500/20 border border-red-500 p-4 rounded-md text-red-300">
            {error}
          </div>
        )}

        {!verifying ? (
          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-400">First Name</label>
              <input
                type="text"
                value={firstName}
                onChange={(e) => setFirstName(e.target.value)}
                className="mt-1 block w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-400">Last Name</label>
              <input
                type="text"
                value={lastName}
                onChange={(e) => setLastName(e.target.value)}
                className="mt-1 block w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white"
                required
              />
            </div>



            <div>
              <label className="block text-sm font-medium text-gray-400">Email</label>
              <input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="mt-1 block w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-400">Password</label>
              <input
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="mt-1 block w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white"
                required
              />
              <p className="mt-1 text-xs text-gray-400">Password must be at least 8 characters</p>
            </div>

            <button
              type="submit"
              disabled={isLoading}
              className="w-full py-2 px-4 bg-blue-600 hover:bg-blue-700 rounded-md font-medium text-white"
            >
              {isLoading ? 'Signing up...' : 'Sign Up'}
            </button>
          </form>
        ) : (
          <form onSubmit={handleVerification} className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-400">Verification Code</label>
              <input
                type="text"
                value={code}
                onChange={(e) => setCode(e.target.value)}
                className="mt-1 block w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white"
                required
              />
              <p className="mt-1 text-xs text-gray-400">Enter the code sent to your email</p>
            </div>

            <button
              type="submit"
              disabled={isLoading}
              className="w-full py-2 px-4 bg-blue-600 hover:bg-blue-700 rounded-md font-medium text-white"
            >
              {isLoading ? 'Verifying...' : 'Verify Email'}
            </button>
          </form>
        )}

        <div className="text-center text-sm text-gray-400">
          <p>This is a simplified sign-up form for testing purposes.</p>
        </div>
      </div>
    </div>
  );
}

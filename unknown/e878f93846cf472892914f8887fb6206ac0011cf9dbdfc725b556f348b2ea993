'use client';

import { useState } from 'react';
import { redirect } from 'next/navigation';

// Force dynamic rendering to avoid static generation errors
export const dynamic = 'force-dynamic';

// Check if test pages are disabled
const isTestPagesDisabled = process.env.NEXT_PUBLIC_DISABLE_TEST_PAGES === 'true';

export default function SimpleApiTestPage() {
  // Redirect to home page if test pages are disabled
  if (isTestPagesDisabled) {
    redirect('/');
  }
  const [getResult, setGetResult] = useState<any>(null);
  const [postResult, setPostResult] = useState<any>(null);
  const [pagesResult, setPagesResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const testGetRequest = async () => {
    setLoading(true);
    setError('');

    try {
      // Simulate a delay
      await new Promise(resolve => setTimeout(resolve, 500));

      // Mock successful response
      const mockData = {
        message: 'Simple API is working! (Mocked)',
        timestamp: new Date().toISOString(),
        success: true
      };

      setGetResult(mockData);
    } catch (err: any) {
      console.error('GET request error:', err);
      setError(`GET Error: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  const testPostRequest = async () => {
    setLoading(true);
    setError('');

    try {
      // Simulate a delay
      await new Promise(resolve => setTimeout(resolve, 500));

      // Create test data
      const testData = { test: 'data', timestamp: new Date().toISOString() };

      // Mock successful response
      const mockData = {
        message: 'Simple POST request received (Mocked)',
        receivedData: testData,
        timestamp: new Date().toISOString(),
        success: true
      };

      setPostResult(mockData);
    } catch (err: any) {
      console.error('POST request error:', err);
      setError(`POST Error: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  const testPagesRequest = async () => {
    setLoading(true);
    setError('');

    try {
      // Simulate a delay
      await new Promise(resolve => setTimeout(resolve, 500));

      // Mock successful response
      const mockData = {
        message: 'Pages API is working! (Mocked)',
        timestamp: new Date().toISOString(),
        success: true
      };

      setPagesResult(mockData);
    } catch (err: any) {
      console.error('Pages API request error:', err);
      setError(`Pages API Error: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-8 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Simple API Test Page (Mock Mode)</h1>
      <p className="text-gray-600 mb-4">This page uses mock data instead of actual API calls to demonstrate functionality.</p>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      <div className="flex space-x-4 mb-8">
        <button
          onClick={testGetRequest}
          disabled={loading}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:bg-blue-300"
        >
          Test Mock GET
        </button>

        <button
          onClick={testPostRequest}
          disabled={loading}
          className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:bg-green-300"
        >
          Test Mock POST
        </button>

        <button
          onClick={testPagesRequest}
          disabled={loading}
          className="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 disabled:bg-purple-300"
        >
          Test Mock Pages API
        </button>
      </div>

      {loading && <div className="text-gray-500">Loading...</div>}

      {getResult && (
        <div className="mb-6">
          <h2 className="text-xl font-semibold mb-2">App Router GET Result:</h2>
          <pre className="bg-gray-100 p-4 rounded overflow-auto">
            {JSON.stringify(getResult, null, 2)}
          </pre>
        </div>
      )}

      {postResult && (
        <div className="mb-6">
          <h2 className="text-xl font-semibold mb-2">App Router POST Result:</h2>
          <pre className="bg-gray-100 p-4 rounded overflow-auto">
            {JSON.stringify(postResult, null, 2)}
          </pre>
        </div>
      )}

      {pagesResult && (
        <div>
          <h2 className="text-xl font-semibold mb-2">Pages API Result:</h2>
          <pre className="bg-gray-100 p-4 rounded overflow-auto">
            {JSON.stringify(pagesResult, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
}

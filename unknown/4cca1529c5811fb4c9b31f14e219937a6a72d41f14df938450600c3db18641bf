"use client"

import { useEffect, useState } from 'react'
import Image from 'next/image'
import { motion, AnimatePresence } from 'framer-motion'

const testimonials = [
  {
    id: 1,
    content: "Blur<PERSON><PERSON> has been a game-changer for our content moderation team. What used to take hours now takes minutes.",
    author: "<PERSON>",
    role: "Content Manager at MediaCorp",
    avatar: "/images/testimonial-1.png"
  },
  {
    id: 2,
    content: "The accuracy of BlurMe's AI is impressive. It detects and blurs faces with incredible precision, saving us countless hours of manual work.",
    author: "<PERSON>",
    role: "Video Producer",
    avatar: "/images/testimonial-2.png"
  },
  {
    id: 3,
    content: "We've reduced our video editing time by 95% since implementing BlurMe. The ROI has been incredible.",
    author: "<PERSON>",
    role: "Director of Operations",
    avatar: "/images/testimonial-3.png"
  }
]

export function Testimonials() {
  const [currentIndex, setCurrentIndex] = useState(0)

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) => (prevIndex + 1) % testimonials.length)
    }, 5000)

    return () => clearInterval(interval)
  }, [])

  return (
    <div className="py-16 bg-navy-dark/50 rounded-2xl">
      <div className="max-w-4xl mx-auto px-6">
        <h2 className="text-3xl font-bold text-center text-white mb-12">
          What our customers are saying
        </h2>

        <div className="relative h-80">
          <AnimatePresence mode="wait">
            <motion.div
              key={currentIndex}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.5 }}
              className="absolute inset-0"
            >
              <div className="flex flex-col items-center text-center">
                <div className="relative mb-8">
                  <div className="absolute -inset-1 rounded-full bg-gradient-to-r from-blue-400 to-green-400 opacity-70 blur-md"></div>
                  <div className="relative rounded-full overflow-hidden h-20 w-20 border-2 border-navy">
                    {testimonials[currentIndex] && (
                      <Image
                        src={testimonials[currentIndex].avatar}
                        alt={testimonials[currentIndex].author}
                        width={80}
                        height={80}
                        className="object-cover"
                      />
                    )}
                  </div>
                </div>

                {testimonials[currentIndex] && (
                  <>
                    <blockquote className="text-xl text-gray-200 italic mb-6">
                      "{testimonials[currentIndex].content}"
                    </blockquote>

                    <div>
                      <p className="font-semibold text-white">{testimonials[currentIndex].author}</p>
                      <p className="text-gray-400 text-sm">{testimonials[currentIndex].role}</p>
                    </div>
                  </>
                )}
              </div>
            </motion.div>
          </AnimatePresence>
        </div>

        <div className="flex justify-center space-x-2 mt-8">
          {testimonials.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentIndex(index)}
              className={`h-2 rounded-full transition-all duration-300 ${
                index === currentIndex ? 'w-8 bg-blue-400' : 'w-2 bg-gray-600'
              }`}
              aria-label={`Go to testimonial ${index + 1}`}
            />
          ))}
        </div>
      </div>
    </div>
  )
}

// This file contains configuration for Clerk authentication

// Check if Clerk API keys are properly set
export function validateClerkConfig() {
  const publishableKey = process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY;
  const secretKey = process.env.CLERK_SECRET_KEY;
  
  const issues = [];
  
  if (!publishableKey) {
    issues.push('NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY is missing');
  } else if (!publishableKey.startsWith('pk_')) {
    issues.push('NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY should start with pk_');
  } else if (publishableKey.length < 20) {
    issues.push('NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY appears to be truncated');
  }
  
  if (!secretKey) {
    issues.push('CLERK_SECRET_KEY is missing');
  } else if (!secretKey.startsWith('sk_')) {
    issues.push('CLERK_SECRET_KEY should start with sk_');
  } else if (secretKey.length < 20) {
    issues.push('CLERK_SECRET_KEY appears to be truncated');
  }
  
  return {
    isValid: issues.length === 0,
    issues,
    publishableKeyInfo: publishableKey ? {
      prefix: publishableKey.substring(0, 5) + '...',
      length: publishableKey.length,
      lastChar: publishableKey.charAt(publishableKey.length - 1)
    } : null,
    secretKeyInfo: secretKey ? {
      prefix: secretKey.substring(0, 5) + '...',
      length: secretKey.length,
      lastChar: secretKey.charAt(secretKey.length - 1)
    } : null
  };
}

// Clerk appearance configuration
export const clerkAppearance = {
  elements: {
    formButtonPrimary: 'bg-[#22C55E] hover:bg-[#4ADE80] text-white',
    card: 'bg-[#0a192f] border border-[#112240] shadow-xl',
    headerTitle: 'text-white',
    headerSubtitle: 'text-[#94a3b8]',
    socialButtonsIconButton: 'border border-[#1e3a6f] bg-[#112240] text-white hover:bg-[#1e3a6f]',
    formFieldLabel: 'text-white',
    formFieldInput: 'bg-[#112240] border-[#1e3a6f] text-white',
    footerActionLink: 'text-[#22C55E] hover:text-[#4ADE80]',
    identityPreview: 'bg-[#112240] border-[#1e3a6f]',
  },
};

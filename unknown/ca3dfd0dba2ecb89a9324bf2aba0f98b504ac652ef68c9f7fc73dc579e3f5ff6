<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Verification Fix Tool</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        h1 {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        .card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"],
        input[type="email"],
        input[type="password"],
        textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-bottom: 15px;
            font-size: 16px;
        }
        button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #2980b9;
        }
        button:disabled {
            background-color: #95a5a6;
            cursor: not-allowed;
        }
        .button-danger {
            background-color: #e74c3c;
        }
        .button-danger:hover {
            background-color: #c0392b;
        }
        .button-success {
            background-color: #2ecc71;
        }
        .button-success:hover {
            background-color: #27ae60;
        }
        .error {
            background-color: #ffecec;
            color: #e74c3c;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 15px;
            border-left: 4px solid #e74c3c;
        }
        .success {
            background-color: #e7f9ef;
            color: #27ae60;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 15px;
            border-left: 4px solid #27ae60;
        }
        .info {
            background-color: #e7f3fe;
            color: #3498db;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 15px;
            border-left: 4px solid #3498db;
        }
        .result {
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 4px;
            margin-top: 20px;
            white-space: pre-wrap;
            overflow-x: auto;
            border: 1px solid #ddd;
        }
        .hidden {
            display: none;
        }
        .button-group {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
        .tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 1px solid #ddd;
        }
        .tab {
            padding: 10px 15px;
            cursor: pointer;
            border: 1px solid transparent;
            border-bottom: none;
            margin-right: 5px;
            border-radius: 4px 4px 0 0;
        }
        .tab.active {
            background-color: white;
            border-color: #ddd;
            border-bottom-color: white;
            margin-bottom: -1px;
        }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 1s ease-in-out infinite;
            margin-right: 10px;
            vertical-align: middle;
        }
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <h1>Email Verification Fix Tool</h1>

    <div class="info">
        <p><strong>How to use this tool:</strong></p>
        <ol>
            <li>Enter your Clerk API key (starts with <code>sk_test_</code> or <code>sk_live_</code>)</li>
            <li>Enter the email address that's having the "already verified" issue</li>
            <li>Click "Check Email Status" to see if the email exists in Clerk's system</li>
            <li>If a user is found, you can delete it</li>
            <li>If no user is found but you're still getting "already verified" errors, try the "Advanced Fix" tab</li>
        </ol>
    </div>

    <div class="card">
        <label for="apiKey">Clerk Secret API Key:</label>
        <input type="password" id="apiKey" placeholder="sk_test_..." />
        <p class="info">You can find this in your Clerk dashboard under API Keys. It starts with sk_test_ or sk_live_</p>
    </div>

    <div class="tabs">
        <div class="tab active" data-tab="check">Check Email</div>
        <div class="tab" data-tab="advanced">Advanced Fix</div>
        <div class="tab" data-tab="cors">CORS Workaround</div>
        <div class="tab" data-tab="help">Help</div>
    </div>

    <div id="check-tab" class="tab-content active">
        <div class="card">
            <h2>Check Email Status</h2>
            <label for="email">Email Address:</label>
            <input type="email" id="email" placeholder="<EMAIL>" />

            <button id="checkEmail" class="button-primary">Check Email Status</button>

            <div id="emailResult" class="result hidden"></div>

            <div id="userActions" class="hidden">
                <h3>User Found</h3>
                <p>This email is associated with a user account in Clerk. You can delete this account to resolve the "already verified" issue.</p>
                <button id="deleteUser" class="button-danger">Delete User Account</button>
            </div>
        </div>
    </div>

    <div id="advanced-tab" class="tab-content">
        <div class="card">
            <h2>Advanced Fix</h2>
            <p class="info">Use this if the email doesn't exist in Clerk's system but you're still getting "already verified" errors.</p>

            <label for="advancedEmail">Email Address:</label>
            <input type="email" id="advancedEmail" placeholder="<EMAIL>" />

            <label for="password">New Password:</label>
            <input type="password" id="password" placeholder="Enter a password" />

            <button id="fixVerified" class="button-success">Attempt Advanced Fix</button>

            <div id="advancedResult" class="result hidden"></div>
        </div>
    </div>

    <div id="cors-tab" class="tab-content">
        <div class="card">
            <h2>CORS Workaround</h2>
            <p class="info">If you're experiencing CORS errors when trying to access Clerk's API directly, you can use this workaround.</p>

            <h3>Option 1: Use Clerk's Dashboard</h3>
            <p>The most reliable way to fix "already verified" issues is to use Clerk's dashboard directly:</p>
            <ol>
                <li>Go to <a href="https://dashboard.clerk.com" target="_blank">dashboard.clerk.com</a> and log in</li>
                <li>Select your application</li>
                <li>Go to "Users" in the sidebar</li>
                <li>Search for the email address that's having issues</li>
                <li>If a user is found, click on it and then click "Delete user" at the bottom</li>
            </ol>

            <h3>Option 2: Use Clerk's API Explorer</h3>
            <p>Clerk provides an API Explorer that lets you make API calls directly from their dashboard:</p>
            <ol>
                <li>Go to <a href="https://dashboard.clerk.com" target="_blank">dashboard.clerk.com</a> and log in</li>
                <li>Select your application</li>
                <li>Go to "API Explorer" in the sidebar</li>
                <li>Select "Users" from the dropdown</li>
                <li>To find a user by email, use: <code>GET /users?email_address=<EMAIL></code></li>
                <li>To delete a user, use: <code>DELETE /users/{user_id}</code></li>
            </ol>

            <h3>Option 3: Use a CORS Proxy</h3>
            <p>You can use a CORS proxy service to bypass CORS restrictions. Enter your request details below:</p>

            <div class="mb-4">
                <label for="corsEmail">Email Address:</label>
                <input type="email" id="corsEmail" placeholder="<EMAIL>" />
            </div>

            <div class="mb-4">
                <label for="corsApiKey">Clerk API Key:</label>
                <input type="password" id="corsApiKey" placeholder="sk_test_..." />
            </div>

            <div class="button-group">
                <button id="corsCheckEmail" class="button-primary">Check Email via Proxy</button>
                <button id="corsDeleteUser" class="button-danger" disabled>Delete User via Proxy</button>
            </div>

            <div id="corsResult" class="result hidden"></div>

            <p class="info">Note: Using a CORS proxy means your API key will be sent through a third-party service. Only use this option if you're comfortable with that security implication.</p>
        </div>
    </div>

    <div id="help-tab" class="tab-content">
        <div class="card">
            <h2>Help & Troubleshooting</h2>

            <h3>Common Issues</h3>
            <ul>
                <li><strong>"Already Verified" Error:</strong> This happens when an email address remains in Clerk's system after a user account is deleted.</li>
                <li><strong>"Email Already Exists" Error:</strong> The email is already registered with an active user account.</li>
                <li><strong>"Identifier Already Exists" Error:</strong> Similar to "already verified", indicates the email is in Clerk's system.</li>
            </ul>

            <h3>Solutions</h3>
            <ol>
                <li>Check if the email exists in Clerk's system using the "Check Email" tab</li>
                <li>If a user is found, delete it using the "Delete User Account" button</li>
                <li>If no user is found but you're still getting errors, try the "Advanced Fix" tab</li>
                <li>If all else fails, contact Clerk support to purge the email from their system</li>
            </ol>

            <h3>API Key Issues</h3>
            <p>Make sure you're using a valid Clerk Secret API Key (starts with <code>sk_test_</code> or <code>sk_live_</code>).</p>
            <p>You can find this in your Clerk dashboard under API Keys.</p>

            <h4>How to Get Your Clerk API Key:</h4>
            <ol>
                <li>Log in to your Clerk Dashboard at <a href="https://dashboard.clerk.com" target="_blank">dashboard.clerk.com</a></li>
                <li>Select your application</li>
                <li>In the left sidebar, click on "API Keys"</li>
                <li>Copy the "Secret Key" (it starts with <code>sk_test_</code> or <code>sk_live_</code>)</li>
                <li>Paste the entire key into the "Clerk Secret API Key" field in this tool</li>
            </ol>

            <h4>Common API Key Errors:</h4>
            <ul>
                <li><strong>Load failed</strong>: This usually means the API key is invalid or incomplete</li>
                <li><strong>Authentication failed</strong>: The API key is not valid or has been revoked</li>
                <li><strong>API key appears to be truncated</strong>: You need to copy the entire API key, which is quite long</li>
            </ul>

            <div class="info">
                <p><strong>Important:</strong> The API key should be kept secret. This tool runs entirely in your browser and doesn't send your API key anywhere except to Clerk's official API.</p>
            </div>
        </div>
    </div>

    <script>
        // Tab switching
        document.querySelectorAll('.tab').forEach(tab => {
            tab.addEventListener('click', () => {
                // Remove active class from all tabs and content
                document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
                document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));

                // Add active class to clicked tab and corresponding content
                tab.classList.add('active');
                document.getElementById(`${tab.dataset.tab}-tab`).classList.add('active');
            });
        });

        // Check Email Status
        document.getElementById('checkEmail').addEventListener('click', async () => {
            const apiKey = document.getElementById('apiKey').value;
            const email = document.getElementById('email').value;
            const resultDiv = document.getElementById('emailResult');
            const userActionsDiv = document.getElementById('userActions');

            if (!apiKey || !email) {
                showError(resultDiv, 'Please enter both API key and email address');
                return;
            }

            // Show loading state
            const checkButton = document.getElementById('checkEmail');
            const originalText = checkButton.textContent;
            checkButton.disabled = true;
            checkButton.innerHTML = '<span class="loading"></span> Checking...';

            try {
                // Validate API key format
                if (!apiKey.startsWith('sk_test_') && !apiKey.startsWith('sk_live_')) {
                    showError(resultDiv, 'Invalid API key format. It should start with sk_test_ or sk_live_');
                    return;
                }

                // Check if API key is too short (likely truncated)
                if (apiKey.length < 30) {
                    showError(resultDiv, 'API key appears to be truncated or incomplete. A valid Clerk API key is much longer.');
                    return;
                }

                console.log('Checking email with API key: ' + apiKey.substring(0, 10) + '...');

                // Call Clerk API to check if the email exists
                const response = await fetch('https://api.clerk.com/v1/users?email_address=' + encodeURIComponent(email), {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${apiKey}`,
                        'Content-Type': 'application/json'
                    }
                });

                // Check for API key errors
                if (response.status === 401) {
                    showError(resultDiv, 'Authentication failed: Invalid API key. Please check your Clerk Secret API key.');
                    return;
                }

                if (!response.ok) {
                    const errorText = await response.text();
                    let errorMessage = `API error (${response.status})`;

                    try {
                        // Try to parse as JSON for more details
                        const errorJson = JSON.parse(errorText);
                        errorMessage += ': ' + (errorJson.message || errorJson.error || errorText);
                    } catch {
                        // If not JSON, just use the text
                        errorMessage += ': ' + errorText;
                    }

                    showError(resultDiv, errorMessage);
                    return;
                }

                const data = await response.json();

                // Display the result
                resultDiv.classList.remove('hidden');
                resultDiv.textContent = JSON.stringify(data, null, 2);

                // Show user actions if users were found
                if (data && data.data && data.data.length > 0) {
                    userActionsDiv.classList.remove('hidden');
                    // Store the user ID for deletion
                    userActionsDiv.dataset.userId = data.data[0].id;

                    // Show success message
                    showSuccess(resultDiv, 'User found! You can delete this account to resolve the "already verified" issue.');
                } else {
                    userActionsDiv.classList.add('hidden');

                    // Show info message
                    showInfo(resultDiv, 'No user found with this email address. If you\'re still getting "already verified" errors, try the Advanced Fix tab.');
                }
            } catch (error) {
                console.error('Error checking email:', error);
                showError(resultDiv, 'Error checking email: ' + (error.message || 'Network error or CORS issue. Check console for details.'));
            } finally {
                // Restore button state
                checkButton.disabled = false;
                checkButton.textContent = originalText;
            }
        });

        // Delete User
        document.getElementById('deleteUser').addEventListener('click', async () => {
            const apiKey = document.getElementById('apiKey').value;
            const userActionsDiv = document.getElementById('userActions');
            const userId = userActionsDiv.dataset.userId;
            const resultDiv = document.getElementById('emailResult');

            if (!apiKey || !userId) {
                showError(resultDiv, 'Missing API key or user ID');
                return;
            }

            // Confirm deletion
            if (!confirm('Are you sure you want to delete this user account? This action cannot be undone.')) {
                return;
            }

            // Show loading state
            const deleteButton = document.getElementById('deleteUser');
            const originalText = deleteButton.textContent;
            deleteButton.disabled = true;
            deleteButton.innerHTML = '<span class="loading"></span> Deleting...';

            try {
                // Validate API key format
                if (!apiKey.startsWith('sk_test_') && !apiKey.startsWith('sk_live_')) {
                    showError(resultDiv, 'Invalid API key format. It should start with sk_test_ or sk_live_');
                    return;
                }

                // Check if API key is too short (likely truncated)
                if (apiKey.length < 30) {
                    showError(resultDiv, 'API key appears to be truncated or incomplete. A valid Clerk API key is much longer.');
                    return;
                }

                console.log('Deleting user with API key: ' + apiKey.substring(0, 10) + '...');

                // Call Clerk API to delete the user
                const response = await fetch(`https://api.clerk.com/v1/users/${userId}`, {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${apiKey}`,
                        'Content-Type': 'application/json'
                    }
                });

                // Check for API key errors
                if (response.status === 401) {
                    showError(resultDiv, 'Authentication failed: Invalid API key. Please check your Clerk Secret API key.');
                    return;
                }

                if (response.ok) {
                    // Show success message
                    showSuccess(resultDiv, 'User successfully deleted! You should now be able to sign up with this email address.');
                    userActionsDiv.classList.add('hidden');
                } else {
                    let errorMessage = `API error (${response.status})`;

                    try {
                        const errorData = await response.json();
                        errorMessage += ': ' + JSON.stringify(errorData);
                    } catch (e) {
                        const errorText = await response.text();
                        errorMessage += ': ' + errorText;
                    }

                    showError(resultDiv, 'Error deleting user: ' + errorMessage);
                }
            } catch (error) {
                console.error('Error deleting user:', error);
                showError(resultDiv, 'Error deleting user: ' + (error.message || 'Network error or CORS issue. Check console for details.'));
            } finally {
                // Restore button state
                deleteButton.disabled = false;
                deleteButton.textContent = originalText;
            }
        });

        // Advanced Fix
        document.getElementById('fixVerified').addEventListener('click', async () => {
            const apiKey = document.getElementById('apiKey').value;
            const email = document.getElementById('advancedEmail').value;
            const password = document.getElementById('password').value;
            const resultDiv = document.getElementById('advancedResult');

            if (!apiKey || !email || !password) {
                showError(resultDiv, 'Please enter API key, email address, and password');
                return;
            }

            // Show loading state
            const fixButton = document.getElementById('fixVerified');
            const originalText = fixButton.textContent;
            fixButton.disabled = true;
            fixButton.innerHTML = '<span class="loading"></span> Fixing...';

            try {
                // Validate API key format
                if (!apiKey.startsWith('sk_test_') && !apiKey.startsWith('sk_live_')) {
                    showError(resultDiv, 'Invalid API key format. It should start with sk_test_ or sk_live_');
                    return;
                }

                // Check if API key is too short (likely truncated)
                if (apiKey.length < 30) {
                    showError(resultDiv, 'API key appears to be truncated or incomplete. A valid Clerk API key is much longer.');
                    return;
                }

                console.log('Attempting advanced fix with API key: ' + apiKey.substring(0, 10) + '...');

                // Try to create a new user with the email and password
                const response = await fetch('https://api.clerk.com/v1/users', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${apiKey}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email_address: [email],
                        password,
                        skip_password_checks: true,
                        // Note: The correct parameter name is skip_password_requirement (singular)
                        skip_password_requirement: true
                    })
                });

                // Check for API key errors
                if (response.status === 401) {
                    showError(resultDiv, 'Authentication failed: Invalid API key. Please check your Clerk Secret API key.');
                    return;
                }

                let data;
                try {
                    data = await response.json();
                } catch (e) {
                    const errorText = await response.text();
                    showError(resultDiv, `API error (${response.status}): ${errorText}`);
                    return;
                }

                // Display the result
                resultDiv.classList.remove('hidden');
                resultDiv.textContent = JSON.stringify(data, null, 2);

                if (response.ok) {
                    showSuccess(resultDiv, 'Successfully created a new user! The "already verified" issue should be resolved.');
                } else {
                    // Check if it's an "already verified" error
                    const isAlreadyVerifiedError =
                        data.errors?.some(e =>
                            e.message?.includes('verified') ||
                            e.message?.includes('already exists')
                        );

                    if (isAlreadyVerifiedError) {
                        showError(resultDiv, 'The email is still marked as "already verified" in Clerk\'s system. You may need to contact Clerk support to completely purge this email from their system.');
                    } else {
                        showError(resultDiv, 'Error creating user: ' + JSON.stringify(data.errors));
                    }
                }
            } catch (error) {
                console.error('Error fixing verified issue:', error);
                showError(resultDiv, 'Error fixing verified issue: ' + (error.message || 'Network error or CORS issue. Check console for details.'));
            } finally {
                // Restore button state
                fixButton.disabled = false;
                fixButton.textContent = originalText;
            }
        });

        // CORS Proxy functionality
        document.getElementById('corsCheckEmail').addEventListener('click', async () => {
            const apiKey = document.getElementById('corsApiKey').value;
            const email = document.getElementById('corsEmail').value;
            const resultDiv = document.getElementById('corsResult');

            if (!apiKey || !email) {
                showError(resultDiv, 'Please enter both API key and email address');
                return;
            }

            // Show loading state
            const checkButton = document.getElementById('corsCheckEmail');
            const originalText = checkButton.textContent;
            checkButton.disabled = true;
            checkButton.innerHTML = '<span class="loading"></span> Checking...';

            try {
                // Use a CORS proxy to access Clerk's API
                const proxyUrl = 'https://cors-anywhere.herokuapp.com/';
                const targetUrl = `https://api.clerk.com/v1/users?email_address=${encodeURIComponent(email)}`;

                const response = await fetch(proxyUrl + targetUrl, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${apiKey}`,
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                });

                if (response.status === 403 && response.statusText.includes('cors-anywhere')) {
                    showError(resultDiv, 'CORS Proxy access denied. You need to request temporary access to the demo server. Click the button below:');

                    const accessButton = document.createElement('button');
                    accessButton.textContent = 'Request Temporary Access';
                    accessButton.className = 'button-primary';
                    accessButton.style.marginTop = '10px';
                    accessButton.onclick = () => {
                        window.open('https://cors-anywhere.herokuapp.com/corsdemo', '_blank');
                    };

                    resultDiv.appendChild(accessButton);
                    return;
                }

                if (!response.ok) {
                    const errorText = await response.text();
                    showError(resultDiv, `API error (${response.status}): ${errorText}`);
                    return;
                }

                const data = await response.json();

                // Display the result
                resultDiv.classList.remove('hidden');
                resultDiv.textContent = JSON.stringify(data, null, 2);

                // Enable delete button if users were found
                const deleteButton = document.getElementById('corsDeleteUser');
                if (data && data.data && data.data.length > 0) {
                    deleteButton.disabled = false;
                    deleteButton.dataset.userId = data.data[0].id;

                    showSuccess(resultDiv, 'User found! You can delete this account using the "Delete User via Proxy" button.');
                } else {
                    deleteButton.disabled = true;
                    delete deleteButton.dataset.userId;

                    showInfo(resultDiv, 'No user found with this email address.');
                }
            } catch (error) {
                console.error('Error using CORS proxy:', error);
                showError(resultDiv, 'Error using CORS proxy: ' + error.message);
            } finally {
                // Restore button state
                checkButton.disabled = false;
                checkButton.textContent = originalText;
            }
        });

        document.getElementById('corsDeleteUser').addEventListener('click', async () => {
            const apiKey = document.getElementById('corsApiKey').value;
            const resultDiv = document.getElementById('corsResult');
            const deleteButton = document.getElementById('corsDeleteUser');
            const userId = deleteButton.dataset.userId;

            if (!apiKey || !userId) {
                showError(resultDiv, 'Missing API key or user ID');
                return;
            }

            // Confirm deletion
            if (!confirm('Are you sure you want to delete this user account? This action cannot be undone.')) {
                return;
            }

            // Show loading state
            const originalText = deleteButton.textContent;
            deleteButton.disabled = true;
            deleteButton.innerHTML = '<span class="loading"></span> Deleting...';

            try {
                // Use a CORS proxy to access Clerk's API
                const proxyUrl = 'https://cors-anywhere.herokuapp.com/';
                const targetUrl = `https://api.clerk.com/v1/users/${userId}`;

                const response = await fetch(proxyUrl + targetUrl, {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${apiKey}`,
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                });

                if (response.status === 403 && response.statusText.includes('cors-anywhere')) {
                    showError(resultDiv, 'CORS Proxy access denied. You need to request temporary access to the demo server. Click the button below:');

                    const accessButton = document.createElement('button');
                    accessButton.textContent = 'Request Temporary Access';
                    accessButton.className = 'button-primary';
                    accessButton.style.marginTop = '10px';
                    accessButton.onclick = () => {
                        window.open('https://cors-anywhere.herokuapp.com/corsdemo', '_blank');
                    };

                    resultDiv.appendChild(accessButton);
                    return;
                }

                if (response.ok) {
                    showSuccess(resultDiv, 'User successfully deleted! You should now be able to sign up with this email address.');
                    deleteButton.disabled = true;
                    delete deleteButton.dataset.userId;
                } else {
                    let errorMessage = `API error (${response.status})`;

                    try {
                        const errorData = await response.json();
                        errorMessage += ': ' + JSON.stringify(errorData);
                    } catch (e) {
                        const errorText = await response.text();
                        errorMessage += ': ' + errorText;
                    }

                    showError(resultDiv, 'Error deleting user: ' + errorMessage);
                }
            } catch (error) {
                console.error('Error using CORS proxy:', error);
                showError(resultDiv, 'Error using CORS proxy: ' + error.message);
            } finally {
                // Restore button state
                deleteButton.disabled = false;
                deleteButton.textContent = originalText;
            }
        });

        // Helper functions
        function showError(element, message) {
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error';
            errorDiv.textContent = message;

            // Insert at the top of the element
            element.classList.remove('hidden');
            element.innerHTML = '';
            element.appendChild(errorDiv);
        }

        function showSuccess(element, message) {
            const successDiv = document.createElement('div');
            successDiv.className = 'success';
            successDiv.textContent = message;

            // Insert at the top of the element
            element.classList.remove('hidden');
            element.innerHTML = '';
            element.appendChild(successDiv);
        }

        function showInfo(element, message) {
            const infoDiv = document.createElement('div');
            infoDiv.className = 'info';
            infoDiv.textContent = message;

            // Insert at the top of the element
            element.classList.remove('hidden');
            element.innerHTML = '';
            element.appendChild(infoDiv);
        }
    </script>
</body>
</html>

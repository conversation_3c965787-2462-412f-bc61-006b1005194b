import { NextResponse } from 'next/server';

export async function GET() {
  // Get the first few characters of the keys for security
  const publishableKey = process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY || '';
  const secretKey = process.env.CLERK_SECRET_KEY || '';
  
  return NextResponse.json({
    publishableKey: publishableKey.substring(0, 10) + '...',
    publishableKeyLength: publishableKey.length,
    secretKey: secretKey.substring(0, 10) + '...',
    secretKeyLength: secretKey.length,
    // Check if keys have the correct prefix
    isPublishableKeyValid: publishableKey.startsWith('pk_test_'),
    isSecretKeyValid: secretKey.startsWith('sk_test_'),
  });
}

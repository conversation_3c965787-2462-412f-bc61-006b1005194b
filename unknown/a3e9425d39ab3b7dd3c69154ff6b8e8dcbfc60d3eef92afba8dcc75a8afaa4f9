import { NextResponse } from 'next/server';

export async function GET() {
  try {
    // Check current environment variables
    const envVars = {
      NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY: process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY || 'missing',
      CLERK_SECRET_KEY: process.env.CLERK_SECRET_KEY || 'missing',
      NODE_ENV: process.env.NODE_ENV || 'missing',
    };

    // Return environment status
    return NextResponse.json({
      success: true,
      message: 'Environment variables checked',
      envVars: {
        NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY: envVars.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY === 'missing' ? 'missing' : 'present',
        CLERK_SECRET_KEY: envVars.CLERK_SECRET_KEY === 'missing' ? 'missing' : 'present',
        NODE_ENV: envVars.NODE_ENV,
      },
    });
  } catch (error) {
    return NextResponse.json({
      success: false,
      message: 'Error checking environment variables',
      error: String(error),
    }, { status: 500 });
  }
}

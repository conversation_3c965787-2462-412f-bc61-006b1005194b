"use client"

import { useState, useRef, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/Card"
import { Slider } from "@/components/ui/Slider"

interface ImageSet {
  id: string
  name: string
  originalImage: string
  blurredImage: string
  prompt: string
}

interface BasicImageCompareProps {
  imageSets: ImageSet[]
  defaultImageSet?: string
}

export function BasicImageCompare({
  imageSets,
  defaultImageSet
}: BasicImageCompareProps) {
  const [sliderValue, setSliderValue] = useState(50)
  const [isDragging, setIsDragging] = useState(false)
  // Use a safe default value to avoid type errors
  const defaultId = imageSets && imageSets.length > 0 && imageSets[0] ? imageSets[0].id : ''
  const [activeImageSet, setActiveImageSet] = useState<string>(defaultImageSet || defaultId)
  const containerRef = useRef<HTMLDivElement>(null)

  const currentImageSet = imageSets.find(set => set.id === activeImageSet) || (imageSets && imageSets.length > 0 && imageSets[0] ? imageSets[0] : {
    id: '',
    name: '',
    originalImage: '',
    blurredImage: '',
    prompt: ''
  })

  const handleSliderChange = (value: number[]) => {
    // Add a check to ensure value[0] exists and is a number
    if (value && value.length > 0 && typeof value[0] === 'number') {
      setSliderValue(value[0])
    }
  }

  const handleMouseDown = (e: React.MouseEvent) => {
    if (!containerRef.current) return
    setIsDragging(true)

    const rect = containerRef.current.getBoundingClientRect()
    const x = e.clientX - rect.left
    const newValue = (x / rect.width) * 100
    setSliderValue(Math.max(0, Math.min(100, newValue)))
  }

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!isDragging || !containerRef.current) return

    const rect = containerRef.current.getBoundingClientRect()
    const x = e.clientX - rect.left
    const newValue = (x / rect.width) * 100
    setSliderValue(Math.max(0, Math.min(100, newValue)))
  }

  const handleMouseUp = () => {
    setIsDragging(false)
  }

  useEffect(() => {
    const handleGlobalMouseUp = () => {
      setIsDragging(false)
    }

    const handleGlobalMouseMove = (e: MouseEvent) => {
      if (isDragging && containerRef.current) {
        const rect = containerRef.current.getBoundingClientRect()
        const x = e.clientX - rect.left
        const newValue = (x / rect.width) * 100
        setSliderValue(Math.max(0, Math.min(100, newValue)))
      }
    }

    window.addEventListener('mouseup', handleGlobalMouseUp)
    window.addEventListener('mousemove', handleGlobalMouseMove)

    return () => {
      window.removeEventListener('mouseup', handleGlobalMouseUp)
      window.removeEventListener('mousemove', handleGlobalMouseMove)
    }
  }, [isDragging])

  return (
    <div className="mx-auto mt-16 max-w-4xl">
      <Card className="overflow-hidden border-navy-light bg-navy shadow-xl">
        <CardContent className="p-0">
          <div
            ref={containerRef}
            className="relative aspect-video w-full cursor-ew-resize"
            onMouseDown={handleMouseDown}
            onMouseMove={handleMouseMove}
            onMouseUp={handleMouseUp}
          >
            {/* Original image */}
            <img
              src={currentImageSet.originalImage}
              alt="Original"
              className="absolute top-0 left-0 w-full h-full object-cover"
            />

            {/* Blurred image with clip-path */}
            <img
              src={currentImageSet.blurredImage}
              alt="Blurred"
              className="absolute top-0 left-0 w-full h-full object-cover"
              style={{
                clipPath: `polygon(0 0, ${sliderValue}% 0, ${sliderValue}% 100%, 0 100%)`
              }}
            />

            {/* Divider */}
            <div
              className="absolute top-0 bottom-0 w-0.5 bg-white"
              style={{
                left: `${sliderValue}%`,
                boxShadow: '0 0 10px rgba(0,0,0,0.3)'
              }}
            >
              {/* Handle */}
              <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-10 h-10 rounded-full bg-green-400 border-4 border-white shadow-lg flex items-center justify-center">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M8 5L3 10L8 15M16 5L21 10L16 15"
                    stroke="#0a192f"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              </div>
            </div>
          </div>

          <div className="bg-navy-light p-6">
            {/* Image Set Selection */}
            <div className="mb-6">
              <h3 className="text-center text-sm uppercase tracking-wider text-gray-400 mb-3">Select Image Type</h3>
              <div className="flex justify-center flex-wrap gap-3">
                {imageSets.map((set) => (
                  <button
                    key={set.id}
                    onClick={() => setActiveImageSet(set.id)}
                    className={`px-5 py-2 rounded-md transition-all duration-200 flex items-center ${activeImageSet === set.id
                      ? 'bg-green-400 text-navy font-medium shadow-lg shadow-green-400/20 scale-105'
                      : 'bg-navy-dark text-gray-300 hover:bg-navy-light/80 hover:scale-105'
                      }`}
                  >
                    <div className={`w-2 h-2 rounded-full mr-2 ${activeImageSet === set.id ? 'bg-navy' : 'bg-gray-500'}`}></div>
                    {set.name}
                  </button>
                ))}
              </div>
            </div>

            {/* Prompt Display */}
            <div className="mb-6 text-center">
              <div className="inline-flex items-center px-5 py-3 bg-navy-dark/70 rounded-lg border border-navy-light/30 shadow-lg">
                <div className="mr-3 flex-shrink-0 w-6 h-6 rounded-full bg-green-400 flex items-center justify-center text-navy text-xs font-bold shadow-sm">AI</div>
                <div className="flex flex-col items-start">
                  <span className="text-xs text-gray-400 mb-0.5">PROMPT USED</span>
                  <span className="text-sm text-gray-200 font-mono font-medium">"{currentImageSet.prompt}"</span>
                </div>
              </div>
            </div>

            <div className="bg-navy-dark/50 p-4 rounded-lg border border-navy-light/30 shadow-inner">
              <Slider
                value={[sliderValue]}
                onValueChange={handleSliderChange}
                min={0}
                max={100}
                step={1}
                className="py-4"
              />
              <div className="mt-3 flex justify-between text-sm">
                <div className="flex items-center">
                  <div className="w-3 h-3 rounded-full bg-green-400 mr-2"></div>
                  <span className="text-gray-200 font-medium">Protected Content</span>
                </div>
                <div className="flex items-center">
                  <span className="text-gray-200 font-medium">Original Content</span>
                  <div className="w-3 h-3 rounded-full bg-gray-400 ml-2"></div>
                </div>
              </div>
              <div className="mt-2 text-center text-xs text-gray-400">
                Drag the slider or use the handle to compare images
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="mt-8 flex flex-wrap justify-center gap-4 text-center">
        <div className="min-w-[180px] rounded-lg bg-navy-light p-4">
          <div className="text-2xl font-bold text-green-400">99.8%</div>
          <div className="text-sm text-gray-300">Detection Accuracy</div>
        </div>
        <div className="min-w-[180px] rounded-lg bg-navy-light p-4">
          <div className="text-2xl font-bold text-green-400">&lt;50ms</div>
          <div className="text-sm text-gray-300">Processing Time</div>
        </div>
        <div className="min-w-[180px] rounded-lg bg-navy-light p-4">
          <div className="text-2xl font-bold text-green-400">GDPR</div>
          <div className="text-sm text-gray-300">Compliant</div>
        </div>
      </div>
    </div>
  )
}

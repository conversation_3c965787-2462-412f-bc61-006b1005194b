# Clerk-Supabase Official Integration Guide

This guide explains how to integrate Clerk authentication with Supa<PERSON> using the official approach recommended by <PERSON>.

## Understanding Client-Side vs Server-Side Rendering

### Client-Side Rendering (CSR)
- Code runs in the browser after the page loads
- Uses React hooks like `useState`, `useEffect`, `useSession`
- Components are marked with `'use client'` directive in Next.js
- Data fetching happens after the page is loaded in the browser
- Good for interactive components that need to respond to user actions

### Server-Side Rendering (SSR)
- Code runs on the server before sending HTML to the browser
- Uses server functions like `auth()` from `@clerk/nextjs/server`
- Components don't need the `'use client'` directive in Next.js
- Data is fetched on the server before the page is sent to the browser
- Good for initial page load and SEO

## Setup Steps

### 1. Set up Clerk as a Supabase Third-Party Auth Provider

1. In the Clerk Dashboard, navigate to the Supabase integration setup:
   - Go to "Integrations" in the sidebar
   - Find and select "Supabase"
   - Click "Activate" or "Configure"
   - Copy the Clerk domain (it should look like `clerk.your-app.clerk.accounts.dev`)

2. In the Supabase Dashboard, add Clerk as a provider:
   - Go to "Authentication" > "Providers"
   - Click "Add Provider" and select "Clerk"
   - Paste the Clerk domain you copied from the Clerk dashboard
   - Save the changes

### 2. Update Your Supabase Client Code

#### Client-Side Integration

```typescript
// src/utils/supabase/client.ts
import { createClient } from '@supabase/supabase-js';
import { useSession } from '@clerk/nextjs';
import { useMemo } from 'react';

export function useSupabaseClient() {
  const { session } = useSession();

  const supabaseClient = useMemo(() => {
    return createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        async accessToken() {
          return session?.getToken() ?? null;
        },
      }
    );
  }, [session]);

  return supabaseClient;
}
```

#### Server-Side Integration

```typescript
// src/utils/supabase/server.ts
import { createClient } from '@supabase/supabase-js';
import { auth } from '@clerk/nextjs/server';

export function createServerSupabaseClient() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      async accessToken() {
        return auth().getToken();
      },
    }
  );
}
```

### 3. Update Your RLS Policies

```sql
-- Update the RLS policy for users table
DROP POLICY IF EXISTS "Users can view their own data" ON public.users;
CREATE POLICY "Users can view their own data"
  ON public.users
  FOR SELECT
  TO authenticated
  USING (
    ((auth.jwt()->>'sub') = (id)::text)
  );
```

## Usage Examples

### Client-Side Example

```tsx
'use client';

import { useEffect, useState } from 'react';
import { useUser } from '@clerk/nextjs';
import { useSupabaseClient } from '@/utils/supabase/client';

export function UserProfile() {
  const [userData, setUserData] = useState(null);
  const { user } = useUser();
  const supabase = useSupabaseClient();
  
  useEffect(() => {
    if (!user) return;
    
    async function loadUserData() {
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('id', user.id)
        .single();
      
      if (!error) {
        setUserData(data);
      }
    }
    
    loadUserData();
  }, [user, supabase]);
  
  // Render user data
}
```

### Server-Side Example

```tsx
// app/profile/page.tsx
import { createServerSupabaseClient } from '@/utils/supabase/server';
import { auth, currentUser } from '@clerk/nextjs/server';

export default async function ProfilePage() {
  const user = await currentUser();
  const supabase = createServerSupabaseClient();
  
  if (!user) {
    return <div>Not signed in</div>;
  }
  
  const { data, error } = await supabase
    .from('users')
    .select('*')
    .eq('id', user.id)
    .single();
  
  // Render user data
}
```

## Troubleshooting

### Common Issues

1. **"refresh_token_not_found" error**: Make sure you've set up Clerk as a Supabase third-party auth provider.

2. **RLS policies not working**: Ensure your RLS policies use `auth.jwt()->>'sub'` to get the user ID.

3. **"Invalid JWT" error**: Verify the Clerk domain is correctly set up in Supabase.

### Testing the Integration

You can test if the integration is working by:

1. Signing in to your application
2. Checking if you can access your data with RLS enabled
3. Verifying that you can't access other users' data

## References

- [Clerk Supabase Integration Documentation](https://clerk.com/docs/integrations/databases/supabase)
- [Supabase Third-Party Auth Providers Documentation](https://supabase.com/docs/guides/auth/third-party-auth)

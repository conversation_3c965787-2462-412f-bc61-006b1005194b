{"[typescript]": {"editor.defaultFormatter": "denoland.vscode-deno"}, "deno.enablePaths": ["supabase/functions"], "deno.lint": true, "deno.unstable": ["bare-node-builtins", "byonm", "sloppy-imports", "unsafe-proto", "webgpu", "broadcast-channel", "worker-options", "cron", "kv", "ffi", "fs", "http", "net"], "editor.codeActionsOnSave": ["source.addMissingImports", "source.fixAll.eslint"], "editor.detectIndentation": false, "editor.formatOnSave": false, "editor.tabSize": 2, "eslint.format.enable": true, "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact", "vue", "html", "markdown", "json", "jsonc", "yaml", "toml", "xml", "gql", "graphql", "astro", "css", "less", "scss", "pcss", "postcss", "github-actions-workflow"], "i18n-ally.keystyle": "nested", "i18n-ally.localesPaths": ["src/locales"], "prettier.enable": false, "search.exclude": {"package-lock.json": true}, "testing.automaticallyOpenTestResults": "neverOpen", "typescript.enablePromptUseWorkspaceTsdk": true, "typescript.preferences.autoImportFileExcludePatterns": ["next/router.d.ts", "next/dist/client/router.d.ts", "lucide-react"], "typescript.preferences.preferTypeOnlyAutoImports": true, "typescript.tsdk": "node_modules/typescript/lib"}
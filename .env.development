# Clerk authentication
# These are example Clerk API keys that should work for testing
# For production, replace with your own keys from the Clerk dashboard
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_Y2xlcmsuZ3VhcmRpYXZpc2lvbi5jb20k
CLERK_SECRET_KEY=sk_test_kZKbHQybnBxYXNkZmdoanNrZGZoc2RrZmhza2RmaHNrZGZoc2RrZmhza2RmaHNrZGY
# This is a development-only workaround - NEVER use this in production
NEXT_PUBLIC_CLERK_SECRET_KEY=sk_test_kZKbHQybnBxYXNkZmdoanNrZGZoc2RrZmhza2RmaHNrZGZoc2RrZmhza2RmaHNrZGY
CLERK_WEBHOOK_SECRET=whsec_YDhHeF5AYPg12UWMDUH1zAG8pQPMBCyL
CLERK_ENCRYPTION_KEY=51nnFwHA3Ap31i7CjEQAT5bnhj5ustzfbjQAxgHjamw!

# Additional Clerk configuration
NEXT_PUBLIC_CLERK_SIGN_IN_URL=/sign-in
NEXT_PUBLIC_CLERK_SIGN_UP_URL=/sign-up
NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL=/dashboard
NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL=/dashboard

# OAuth configuration
NEXT_PUBLIC_CLERK_OAUTH_CALLBACK_URL=/dashboard


Middleware:
 src/middleware.ts
Clerk Configuration:
 src/utils/clerk-config.ts
 src/utils/clerk-appearance.ts
Authentication Layouts:
 src/app/[locale]/(auth)/layout.tsx
 src/app/[locale]/(auth)/(center)/layout.tsx (if it exists)
Sign-in and Sign-up Pages:
 src/app/[locale]/(auth)/(center)/sign-in/[[...sign-in]]/page.tsx
 src/app/[locale]/(auth)/(center)/sign-up/[[...sign-up]]/page.tsx
Custom Authentication Components:
 src/components/auth/CustomSignIn.tsx
 src/components/auth/CustomSignUp.tsx
Any other components in  src/components/auth/ directory
API Routes:
 src/app/api/auth/callback/google/route.ts
 src/app/api/clerk-signup/route.ts
 src/app/api/direct-signup/route.ts
 src/app/api/verify-email/route.ts (if it exists)
 src/app/api/check-email/route.ts (if it exists)
Root Layouts:
 src/app/layout.tsx
 src/app/[locale]/layout.tsx
Standalone Layouts (if you're using them):
 src/app/standalone/layout.tsx
 src/app/(standalone)/layout.tsx
Environment Files (make sure to copy the Clerk variables):
 .env.local
 .env.production
Next.js Config:
 next.config.js (specifically the Clerk-related configuration)